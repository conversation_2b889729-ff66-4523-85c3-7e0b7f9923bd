#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化的俄罗斯方块式装箱算法模块
用于C类图片的排列，实现真实的俄罗斯方块效果，模拟物理碰撞，最大化水平利用率

特性：
1. 多线程并行处理，提高性能
2. 空间分区优化，减少碰撞检测开销
3. 智能旋转决策，提高利用率
4. 性能监控和报告
"""

import logging
import time
import random
import threading
import concurrent.futures
import sys
from typing import Dict, List, Tuple, Any, Optional

# 导入抽象基类
from core.abstract_packer import AbstractPacker

# 尝试导入性能优化模块
try:
    from utils.parallel_manager import ParallelManager
    PERFORMANCE_MODULES_AVAILABLE = True
except ImportError:
    PERFORMANCE_MODULES_AVAILABLE = False

log = logging.getLogger(__name__)

class TetrisPacker(AbstractPacker):
    """
    优化的俄罗斯方块式装箱算法实现，用于C类图片的排列
    特点：
    1. 模拟真实的俄罗斯方块下落和碰撞过程
    2. 寻找水平最低点，优化图片放置位置
    3. 确保图片有足够的支撑，避免悬空
    4. 智能旋转决策，提高水平利用率
    5. 填充现有行中的空隙，减少垂直高度
    6. 重分配图片位置，优化整体布局
    7. 扁平化布局，尽量减少画布高度
    """
    def __init__(self, container_width: int, image_spacing: int = 0, max_height: int = 0, log_signal=None, is_last_canvas=False, canvas_truncation_handler=None):
        """
        初始化俄罗斯方块式装箱器

        Args:
            container_width: 容器宽度（像素）
            image_spacing: 图片间距（像素）
            max_height: 最大高度限制（0表示无限制）
            log_signal: 日志信号，用于向UI发送日志
            is_last_canvas: 是否是最后一个画布，用于决定是否在处理完所有图片后截断画布
            canvas_truncation_handler: 画布截断处理器，用于处理画布截断逻辑
        """
        # 调用父类初始化方法
        super().__init__(container_width, image_spacing, max_height)

        # 设置最大高度限制
        self.max_height = max_height  # 最大高度限制
        self.current_max_height = 0   # 当前最大高度
        self.log_signal = log_signal  # 日志信号
        self.is_last_canvas = is_last_canvas  # 是否是最后一个画布
        self.canvas_is_full = False   # 画布是否已满标志
        self.is_all_images_arranged = False  # 是否所有图片都已排列完成
        self.bottom_image_moved = False  # 底部图片是否已移动标志
        self.canvas_truncation_handler = canvas_truncation_handler  # 画布截断处理器

        # 存储画布的占用情况，使用网格表示
        # 每个单元格存储一个布尔值，表示该位置是否被占用
        self.grid = {}  # 使用字典实现稀疏矩阵，键为(x, y)坐标，值为True表示占用

        # 存储每一行的高度信息
        self.row_heights = {}  # 键为y坐标，值为该行的高度

        # 存储每一行的右边界
        self.row_right_edges = {}  # 键为y坐标，值为该行的右边界x坐标

        # 算法参数 - 经过优化测试的最佳参数，可通过image_data传入覆盖
        self.horizontal_priority = 75  # 水平优先级（百分比），越高越优先考虑水平利用率
        self.gap_filling_priority = 90  # 空隙填充优先级（百分比），越高越优先填充现有行空隙
        self.rotation_priority = 65  # 旋转优先级（百分比），越高越倾向于旋转图片

        # 性能优化相关
        self.use_grid_optimization = True  # 是否使用网格优化
        self.grid_cell_size = 10  # 网格单元格大小，用于优化碰撞检测
        self.grid_cells = {}  # 网格单元格，键为(grid_x, grid_y)，值为该单元格中的图片列表

        # 并行处理相关
        self.use_parallel_processing = PERFORMANCE_MODULES_AVAILABLE  # 是否使用并行处理
        self._lock = threading.Lock() if threading and self.use_parallel_processing else None  # 线程锁
        self.thread_pool = None  # 线程池

        # 创建并行管理器和性能监控器
        if self.use_parallel_processing:
            try:
                self.parallel_manager = ParallelManager()

                # 创建线程池
                self.thread_pool = self.parallel_manager.create_thread_pool(
                    "tetris_packer", ParallelManager.TASK_TYPE_CPU)

                log.info("并行处理已启用，使用线程池进行优化")
            except Exception as e:
                log.warning(f"初始化并行处理失败: {str(e)}，将使用单线程模式")
                self.use_parallel_processing = False

        # 日志相关设置
        self.log_enabled = True
        self.last_log_time = 0
        self.log_interval = 0.5  # 日志输出间隔（秒）

        # 性能监控
        self.placement_count = 0
        self.placement_time = 0
        self.gap_fill_count = 0
        self.gap_fill_time = 0
        self.drop_simulation_count = 0
        self.drop_simulation_time = 0

    def _try_fill_row(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        尝试找到能够填满或接近填满一行的位置

        Args:
            width: 图片宽度（包含间距）
            height: 图片高度（包含间距）
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 如果没有已放置的图片，从左上角开始放置
        if not self.placed_images:
            return 0, 0, True

        # 获取当前所有行的信息
        rows_info = {}
        for img in self.placed_images:
            y = img['y']
            img_right = img['x'] + img['width']
            img_bottom = y + img['height']

            # 更新行信息
            if y not in rows_info:
                rows_info[y] = {'right_edge': 0, 'height': 0, 'bottom': 0, 'covered_width': 0}

            rows_info[y]['right_edge'] = max(rows_info[y]['right_edge'], img_right)
            rows_info[y]['height'] = max(rows_info[y]['height'], img['height'])
            rows_info[y]['bottom'] = max(rows_info[y]['bottom'], img_bottom)

            # 计算行覆盖宽度
            row_ranges = []
            for img2 in self.placed_images:
                if img2['y'] == y:
                    row_ranges.append((img2['x'], img2['x'] + img2['width']))

            # 合并重叠的范围
            if row_ranges:
                row_ranges.sort()
                merged = []
                for start, end in row_ranges:
                    if not merged or start > merged[-1][1]:
                        merged.append((start, end))
                    else:
                        merged[-1] = (merged[-1][0], max(merged[-1][1], end))

                # 计算行覆盖宽度
                covered_width = sum(end - start for start, end in merged)
                rows_info[y]['covered_width'] = covered_width

        # 按y坐标排序行
        sorted_rows = sorted(rows_info.keys())

        # 尝试找到能够填满或接近填满一行的位置
        best_x = 0
        best_y = 0
        best_score = -float('inf')
        best_success = False

        for y in sorted_rows:
            row_info = rows_info[y]
            right_edge = row_info['right_edge']
            covered_width = row_info['covered_width']

            # 计算当前行利用率
            current_row_util = covered_width / self.container_width

            # 计算放置新图片后的行利用率
            new_row_util = (covered_width + width) / self.container_width

            # 如果放置后行利用率超过100%，跳过
            if new_row_util > 1.0:
                continue

            # 尝试在行右侧放置
            x = right_edge

            # 检查是否可以放置
            if x + width <= self.container_width and not self._check_collision(x, y, width, height):
                # 计算评分 - 优先考虑能填满或接近填满一行的位置
                score = new_row_util * 100

                # 如果能填满或接近填满一行，给予额外奖励
                if new_row_util >= 0.95:
                    score += 50
                elif new_row_util >= 0.85:
                    score += 30

                # 更新最佳位置
                if score > best_score:
                    best_score = score
                    best_x = x
                    best_y = y
                    best_success = True

        # 如果找到合适位置，返回
        if best_success:
            return best_x, best_y, True

        # 如果无法在现有行找到合适位置，尝试创建新行
        if self.placed_images:
            # 找到最低的行底部
            max_bottom = 0
            for y, info in rows_info.items():
                bottom = info['bottom']
                max_bottom = max(max_bottom, bottom)

            # 在最低行底部创建新行
            new_row_y = max_bottom

            # 检查是否可以放置
            if not self._check_collision(0, new_row_y, width, height):
                return 0, new_row_y, True

        # 如果无法放置，返回失败
        return 0, 0, False

    def find_position(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        为图片找到合适的位置，使用优化的俄罗斯方块算法
        优化水平利用率，减少水平空隙

        注意：width表示长边，height表示短边，默认图片为横向放置
        如果图片已旋转，则width和height已交换

        Args:
            width: 图片宽度（长边，如果已旋转则为短边）
            height: 图片高度（短边，如果已旋转则为长边）
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 性能监控开始
        start_time = time.time()

        # 重置状态标志，避免上次执行状态影响当前执行
        self.bottom_image_moved = False

        # 检查是否超过最大高度限制
        # 如果当前最大高度已经接近或超过最大高度限制，直接返回失败
        if self.max_height > 0:
            current_max_height = 0
            if self.placed_images:
                for img in self.placed_images:
                    current_max_height = max(current_max_height, img['y'] + img['height'])

            # 更新当前最大高度
            self.current_max_height = current_max_height

            # 获取图片类型
            image_class = image_data.get('image_class', '') if image_data else ''

            # 检查底部是否只有一个图片
            is_single_bottom, bottom_image, bottom_image_class = self.is_single_image_at_bottom()

            # 如果底部只有一个B类或C类图片，进行智能判断
            if is_single_bottom and bottom_image_class in ['B', 'C']:
                # 获取底部图片宽度
                bottom_image_width = bottom_image['width']

                # 如果当前图片与底部单图片能形成完整B类图片组（宽度之和接近画布宽度）
                if bottom_image_class == 'B' and image_class == 'B':
                    combined_width = bottom_image_width + width
                    if combined_width >= self.container_width * 0.9:
                        if self.log_enabled:
                            log.info(f"当前图片与底部单B类图片可以形成完整B类图片组，宽度之和 {combined_width}px 接近画布宽度 {self.container_width}px")
                        # 不设置画布已满标志，允许继续放置
                        pass
                    elif current_max_height + height > self.max_height:
                        if self.log_enabled:
                            log.warning(f"当前高度 {current_max_height}px + 图片高度 {height}px 会超过最大高度限制 {self.max_height}px，但底部有单B类图片，考虑移动")

                        # 设置底部图片已移动标志
                        self.bottom_image_moved = True

                        # 如果有日志信号，发送底部图片移动信息
                        if hasattr(self, 'log_signal') and self.log_signal:
                            self.log_signal.emit(f"底部单B类图片将被移至下一画布")

                        # 设置画布已满标志
                        self.canvas_is_full = True
                        return 0, 0, False
                elif current_max_height + height > self.max_height:
                    if self.log_enabled:
                        log.warning(f"当前高度 {current_max_height}px + 图片高度 {height}px 会超过最大高度限制 {self.max_height}px，底部有单{bottom_image_class}类图片")

                    # 设置底部图片已移动标志
                    self.bottom_image_moved = True

                    # 如果有日志信号，发送底部图片移动信息
                    if hasattr(self, 'log_signal') and self.log_signal:
                        self.log_signal.emit(f"底部单{bottom_image_class}类图片将被移至下一画布")

                    # 设置画布已满标志
                    self.canvas_is_full = True
                    return 0, 0, False

            # 如果是B类图片，检查是否是一行的最后一个图片
            elif image_class == 'B':
                # 获取B类图片组信息
                group_index = image_data.get('group_index', -1)
                in_group_index = image_data.get('in_group_index', -1)
                group_size = image_data.get('group_size', 0)

                # 计算一行可以放置的B类图片数量
                b_width = width
                max_count_in_row = self.container_width // b_width

                # 如果当前组大小小于一行可以放置的数量，更新组大小
                if group_size > 0 and group_size < max_count_in_row:
                    if self.log_enabled:
                        log.warning(f"B类图片组 {group_index} 大小 ({group_size}) 小于一行可以放置的数量 ({max_count_in_row})，将尝试放置完整的一行")
                    # 更新组大小
                    group_size = max_count_in_row
                    image_data['group_size'] = group_size

                # 如果不是一行的最后一个图片，且当前高度加上图片高度会超过最大高度限制
                if group_size > 0 and in_group_index < group_size - 1 and current_max_height + height > self.max_height:
                    if self.log_enabled:
                        log.warning(f"B类图片组 {group_index} 中的图片 {in_group_index+1}/{group_size} 会超过最大高度限制，但不是行的最后一个图片，允许继续放置")
                    # 不设置画布已满标志，允许继续放置
                    pass
                # 如果是一行的最后一个图片，且当前高度加上图片高度会超过最大高度限制
                elif current_max_height + height > self.max_height:
                    # 检查是否是行的最后一个图片
                    is_last_in_row = image_data.get('is_last_in_row', False) or (group_size > 0 and in_group_index == group_size - 1)

                    if is_last_in_row:
                        if self.log_enabled:
                            log.warning(f"当前高度 {current_max_height}px + 图片高度 {height}px 会超过最大高度限制 {self.max_height}px，且是行的最后一个图片，无法放置")

                        # 设置画布已满标志
                        self.canvas_is_full = True

                        # 如果有日志信号，发送画布已满信息
                        if hasattr(self, 'log_signal') and self.log_signal:
                            self.log_signal.emit(f"画布已满：当前高度 {current_max_height}px 接近最大高度限制 {self.max_height}px，需要创建新画布")

                        return 0, 0, False
                    else:
                        # 不是行的最后一个图片，允许继续放置
                        if self.log_enabled:
                            log.warning(f"当前高度 {current_max_height}px + 图片高度 {height}px 会超过最大高度限制 {self.max_height}px，但不是行的最后一个图片，允许继续放置")
                        # 不设置画布已满标志，允许继续放置
                        pass
            # 对于非B类图片，使用原来的逻辑
            elif current_max_height + height > self.max_height:
                if self.log_enabled:
                    log.warning(f"当前高度 {current_max_height}px + 图片高度 {height}px 会超过最大高度限制 {self.max_height}px，无法放置")

                # 设置画布已满标志
                self.canvas_is_full = True

                # 如果有日志信号，发送画布已满信息
                if hasattr(self, 'log_signal') and self.log_signal:
                    self.log_signal.emit(f"画布已满：当前高度 {current_max_height}px 接近最大高度限制 {self.max_height}px，需要创建新画布")

                return 0, 0, False
        self.placement_count += 1

        # 特殊处理超宽图片：如果图片宽度超过容器宽度，尝试旋转
        if width > self.container_width:
            log.warning(f"检测到超宽图片: 宽度(长边) {width}px 超过容器宽度 {self.container_width}px，尝试旋转")
            # 如果旋转后仍然超过容器宽度，则强制缩放
            if height > self.container_width:
                log.warning(f"旋转后仍然超宽: 高度(短边) {height}px 也超过容器宽度 {self.container_width}px，强制缩放")
                # 计算缩放比例，使宽度等于容器宽度的95%
                scale_ratio = (self.container_width * 0.95) / width
                width = int(width * scale_ratio)
                height = int(height * scale_ratio)
                log.info(f"图片已缩放至 {width}x{height}px")

                # 更新图片数据中的尺寸信息
                if image_data:
                    image_data['width_px'] = width
                    image_data['height_px'] = height
                    if 'width_cm' in image_data and 'height_cm' in image_data:
                        image_data['width_cm'] = image_data['width_cm'] * scale_ratio
                        image_data['height_cm'] = image_data['height_cm'] * scale_ratio
            else:
                # 如果旋转后宽度适合容器，则强制旋转
                log.info(f"图片将被旋转90度，新尺寸为 {height}x{width}px (高度x宽度)")
                width, height = height, width  # 旋转后，原来的高度变为宽度，原来的宽度变为高度

                # 更新图片数据中的旋转状态
                if image_data:
                    image_data['need_rotation'] = True

        # 考虑图片间距
        width_with_spacing = width + self.image_spacing
        height_with_spacing = height + self.image_spacing

        # 获取空隙填充优先级
        gap_filling_priority = self.gap_filling_priority / 100.0
        if image_data and 'c_gap_filling_priority' in image_data:
            gap_filling_priority = image_data['c_gap_filling_priority'] / 100.0

        # 获取水平优先级
        horizontal_priority = self.horizontal_priority / 100.0
        if image_data and 'c_horizontal_priority' in image_data:
            horizontal_priority = image_data['c_horizontal_priority'] / 100.0

        # 记录日志
        if self.log_enabled:
            log.info(f"图片尺寸: {width}x{height}, 空隙填充优先级: {gap_filling_priority:.2f}, 水平优先级: {horizontal_priority:.2f}")

        # 根据空隙填充优先级决定是否尝试填充空隙
        if gap_filling_priority > 0.5 and len(self.placed_images) > 0:
            # 性能监控 - 空隙填充开始
            gap_fill_start_time = time.time()

            # 首先尝试填充现有行中的空隙
            gap_fill_result = self._try_fill_gap(width_with_spacing, height_with_spacing, image_data)

            # 性能监控 - 空隙填充结束
            self.gap_fill_time += time.time() - gap_fill_start_time
            self.gap_fill_count += 1

            if gap_fill_result[2]:  # 如果成功填充空隙
                best_x, best_y, _ = gap_fill_result

                # 更新已放置图片信息和网格
                need_rotation = False
                if image_data and 'need_rotation' in image_data:
                    need_rotation = image_data['need_rotation']

                self._update_after_placement(best_x, best_y, width, height, width_with_spacing, height_with_spacing, image_data, need_rotation, is_gap_fill=True)

                # 记录日志
                if self.log_enabled:
                    log.info(f"成功填充空隙: ({best_x}, {best_y})")

                # 性能监控结束
                self.placement_time += time.time() - start_time

                return best_x, best_y, True

        # 尝试找到能够填满或接近填满一行的位置
        row_fill_result = self._try_fill_row(width_with_spacing, height_with_spacing, image_data)
        row_x, row_y, row_success = row_fill_result

        if row_success:
            # 更新已放置图片信息和网格
            need_rotation = False
            if image_data and 'need_rotation' in image_data:
                need_rotation = image_data['need_rotation']

            self._update_after_placement(row_x, row_y, width, height, width_with_spacing, height_with_spacing, image_data, need_rotation)

            # 记录日志
            if self.log_enabled:
                log.info(f"成功填满或接近填满一行: ({row_x}, {row_y})")

            # 性能监控结束
            self.placement_time += time.time() - start_time

            return row_x, row_y, True

        # 如果无法填充空隙或填满行，则考虑水平和垂直两种放置方式
        rotation_result = self.find_best_rotation_and_position(width, height, image_data)
        best_x, best_y, success, need_rotation = rotation_result

        if success:
            # 更新已放置图片信息和网格
            actual_width = width if not need_rotation else height
            actual_height = height if not need_rotation else width
            width_with_spacing = actual_width + self.image_spacing
            height_with_spacing = actual_height + self.image_spacing

            self._update_after_placement(best_x, best_y, actual_width, actual_height, width_with_spacing, height_with_spacing, image_data, need_rotation)

            # 记录日志
            if self.log_enabled:
                log.info(f"成功放置图片: ({best_x}, {best_y}), {'旋转90度' if need_rotation else '不旋转'}")

            # 性能监控结束
            self.placement_time += time.time() - start_time

            return best_x, best_y, True

        # 如果无法放置，返回失败
        # 记录日志
        if self.log_enabled:
            log.info("无法放置图片")

        # 性能监控结束
        self.placement_time += time.time() - start_time

        return 0, 0, False

    def find_best_rotation_and_position(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool, bool]:
        """
        同时考虑水平和垂直两种放置方式，找到最佳的旋转和位置
        优化水平利用率，减少水平空隙

        增强版：更智能地根据周围已放置图片的形状决定是否旋转当前图片

        注意：width表示长边，height表示短边，默认图片为横向放置
        旋转后，width和height将互换：原来的height变为width，原来的width变为height

        使用并行处理同时评估水平和垂直放置方式

        Args:
            width: 图片宽度（长边）
            height: 图片高度（短边）
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool, bool]: (x, y, 是否成功, 是否需要旋转)
        """
        # 分析当前画布状态，动态调整旋转策略
        rotation_priority = self._analyze_rotation_priority(width, height, image_data)

        # 对于超宽图片，增加旋转倾向
        if width > self.container_width * 0.8:  # 如果宽度超过容器宽度的80%
            rotation_priority = max(rotation_priority, 80)  # 提高旋转优先级
            log.info(f"检测到宽图片: 宽度 {width}px 接近容器宽度 {self.container_width}px，提高旋转优先级至 {rotation_priority}%")

        # 获取水平优先级
        horizontal_priority = self.horizontal_priority
        if image_data and 'c_horizontal_priority' in image_data:
            horizontal_priority = image_data['c_horizontal_priority']

        # 记录日志
        if self.log_enabled:
            log.info(f"水平优先级: {horizontal_priority}%, 旋转优先级: {rotation_priority}%")

        # 使用并行处理同时评估水平和垂直放置
        if self.use_parallel_processing and self.thread_pool:
            try:
                # 提交两个任务到线程池
                horizontal_future = self.thread_pool.submit(self.find_best_position, width, height, image_data)
                vertical_future = self.thread_pool.submit(self.find_best_position, height, width, image_data)

                # 获取结果
                horizontal_result = horizontal_future.result()
                vertical_result = vertical_future.result()

                horizontal_x, horizontal_y, horizontal_success = horizontal_result
                vertical_x, vertical_y, vertical_success = vertical_result
            except Exception as e:
                log.warning(f"并行评估放置方式失败: {str(e)}，回退到顺序处理")
                # 回退到顺序处理
                horizontal_result = self.find_best_position(width, height, image_data)
                horizontal_x, horizontal_y, horizontal_success = horizontal_result

                vertical_result = self.find_best_position(height, width, image_data)
                vertical_x, vertical_y, vertical_success = vertical_result
        else:
            # 顺序处理
            # 考虑水平放置（不旋转）
            horizontal_result = self.find_best_position(width, height, image_data)
            horizontal_x, horizontal_y, horizontal_success = horizontal_result

            # 考虑垂直放置（旋转90度）
            vertical_result = self.find_best_position(height, width, image_data)
            vertical_x, vertical_y, vertical_success = vertical_result

        # 决定是否旋转
        need_rotation = False

        # 如果两种方式都成功，选择更优的一种
        if horizontal_success and vertical_success:
            # 计算两种方式的水平利用率
            horizontal_width_ratio = width / self.container_width
            vertical_width_ratio = height / self.container_width

            # 记录日志
            if self.log_enabled:
                log.info(f"水平放置宽度比: {horizontal_width_ratio:.2f}, 垂直放置宽度比: {vertical_width_ratio:.2f}")

            # 计算两种方式的评分
            horizontal_score = self._calculate_position_score(horizontal_x, horizontal_y, width, height)
            vertical_score = self._calculate_position_score(vertical_x, vertical_y, height, width)

            # 计算环境适应度分数
            horizontal_env_score = self._calculate_environment_score(horizontal_x, horizontal_y, width, height)
            vertical_env_score = self._calculate_environment_score(vertical_x, vertical_y, height, width)

            # 综合评分 = 位置评分 * 0.7 + 环境适应度分数 * 0.3
            horizontal_total_score = horizontal_score * 0.7 + horizontal_env_score * 0.3
            vertical_total_score = vertical_score * 0.7 + vertical_env_score * 0.3

            # 记录日志
            if self.log_enabled:
                log.info(f"水平放置评分: {horizontal_score:.2f}, 环境评分: {horizontal_env_score:.2f}, 总分: {horizontal_total_score:.2f}")
                log.info(f"垂直放置评分: {vertical_score:.2f}, 环境评分: {vertical_env_score:.2f}, 总分: {vertical_total_score:.2f}")

            # 检查图片数据中是否有相似图片组合信息
            has_similar_patterns = image_data and image_data.get('has_similar_patterns', False)
            combined_horizontal_util = image_data.get('combined_horizontal_util', 0) if image_data else 0

            # 如果有相似图片可以组合，且组合利用率接近垂直放置评分，优先选择水平放置
            if has_similar_patterns and combined_horizontal_util > 0 and combined_horizontal_util >= vertical_total_score * 0.7:
                need_rotation = False
                # 记录日志
                if self.log_enabled:
                    log.info(f"选择水平放置: 发现相似图片可组合，组合利用率 {combined_horizontal_util:.2f} 接近垂直总分 {vertical_total_score:.2f}")
            # 否则，如果垂直放置的总分明显更高，选择垂直放置
            elif vertical_total_score > horizontal_total_score * (1 + rotation_priority / 200):  # 根据旋转优先级动态调整阈值
                need_rotation = True
                # 记录日志
                if self.log_enabled:
                    log.info(f"选择垂直放置: 垂直总分 {vertical_total_score:.2f} 明显高于水平总分 {horizontal_total_score:.2f}")
            # 如果垂直放置的宽度比更接近1（更好地填充行），选择垂直放置
            elif abs(1.0 - vertical_width_ratio) < abs(1.0 - horizontal_width_ratio) * (0.7 + rotation_priority / 200):
                need_rotation = True
                # 记录日志
                if self.log_enabled:
                    log.info(f"选择垂直放置: 垂直宽度比 {vertical_width_ratio:.2f} 更接近1，比水平宽度比 {horizontal_width_ratio:.2f} 更好地填充行")
            # 考虑画布高度因素 - 如果垂直放置的y坐标明显小于水平放置，优先选择垂直放置
            elif vertical_y < horizontal_y * 0.8 and vertical_total_score >= horizontal_total_score * 0.9:
                need_rotation = True
                # 记录日志
                if self.log_enabled:
                    log.info(f"选择垂直放置: 垂直y坐标 {vertical_y} 明显小于水平y坐标 {horizontal_y}，有助于减少画布高度")
            else:
                # 记录日志
                if self.log_enabled:
                    log.info(f"选择水平放置: 水平总分 {horizontal_total_score:.2f} 与垂直总分 {vertical_total_score:.2f} 相近")

        # 如果只有一种方式成功，选择成功的那种
        elif not horizontal_success and vertical_success:
            need_rotation = True
            # 记录日志
            if self.log_enabled:
                log.info("选择垂直放置: 水平放置失败")
        elif horizontal_success and not vertical_success:
            # 记录日志
            if self.log_enabled:
                log.info("选择水平放置: 垂直放置失败")

        # 返回最终结果
        if need_rotation:
            return vertical_x, vertical_y, vertical_success, True
        else:
            return horizontal_x, horizontal_y, horizontal_success, False

    def _analyze_rotation_priority(self, width: int, height: int, image_data: Dict[str, Any] = None) -> int:
        """
        分析当前画布状态，动态调整旋转优先级
        增强版：考虑底部图片的特殊处理，优先横向放置

        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片相关数据

        Returns:
            int: 调整后的旋转优先级 (0-100)
        """
        # 获取基础旋转优先级
        rotation_priority = self.rotation_priority
        if image_data and 'c_rotation_priority' in image_data:
            rotation_priority = image_data['c_rotation_priority']

        # 如果没有已放置的图片，使用默认优先级
        if not self.placed_images:
            return rotation_priority

        # 分析当前画布的形状特征
        canvas_width = self.container_width
        canvas_height = self.get_max_height()

        # 检查是否是底部图片
        is_bottom_image = False
        if self.placed_images:
            # 计算当前最大高度
            max_height = self.get_max_height()

            # 使用更灵活的阈值
            bottom_threshold = 0.85
            if hasattr(self, 'is_last_canvas') and self.is_last_canvas:  # 如果是最后一个画布，使用更低的阈值
                bottom_threshold = 0.75

            # 如果当前高度接近最大高度限制，认为是底部图片
            if self.max_height > 0 and max_height > self.max_height * bottom_threshold:
                is_bottom_image = True

                # 根据接近程度动态调整减少值
                proximity_ratio = min(1.0, (max_height / self.max_height))
                reduction = int(40 * proximity_ratio)  # 最多减少40

                # 如果是底部图片，优先横向放置（减少旋转倾向）
                rotation_priority = max(0, rotation_priority - reduction)
                if self.log_enabled:
                    log.info(f"检测到底部图片，减少旋转倾向 {reduction}，旋转优先级调整为 {rotation_priority}")

        # 如果不是底部图片且画布高度已经很大，增加旋转优先级以减少高度
        elif canvas_height > canvas_width * 1.5:
            rotation_priority = min(100, rotation_priority + 10)

        # 获取图片类型
        image_class = image_data.get('image_class', '') if image_data else ''

        # 如果是B类图片，优先横向放置（减少旋转倾向）
        if image_class == 'B':
            # 获取B类图片组信息
            group_index = image_data.get('group_index', -1)
            in_group_index = image_data.get('in_group_index', -1)
            group_size = image_data.get('group_size', 0)

            # 根据组信息动态调整减少值
            reduction = 25  # 基础减少值

            # 如果是组中的图片，增加减少值
            if group_size > 0 and in_group_index >= 0:
                # 组中的最后一个图片减少值更大，确保完整排列
                if in_group_index == group_size - 1:
                    reduction = 35
                # 组中的第一个图片减少值也较大，确保开始横向排列
                elif in_group_index == 0:
                    reduction = 30

            rotation_priority = max(0, rotation_priority - reduction)
            if self.log_enabled:
                log.info(f"检测到B类图片 (组:{group_index}, 索引:{in_group_index}/{group_size})，减少旋转倾向 {reduction}，旋转优先级调整为 {rotation_priority}")

        # 分析已放置图片的形状分布
        horizontal_count = 0  # 横向图片数量
        vertical_count = 0    # 纵向图片数量

        for img in self.placed_images:
            img_width = img['width']
            img_height = img['height']
            if img_width > img_height:
                horizontal_count += 1
            else:
                vertical_count += 1

        # 如果横向图片明显多于纵向图片，增加旋转优先级
        if horizontal_count > vertical_count * 2 and not is_bottom_image and image_class != 'B':
            rotation_priority = min(100, rotation_priority + 5)
        # 如果纵向图片明显多于横向图片，减少旋转优先级
        elif vertical_count > horizontal_count * 2:
            rotation_priority = max(0, rotation_priority - 5)

        # 分析当前图片的形状特征
        aspect_ratio = width / height if height > 0 else 1

        # 如果图片非常细长，增加旋转优先级，除非是底部图片或B类图片
        if (aspect_ratio > 3 or aspect_ratio < 0.33) and not is_bottom_image and image_class != 'B':
            rotation_priority = min(100, rotation_priority + 15)

        # 分析底部区域的空间利用情况
        bottom_utilization = self._analyze_bottom_utilization()

        # 如果底部利用率低，增加旋转优先级以提高利用率，除非是底部图片或B类图片
        if bottom_utilization < 0.7 and not is_bottom_image and image_class != 'B':
            rotation_priority = min(100, rotation_priority + int((0.7 - bottom_utilization) * 20))

        return rotation_priority

    def _calculate_environment_score(self, x: int, y: int, width: int, height: int) -> float:
        """
        计算图片在特定位置的环境适应度分数

        Args:
            x: 图片x坐标
            y: 图片y坐标
            width: 图片宽度
            height: 图片高度

        Returns:
            float: 环境适应度分数 (0-100)
        """
        # 如果没有已放置的图片，返回默认分数
        if not self.placed_images:
            return 50.0

        # 计算与周围图片的接触面积
        contact_area = 0
        total_perimeter = 2 * (width + height)

        # 检查四个边的接触情况
        # 上边
        for i in range(x, x + width):
            if self._is_occupied(i, y - 1):
                contact_area += 1

        # 右边
        for i in range(y, y + height):
            if self._is_occupied(x + width, i):
                contact_area += 1

        # 下边
        for i in range(x, x + width):
            if self._is_occupied(i, y + height):
                contact_area += 1

        # 左边
        for i in range(y, y + height):
            if self._is_occupied(x - 1, i):
                contact_area += 1

        # 计算接触比例
        contact_ratio = contact_area / total_perimeter

        # 计算与画布边界的接触
        edge_contact = 0
        if x == 0:  # 左边界
            edge_contact += height
        if x + width == self.container_width:  # 右边界
            edge_contact += height
        if y == 0:  # 上边界
            edge_contact += width

        # 计算边界接触比例
        edge_ratio = edge_contact / total_perimeter

        # 计算填充行的程度
        row_fill_ratio = width / self.container_width

        # 计算高度因素 - 优先选择y坐标小的位置
        height_factor = 1.0 - (y / max(1, self.get_max_height()))

        # 综合评分：
        # - 接触比例 (40%): 与其他图片接触越多越好
        # - 边界接触 (20%): 与画布边界接触越多越好
        # - 行填充 (20%): 填满一行越好
        # - 高度因素 (20%): y坐标越小越好
        score = (
            contact_ratio * 40 +
            edge_ratio * 20 +
            row_fill_ratio * 20 +
            height_factor * 20
        )

        return score

    def _analyze_bottom_utilization(self) -> float:
        """
        分析画布底部区域的空间利用率

        Returns:
            float: 底部区域利用率 (0-1)
        """
        if not self.placed_images:
            return 1.0

        # 获取当前最大高度
        max_height = self.get_max_height()
        if max_height <= 0:
            return 1.0

        # 定义底部区域 - 最大高度的下20%
        bottom_start = int(max_height * 0.8)
        bottom_end = max_height

        # 计算底部区域的总面积
        bottom_area = self.container_width * (bottom_end - bottom_start)
        if bottom_area <= 0:
            return 1.0

        # 计算底部区域已占用的面积
        occupied_area = 0
        for img in self.placed_images:
            img_top = img['y']
            img_bottom = img['y'] + img['height']
            img_width = img['width']

            # 如果图片与底部区域有交集
            if img_bottom > bottom_start and img_top < bottom_end:
                # 计算交集区域
                intersection_top = max(img_top, bottom_start)
                intersection_bottom = min(img_bottom, bottom_end)
                intersection_height = intersection_bottom - intersection_top

                # 累加交集面积
                occupied_area += img_width * intersection_height

        # 计算利用率
        utilization = min(1.0, occupied_area / bottom_area)

        return utilization

    def _calculate_position_score(self, x: int, y: int, width: int, height: int) -> float:
        """
        计算位置评分，主要考虑水平利用率和行填充效果
        优化版本：更全面地评估位置质量，考虑更多因素

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度
            height: 图片高度

        Returns:
            float: 位置评分，越高越好
        """
        # 计算该行的当前利用率和放置后的利用率
        row_ranges = []
        for img in self.placed_images:
            if img['y'] <= y < img['y'] + img['height']:
                row_ranges.append((img['x'], img['x'] + img['width']))

        # 合并重叠的范围
        if row_ranges:
            row_ranges.sort()
            merged = []
            for start, end in row_ranges:
                if not merged or start > merged[-1][1]:
                    merged.append((start, end))
                else:
                    merged[-1] = (merged[-1][0], max(merged[-1][1], end))

            # 计算当前行利用率
            covered_width = sum(end - start for start, end in merged)
            current_row_util = covered_width / self.container_width

            # 添加新图片后的范围
            new_ranges = merged.copy()
            new_ranges.append((x, x + width))
            new_ranges.sort()

            # 合并重叠的范围
            merged = []
            for start, end in new_ranges:
                if not merged or start > merged[-1][1]:
                    merged.append((start, end))
                else:
                    merged[-1] = (merged[-1][0], max(merged[-1][1], end))

            # 计算新的行利用率
            new_covered_width = sum(end - start for start, end in merged)
            new_row_util = new_covered_width / self.container_width

            # 计算利用率提升
            util_improvement = new_row_util - current_row_util
        else:
            # 如果是新行，则利用率就是图片宽度与容器宽度的比值
            current_row_util = 0
            new_row_util = width / self.container_width
            util_improvement = new_row_util

        # 计算水平利用率评分 (0-100)
        horizontal_util_score = new_row_util * 100

        # 计算利用率提升评分 (0-100)
        util_improvement_score = util_improvement * 100

        # 计算高度评分 (负值，因为我们想要最小化高度)
        height_score = -y

        # 计算行填充评分 - 如果能填满或接近填满一行，给予额外奖励
        row_fill_score = 0
        if new_row_util >= 0.95:  # 行利用率达到95%以上
            row_fill_score = 50  # 给予较大的奖励
        elif new_row_util >= 0.85:  # 行利用率达到85%以上
            row_fill_score = 30  # 给予中等奖励

        # 计算空隙填充评分 - 如果能填充现有空隙，给予额外奖励
        gap_fill_score = 0
        for img in self.placed_images:
            # 检查是否在同一行
            if abs(img['y'] - y) < height:
                # 检查是否填充了空隙
                if img['x'] + img['width'] < x < img['x'] + img['width'] + width * 2:
                    gap_fill_score = 20  # 填充空隙加分
                    break

        # 计算边缘接触评分 - 优先选择能与多个已放置图片接触的位置
        edge_contact_score = 0
        contact_count = 0

        # 检查四个边的接触情况
        # 上边
        for i in range(x, x + width):
            if self._is_occupied(i, y - 1):
                contact_count += 1

        # 右边
        for i in range(y, y + height):
            if self._is_occupied(x + width, i):
                contact_count += 1

        # 下边
        for i in range(x, x + width):
            if self._is_occupied(i, y + height):
                contact_count += 1

        # 左边
        for i in range(y, y + height):
            if self._is_occupied(x - 1, i):
                contact_count += 1

        # 计算接触比例
        total_perimeter = 2 * (width + height)
        contact_ratio = contact_count / total_perimeter if total_perimeter > 0 else 0

        # 根据接触比例计算接触分数
        edge_contact_score = contact_ratio * 30  # 最多30分

        # 计算与画布边界的接触
        edge_bonus = 0
        if x == 0:  # 左边界
            edge_bonus += 10
        if x + width == self.container_width:  # 右边界
            edge_bonus += 10
        if y == 0:  # 上边界
            edge_bonus += 15

        # 计算底部区域优先级 - 优先填充底部区域
        bottom_bonus = 0
        max_height = self.get_max_height()
        if max_height > 0 and y > max_height * 0.7:  # 如果在画布底部30%区域
            bottom_bonus = 15

        # 计算形状匹配度 - 考虑周围已放置图片的形状
        shape_match_score = 0
        surrounding_images = []

        # 收集周围的图片
        for img in self.placed_images:
            # 检查是否在附近
            if (abs(img['x'] - (x + width)) < width or  # 右侧
                abs((img['x'] + img['width']) - x) < width or  # 左侧
                abs(img['y'] - (y + height)) < height or  # 下方
                abs((img['y'] + img['height']) - y) < height):  # 上方
                surrounding_images.append(img)

        # 如果有周围图片，计算形状匹配度
        if surrounding_images:
            # 计算周围图片的平均宽高比
            avg_aspect_ratio = 0
            for img in surrounding_images:
                img_aspect_ratio = img['width'] / img['height'] if img['height'] > 0 else 1
                avg_aspect_ratio += img_aspect_ratio

            avg_aspect_ratio /= len(surrounding_images)

            # 计算当前图片的宽高比
            current_aspect_ratio = width / height if height > 0 else 1

            # 计算宽高比的相似度 (0-1)
            aspect_similarity = min(current_aspect_ratio, avg_aspect_ratio) / max(current_aspect_ratio, avg_aspect_ratio)

            # 根据相似度计算形状匹配分数
            shape_match_score = aspect_similarity * 20  # 最多20分

        # 计算垂直紧凑度评分
        vertical_compactness_score = 0

        # 检查上方是否有图片
        for img in self.placed_images:
            if (img['x'] <= x < img['x'] + img['width'] or
                img['x'] < x + width <= img['x'] + img['width'] or
                x <= img['x'] < x + width):
                if img['y'] + img['height'] <= y:
                    # 计算垂直距离
                    vertical_distance = y - (img['y'] + img['height'])
                    # 距离越小，评分越高
                    if vertical_distance < 5:  # 几乎紧贴
                        vertical_compactness_score += 50
                    elif vertical_distance < 15:  # 较近
                        vertical_compactness_score += 30
                    elif vertical_distance < 30:  # 一般距离
                        vertical_compactness_score += 15
                    break

        # 检查下方是否有图片
        for img in self.placed_images:
            if (img['x'] <= x < img['x'] + img['width'] or
                img['x'] < x + width <= img['x'] + img['width'] or
                x <= img['x'] < x + width):
                if img['y'] >= y + height:
                    # 计算垂直距离
                    vertical_distance = img['y'] - (y + height)
                    # 距离越小，评分越高
                    if vertical_distance < 5:  # 几乎紧贴
                        vertical_compactness_score += 50
                    elif vertical_distance < 15:  # 较近
                        vertical_compactness_score += 30
                    elif vertical_distance < 30:  # 一般距离
                        vertical_compactness_score += 15
                    break

        # 综合评分：
        # - 水平利用率(35%)
        # - 利用率提升(15%)
        # - 高度(10%)
        # - 行填充(10%)
        # - 空隙填充(5%)
        # - 边缘接触(10%)
        # - 边界接触(5%)
        # - 底部优先(5%)
        # - 形状匹配(5%)
        # - 垂直紧凑度(15%) - 新增
        final_score = (
            0.35 * horizontal_util_score +
            0.15 * util_improvement_score +
            0.10 * height_score +
            0.10 * row_fill_score +
            0.05 * gap_fill_score +
            0.10 * edge_contact_score +
            0.05 * edge_bonus +
            0.05 * bottom_bonus +
            0.05 * shape_match_score +
            0.15 * vertical_compactness_score  # 新增垂直紧凑度评分
        )

        return final_score

    def _is_occupied(self, x: int, y: int) -> bool:
        """
        检查指定位置是否被占用

        Args:
            x: x坐标
            y: y坐标

        Returns:
            bool: 是否被占用
        """
        # 检查是否超出边界
        if x < 0 or x >= self.container_width or y < 0:
            return False

        # 检查是否与已放置图片重叠
        for img in self.placed_images:
            img_left = img['x']
            img_right = img['x'] + img['width']
            img_top = img['y']
            img_bottom = img['y'] + img['height']

            if img_left <= x < img_right and img_top <= y < img_bottom:
                return True

        return False

    def find_best_position(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        寻找最佳放置位置，优化水平利用率，避免垂直空隙
        图片放置在坐标最低点，而不是每一行的最低点

        Args:
            width: 图片宽度（包含间距）
            height: 图片高度（包含间距）
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 如果没有已放置的图片，从左上角开始放置
        if not self.placed_images:
            return 0, 0, True

        # 获取水平优先级
        horizontal_priority = self.horizontal_priority / 100.0
        if image_data and 'c_horizontal_priority' in image_data:
            horizontal_priority = image_data['c_horizontal_priority'] / 100.0

        # 记录日志
        if self.log_enabled:
            log.info(f"寻找最佳位置: 图片尺寸 {width}x{height}, 水平优先级: {horizontal_priority:.2f}")

        # 创建高度图（skyline），记录每个x坐标的最高点
        # 这样可以找到整个画布的最低可用点，而不仅仅是行的最低点
        skyline = [0] * (self.container_width + 1)

        # 更新skyline，记录每个x坐标的最高点
        for img in self.placed_images:
            img_left = img['x']
            img_right = img['x'] + img['width']
            img_top = img['y']
            img_bottom = img['y'] + img['height']

            # 更新该图片覆盖范围内的skyline
            for x in range(img_left, min(img_right, self.container_width)):
                skyline[x] = max(skyline[x], img_bottom)

        # 初始化最佳位置变量
        best_y = float('inf')  # 最低的y坐标
        best_x = 0             # 对应的x坐标
        best_score = -float('inf')  # 位置评分

        # 尝试所有可能的水平起点，步长为宽度的1/20，提高效率
        step = max(1, width // 20)
        for x in range(0, self.container_width - width + 1, step):
            # 检查在x位置放置图片是否会超出容器边界
            if x + width > self.container_width:
                continue

            # 找到当前x位置下的最低可放置点
            # 这是该位置的skyline高度（即该位置下方已有图片的最高点）
            y = 0
            for i in range(x, x + width):
                y = max(y, skyline[i])

            # 检查在(x,y)位置放置图片是否会与其他图片重叠
            can_place = True
            for img in self.placed_images:
                img_left = img['x']
                img_right = img['x'] + img['width']
                img_top = img['y']
                img_bottom = img['y'] + img['height']

                # 检查是否重叠
                if not (x + width <= img_left or x >= img_right or
                        y + height <= img_top or y >= img_bottom):
                    can_place = False
                    break

            # 如果不能放置，尝试检测垂直空隙
            if not can_place:
                # 检查是否存在垂直空隙
                vertical_gap_found = False
                vertical_gap_y = 0

                # 获取当前位置的左右两侧图片
                left_images = []
                right_images = []

                for img in self.placed_images:
                    # 左侧图片
                    if img['x'] + img['width'] <= x and img['x'] + img['width'] >= x - 20:  # 左侧20像素范围内
                        left_images.append(img)
                    # 右侧图片
                    if img['x'] >= x + width and img['x'] <= x + width + 20:  # 右侧20像素范围内
                        right_images.append(img)

                # 如果左右两侧都有图片，可能存在垂直空隙
                if left_images and right_images:
                    # 找到左侧图片的最大y坐标
                    left_max_y = max(img['y'] + img['height'] for img in left_images)
                    # 找到右侧图片的最大y坐标
                    right_max_y = max(img['y'] + img['height'] for img in right_images)
                    # 取较大的y坐标作为垂直空隙的起始点
                    vertical_gap_y = max(left_max_y, right_max_y)

                    # 检查在垂直空隙位置放置图片是否会与其他图片重叠
                    vertical_gap_can_place = True
                    for img in self.placed_images:
                        img_left = img['x']
                        img_right = img['x'] + img['width']
                        img_top = img['y']
                        img_bottom = img['y'] + img['height']

                        # 检查是否重叠
                        if not (x + width <= img_left or x >= img_right or
                                vertical_gap_y + height <= img_top or vertical_gap_y >= img_bottom):
                            vertical_gap_can_place = False
                            break

                    if vertical_gap_can_place:
                        vertical_gap_found = True
                        y = vertical_gap_y
                        can_place = True

                # 如果仍然不能放置，继续尝试下一个位置
                if not vertical_gap_found:
                    continue

            # 如果可以放置
            if can_place:
                # 计算放置后的水平利用率
                # 创建一个临时的skyline来计算
                temp_skyline = skyline.copy()
                for i in range(x, x + width):
                    temp_skyline[i] = max(temp_skyline[i], y + height)

                # 计算放置前的行利用率
                current_row_util = 0
                occupied_width = 0
                for i in range(self.container_width):
                    if skyline[i] > y:
                        occupied_width += 1
                if self.container_width > 0:
                    current_row_util = occupied_width / self.container_width

                # 计算放置后的行利用率
                new_row_util = 0
                new_occupied_width = 0
                for i in range(self.container_width):
                    if temp_skyline[i] > y:
                        new_occupied_width += 1
                if self.container_width > 0:
                    new_row_util = new_occupied_width / self.container_width

                # 计算利用率提升
                util_improvement = new_row_util - current_row_util

                # 计算位置评分：结合高度和水平利用率
                # 高度权重 = 1 - 水平优先级，水平利用率权重 = 水平优先级
                height_score = -y  # 负值，因为我们想要最小化高度
                util_score = util_improvement * 100  # 放大利用率提升的影响

                # 额外奖励：如果图片底部与其他图片顶部对齐（减少垂直空隙）
                alignment_bonus = 0
                for img in self.placed_images:
                    if img['y'] + img['height'] == y:
                        alignment_bonus += 20  # 给予较大的奖励
                        break

                # 计算最终评分
                score = (1 - horizontal_priority) * height_score + horizontal_priority * util_score + alignment_bonus

                # 更新最佳位置
                # 优先考虑评分更高的位置，如果评分相同，选择y坐标更小的位置
                if score > best_score or (score == best_score and y < best_y):
                    best_score = score
                    best_y = y
                    best_x = x

                    # 记录日志
                    if self.log_enabled and random.random() < 0.1:  # 只记录10%的日志，避免日志过多
                        log.info(f"更新最佳位置: ({best_x}, {best_y}), 评分: {best_score:.2f}, 高度: {y}, 利用率提升: {util_improvement:.2f}")

        # 如果找到合适位置，返回
        if best_y < float('inf'):
            # 记录日志
            if self.log_enabled:
                log.info(f"找到最佳位置: ({best_x}, {best_y}), 评分: {best_score:.2f}")
            return best_x, best_y, True

        # 如果无法放置，返回失败
        if self.log_enabled:
            log.info("无法找到合适的放置位置")
        return 0, 0, False



    def _check_collision(self, x: int, y: int, width: int, height: int) -> bool:
        """
        检查在位置(x,y)放置图片是否会发生碰撞

        使用空间分区和并行处理优化碰撞检测性能

        注意：width和height已经包含了图片间距
        注意：width表示长边，height表示短边，但如果图片已旋转，则width和height已交换

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度（包含间距，如果已旋转则为短边）
            height: 图片高度（包含间距，如果已旋转则为长边）

        Returns:
            bool: 是否发生碰撞
        """

        # 检查是否超出容器边界
        if x < 0 or y < 0:
            return True

        # 特殊处理超宽图片：如果图片宽度超过容器宽度，强制返回碰撞
        # 这样可以防止超宽图片被错误放置
        if width > self.container_width:  # 如果宽度超过容器宽度
            log.warning(f"检测到超宽图片: 宽度 {width}px 超过容器宽度 {self.container_width}px，强制返回碰撞")
            return True

        # 正常检查容器边界
        if x + width > self.container_width:
            return True

        # 检查是否超过最大高度限制
        if self.max_height > 0 and y + height > self.max_height:
            log.warning(f"检测到超高图片: 位置 ({x}, {y}) 高度 {height}px 会超过最大高度限制 {self.max_height}px，强制返回碰撞")
            return True

        # 如果是第一个图片，放在底部
        if not self.placed_images:
            return y > 0

        # 标准碰撞检测，不再特殊处理间距
        # 使用标准的碰撞检测边界
        collision_margin = 0  # 不缩小碰撞边界

        # 使用并行处理检查碰撞
        if self.use_parallel_processing and self.thread_pool and len(self.placed_images) > 50:
            try:
                # 使用网格优化
                if self.use_grid_optimization:
                    # 计算当前矩形所在的网格单元格
                    grid_x_start = max(0, x // self.grid_cell_size)
                    grid_x_end = min((x + width) // self.grid_cell_size + 1,
                                    self.container_width // self.grid_cell_size)
                    grid_y_start = max(0, y // self.grid_cell_size)
                    grid_y_end = min((y + height) // self.grid_cell_size + 1,
                                    self.max_height // self.grid_cell_size if self.max_height > 0 else 1000)

                    # 收集需要检查的图片索引
                    checked_indices = set()
                    for grid_x in range(grid_x_start, grid_x_end):
                        for grid_y in range(grid_y_start, grid_y_end):
                            grid_key = (grid_x, grid_y)
                            if grid_key in self.grid_cells:
                                for img_index in self.grid_cells[grid_key]:
                                    if img_index not in checked_indices and 0 <= img_index < len(self.placed_images):
                                        checked_indices.add(img_index)

                    # 如果需要检查的图片数量较多，使用并行处理
                    if len(checked_indices) > 10:
                        # 将图片分成多个批次
                        batch_size = max(1, len(checked_indices) // 4)
                        batches = []
                        current_batch = []

                        for img_index in checked_indices:
                            current_batch.append(img_index)
                            if len(current_batch) >= batch_size:
                                batches.append(current_batch)
                                current_batch = []

                        if current_batch:
                            batches.append(current_batch)

                        # 定义检查批次的函数
                        def check_batch(batch_indices):
                            for idx in batch_indices:
                                img = self.placed_images[idx]
                                # 计算两个矩形的边界，应用碰撞边界缩小量
                                img_left = img['x'] + collision_margin
                                img_right = img['x'] + img['width'] - collision_margin
                                img_top = img['y'] + collision_margin
                                img_bottom = img['y'] + img['height'] - collision_margin

                                rect_left = x + collision_margin
                                rect_right = x + width - collision_margin
                                rect_top = y + collision_margin
                                rect_bottom = y + height - collision_margin

                                # 检查是否重叠，确保边界有效
                                if (img_right > img_left and img_bottom > img_top and
                                    rect_right > rect_left and rect_bottom > rect_top and
                                    not (rect_right <= img_left or rect_left >= img_right or
                                         rect_bottom <= img_top or rect_top >= img_bottom)):
                                    return True
                            return False

                        # 并行检查所有批次
                        futures = [self.thread_pool.submit(check_batch, batch) for batch in batches]

                        # 如果任何一个批次检测到碰撞，则返回True
                        for future in concurrent.futures.as_completed(futures):
                            if future.result():
                                return True

                        return False
                    else:
                        # 如果图片数量较少，使用传统方法
                        for img_index in checked_indices:
                            img = self.placed_images[img_index]
                            # 计算两个矩形的边界，应用碰撞边界缩小量
                            img_left = img['x'] + collision_margin
                            img_right = img['x'] + img['width'] - collision_margin
                            img_top = img['y'] + collision_margin
                            img_bottom = img['y'] + img['height'] - collision_margin

                            rect_left = x + collision_margin
                            rect_right = x + width - collision_margin
                            rect_top = y + collision_margin
                            rect_bottom = y + height - collision_margin

                            # 检查是否重叠，确保边界有效
                            if (img_right > img_left and img_bottom > img_top and
                                rect_right > rect_left and rect_bottom > rect_top and
                                not (rect_right <= img_left or rect_left >= img_right or
                                     rect_bottom <= img_top or rect_top >= img_bottom)):
                                return True
                else:
                    # 如果不使用网格优化，但图片数量较多，仍然使用并行处理
                    if len(self.placed_images) > 50:
                        # 将图片分成多个批次
                        batch_size = max(1, len(self.placed_images) // 4)
                        batches = []
                        current_batch = []

                        for i, img in enumerate(self.placed_images):
                            current_batch.append(i)
                            if len(current_batch) >= batch_size:
                                batches.append(current_batch)
                                current_batch = []

                        if current_batch:
                            batches.append(current_batch)

                        # 定义检查批次的函数
                        def check_batch(batch_indices):
                            for idx in batch_indices:
                                img = self.placed_images[idx]
                                # 计算两个矩形的边界，应用碰撞边界缩小量
                                img_left = img['x'] + collision_margin
                                img_right = img['x'] + img['width'] - collision_margin
                                img_top = img['y'] + collision_margin
                                img_bottom = img['y'] + img['height'] - collision_margin

                                rect_left = x + collision_margin
                                rect_right = x + width - collision_margin
                                rect_top = y + collision_margin
                                rect_bottom = y + height - collision_margin

                                # 检查是否重叠，确保边界有效
                                if (img_right > img_left and img_bottom > img_top and
                                    rect_right > rect_left and rect_bottom > rect_top and
                                    not (rect_right <= img_left or rect_left >= img_right or
                                         rect_bottom <= img_top or rect_top >= img_bottom)):
                                    return True
                            return False

                        # 并行检查所有批次
                        futures = [self.thread_pool.submit(check_batch, batch) for batch in batches]

                        # 如果任何一个批次检测到碰撞，则返回True
                        for future in concurrent.futures.as_completed(futures):
                            if future.result():
                                return True

                        return False
            except Exception as e:
                # 如果并行处理出错，回退到传统方法
                log.warning(f"并行碰撞检测失败: {str(e)}，回退到传统方法")

        # 使用网格优化检查碰撞
        if self.use_grid_optimization and len(self.placed_images) > 10:
            try:
                # 计算当前矩形所在的网格单元格
                grid_x_start = max(0, x // self.grid_cell_size)
                grid_x_end = min((x + width) // self.grid_cell_size + 1,
                                self.container_width // self.grid_cell_size)
                grid_y_start = max(0, y // self.grid_cell_size)
                grid_y_end = min((y + height) // self.grid_cell_size + 1,
                                self.max_height // self.grid_cell_size if self.max_height > 0 else 1000)

                # 检查这些单元格中的图片是否与当前矩形重叠
                checked_indices = set()
                for grid_x in range(grid_x_start, grid_x_end):
                    for grid_y in range(grid_y_start, grid_y_end):
                        grid_key = (grid_x, grid_y)
                        if grid_key in self.grid_cells:
                            for img_index in self.grid_cells[grid_key]:
                                if img_index in checked_indices:
                                    continue
                                checked_indices.add(img_index)

                                # 防止索引错误
                                if img_index < 0 or img_index >= len(self.placed_images):
                                    continue

                                img = self.placed_images[img_index]
                                # 计算两个矩形的边界，应用碰撞边界缩小量
                                img_left = img['x'] + collision_margin
                                img_right = img['x'] + img['width'] - collision_margin
                                img_top = img['y'] + collision_margin
                                img_bottom = img['y'] + img['height'] - collision_margin

                                rect_left = x + collision_margin
                                rect_right = x + width - collision_margin
                                rect_top = y + collision_margin
                                rect_bottom = y + height - collision_margin

                                # 检查是否重叠，确保边界有效
                                if (img_right > img_left and img_bottom > img_top and
                                    rect_right > rect_left and rect_bottom > rect_top and
                                    not (rect_right <= img_left or rect_left >= img_right or
                                         rect_bottom <= img_top or rect_top >= img_bottom)):
                                    return True
            except Exception as e:
                # 如果网格优化出错，回退到传统方法
                log.warning(f"网格碰撞检测失败: {str(e)}，回退到传统方法")
        else:
            # 使用传统方法检查是否与已放置的图片重叠
            for img in self.placed_images:
                # 计算两个矩形的边界，应用碰撞边界缩小量
                img_left = img['x'] + collision_margin
                img_right = img['x'] + img['width'] - collision_margin
                img_top = img['y'] + collision_margin
                img_bottom = img['y'] + img['height'] - collision_margin

                rect_left = x + collision_margin
                rect_right = x + width - collision_margin
                rect_top = y + collision_margin
                rect_bottom = y + height - collision_margin

                # 检查是否重叠，确保边界有效
                if (img_right > img_left and img_bottom > img_top and
                    rect_right > rect_left and rect_bottom > rect_top and
                    not (rect_right <= img_left or rect_left >= img_right or
                         rect_bottom <= img_top or rect_top >= img_bottom)):
                    return True

        return False

    def get_utilization(self) -> float:
        """
        计算画布利用率

        Returns:
            float: 利用率（0-1之间）
        """
        if not self.placed_images:
            return 0.0

        # 计算已放置图片的总面积
        total_area = 0
        for img in self.placed_images:
            total_area += img['width'] * img['height']

        # 计算画布面积
        canvas_area = self.container_width * self.max_height
        if canvas_area <= 0:
            return 0.0

        # 计算利用率
        return total_area / canvas_area

    def get_horizontal_utilization(self) -> Dict[int, float]:
        """
        计算每一行的水平利用率

        Returns:
            Dict[int, float]: 键为y坐标，值为该行的水平利用率（0-1之间）
        """
        if not self.placed_images:
            return {}

        # 按行分组已放置的图片
        rows = {}
        for img in self.placed_images:
            y = img['y']
            height = img['height']

            # 将图片添加到所有覆盖的行
            for row_y in range(y, y + height):
                if row_y not in rows:
                    rows[row_y] = []
                rows[row_y].append(img)

        # 计算每一行的水平利用率
        row_utilization = {}
        for y, row_images in rows.items():
            # 计算行中所有图片的水平覆盖范围
            covered_ranges = []
            for img in row_images:
                covered_ranges.append((img['x'], img['x'] + img['width']))

            # 合并重叠的范围
            covered_ranges.sort()
            merged_ranges = []
            for start, end in covered_ranges:
                if not merged_ranges or start > merged_ranges[-1][1]:
                    merged_ranges.append((start, end))
                else:
                    merged_ranges[-1] = (merged_ranges[-1][0], max(merged_ranges[-1][1], end))

            # 计算覆盖的总宽度
            covered_width = sum(end - start for start, end in merged_ranges)

            # 计算行利用率
            row_utilization[y] = covered_width / self.container_width

        return row_utilization

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计信息
        """
        return {
            'placement_count': self.placement_count,
            'placement_time': self.placement_time,
            'gap_fill_count': self.gap_fill_count,
            'gap_fill_time': self.gap_fill_time,
            'drop_simulation_count': self.drop_simulation_count,
            'drop_simulation_time': self.drop_simulation_time
        }

    def _update_after_placement(self, x: int, y: int, width: int, height: int, width_with_spacing: int, height_with_spacing: int, image_data: Dict[str, Any] = None, need_rotation: bool = False, is_gap_fill: bool = False) -> None:
        """
        更新放置后的状态

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度（不包含间距）
            height: 图片高度（不包含间距）
            width_with_spacing: 图片宽度（包含间距）
            height_with_spacing: 图片高度（包含间距）
            image_data: 图片相关数据
            need_rotation: 是否需要旋转
            is_gap_fill: 是否是填充空隙
        """
        # 更新最大高度
        self.max_height = max(self.max_height, y + height_with_spacing)

        # 更新行高度信息
        for row_y in range(y, y + height_with_spacing):
            self.row_heights[row_y] = max(self.row_heights.get(row_y, 0), height_with_spacing - (row_y - y))

        # 更新行右边界信息
        for row_y in range(y, y + height_with_spacing):
            self.row_right_edges[row_y] = max(self.row_right_edges.get(row_y, 0), x + width_with_spacing)

        # 更新网格
        for grid_x in range(x, x + width_with_spacing):
            for grid_y in range(y, y + height_with_spacing):
                self.grid[(grid_x, grid_y)] = True

        # 创建图片信息
        img_info = {
            'x': x,
            'y': y,
            'width': width,
            'height': height,
            'rotated': need_rotation,
            'is_gap_fill': is_gap_fill
        }

        # 添加图片相关数据
        if image_data:
            img_info['data'] = image_data

        # 添加到已放置图片列表
        self.placed_images.append(img_info)

        # 更新网格优化相关信息
        if self.use_grid_optimization:
            img_index = len(self.placed_images) - 1
            grid_x_start = x // self.grid_cell_size
            grid_x_end = (x + width_with_spacing) // self.grid_cell_size
            grid_y_start = y // self.grid_cell_size
            grid_y_end = (y + height_with_spacing) // self.grid_cell_size

            for grid_x in range(grid_x_start, grid_x_end + 1):
                for grid_y in range(grid_y_start, grid_y_end + 1):
                    grid_key = (grid_x, grid_y)
                    if grid_key not in self.grid_cells:
                        self.grid_cells[grid_key] = []
                    self.grid_cells[grid_key].append(img_index)

    def redistribute_images(self, max_time_seconds: float = 5.0, max_images_to_process: int = 100) -> bool:
        """
        重新分配图片位置，尝试填充中间空隙，优化整体布局

        Args:
            max_time_seconds: 最大执行时间（秒），超过此时间将提前返回
            max_images_to_process: 最大处理图片数量，超过此数量将只处理部分图片

        Returns:
            bool: 是否成功重新分配
        """
        if len(self.placed_images) < 2:
            return False

        # 记录开始时间
        start_time = time.time()

        try:
            # 记录原始布局
            original_layout = self.placed_images.copy()
            original_max_height = self.max_height
            original_grid = self.grid.copy()

            # 限制处理的图片数量，避免处理时间过长
            # 优先处理底部30%区域的图片，这些图片对优化效果影响最大
            bottom_threshold = self.max_height * 0.7
            bottom_images = [img for img in original_layout if img['y'] >= bottom_threshold]
            other_images = [img for img in original_layout if img['y'] < bottom_threshold]

            # 如果底部图片太多，只处理部分
            if len(bottom_images) > max_images_to_process:
                # 按面积降序排序，优先处理大图片
                bottom_images.sort(key=lambda img: img['width'] * img['height'], reverse=True)
                bottom_images = bottom_images[:max_images_to_process]

            # 合并需要处理的图片列表
            images_to_process = bottom_images

            # 如果处理的图片太少，添加一些非底部图片
            if len(images_to_process) < max_images_to_process // 2 and other_images:
                # 按面积降序排序，优先处理大图片
                other_images.sort(key=lambda img: img['width'] * img['height'], reverse=True)
                images_to_process.extend(other_images[:max_images_to_process - len(images_to_process)])

            # 如果没有图片需要处理，返回失败
            if not images_to_process:
                return False

            # 第一步：识别所有空隙
            # 创建一个二维网格表示画布占用情况
            grid_width = self.container_width
            grid_height = self.max_height
            grid = [[False for _ in range(grid_width)] for _ in range(grid_height)]

            # 标记已占用的区域
            for img in self.placed_images:
                x = img['x']
                y = img['y']
                width = img['width']
                height = img['height']

                for i in range(x, min(x + width, grid_width)):
                    for j in range(y, min(y + height, grid_height)):
                        if 0 <= i < grid_width and 0 <= j < grid_height:
                            grid[j][i] = True

            # 使用连通区域算法找到所有空隙
            all_gaps = []
            visited = [[False for _ in range(grid_width)] for _ in range(grid_height)]

            # 定义方向：上、右、下、左
            directions = [(-1, 0), (0, 1), (1, 0), (0, -1)]

            # 广度优先搜索找到连通区域
            for y in range(grid_height):
                for x in range(grid_width):
                    if not grid[y][x] and not visited[y][x]:
                        # 找到一个空隙的起点
                        gap_cells = []
                        queue = [(y, x)]
                        visited[y][x] = True

                        while queue:
                            cy, cx = queue.pop(0)
                            gap_cells.append((cy, cx))

                            for dy, dx in directions:
                                ny, nx = cy + dy, cx + dx
                                if (0 <= ny < grid_height and 0 <= nx < grid_width and
                                    not grid[ny][nx] and not visited[ny][nx]):
                                    queue.append((ny, nx))
                                    visited[ny][nx] = True

                        # 计算空隙的边界
                        min_x = min(cx for _, cx in gap_cells)
                        max_x = max(cx for _, cx in gap_cells) + 1
                        min_y = min(cy for cy, _ in gap_cells)
                        max_y = max(cy for cy, _ in gap_cells) + 1

                        # 计算空隙的宽度和高度
                        gap_width = max_x - min_x
                        gap_height = max_y - min_y

                        # 只考虑足够大的空隙
                        if gap_width >= 10 and gap_height >= 10:
                            # 计算空隙的面积
                            gap_area = len(gap_cells)

                            # 计算空隙的填充率（实际空隙面积 / 边界矩形面积）
                            fill_ratio = gap_area / (gap_width * gap_height)

                            # 记录空隙信息
                            all_gaps.append({
                                'x': min_x,
                                'y': min_y,
                                'width': gap_width,
                                'height': gap_height,
                                'area': gap_area,
                                'fill_ratio': fill_ratio,
                                'cells': gap_cells
                            })

            # 按面积降序排序空隙，优先填充大空隙
            all_gaps.sort(key=lambda g: -g['area'])

            # 检查是否超时
            if time.time() - start_time > max_time_seconds * 0.3:  # 分配30%的时间用于空隙检测
                # 如果已经超时，使用更简单的方法
                return self._redistribute_images_fast(original_layout, original_max_height, original_grid,
                                                    images_to_process, max_time_seconds * 0.7, start_time)

            # 第二步：尝试移动图片填充空隙
            # 清空当前布局
            self.placed_images = []
            self.grid = {}
            self.row_heights = {}
            self.row_right_edges = {}
            self.grid_cells = {}
            self.max_height = 0

            # 按面积降序排序图片，优先放置大图片
            sorted_images = sorted(original_layout, key=lambda img: -(img['width'] * img['height']))

            # 移动计数
            moved_count = 0

            # 检查是否超时
            if time.time() - start_time > max_time_seconds * 0.4:  # 分配40%的时间用于初始化
                # 如果已经超时，使用更简单的方法
                return self._redistribute_images_fast(original_layout, original_max_height, original_grid,
                                                    images_to_process, max_time_seconds * 0.6, start_time)

            # 第一轮：尝试填充空隙
            for gap in all_gaps:
                # 找到最适合这个空隙的图片
                best_img_index = -1
                best_fit_score = -float('inf')
                best_rotation = False

                for i, img in enumerate(sorted_images):
                    if img in self.placed_images:
                        continue  # 跳过已放置的图片

                    img_width = img['width']
                    img_height = img['height']

                    # 考虑旋转
                    for rotation in [False, True]:
                        actual_width = img_height if rotation else img_width
                        actual_height = img_width if rotation else img_height

                        # 检查图片是否能放入空隙
                        if actual_width <= gap['width'] and actual_height <= gap['height']:
                            # 计算适应度分数
                            width_ratio = actual_width / gap['width']
                            height_ratio = actual_height / gap['height']
                            area_ratio = (actual_width * actual_height) / gap['area']

                            # 综合评分：面积利用率 + 形状匹配度
                            fit_score = area_ratio * 0.7 + (width_ratio * height_ratio) * 0.3

                            # 更新最佳匹配
                            if fit_score > best_fit_score:
                                best_fit_score = fit_score
                                best_img_index = i
                                best_rotation = rotation

                # 如果找到合适的图片，放置到空隙中
                if best_img_index >= 0 and best_fit_score > 0.5:  # 要求至少50%的适应度
                    img = sorted_images[best_img_index]
                    img_width = img['width']
                    img_height = img['height']

                    # 考虑旋转
                    if best_rotation:
                        img_width, img_height = img_height, img_width

                    # 放置图片
                    x = gap['x']
                    y = gap['y']

                    # 检查是否与已放置的图片重叠
                    if not self._check_collision(x, y, img_width, img_height):
                        # 更新图片位置
                        img['x'] = x
                        img['y'] = y
                        img['rotated'] = best_rotation

                        # 添加到已放置图片列表
                        width_with_spacing = img_width + self.image_spacing
                        height_with_spacing = img_height + self.image_spacing
                        self._update_after_placement(
                            x, y, img_width, img_height,
                            width_with_spacing, height_with_spacing,
                            img.get('data', {}), best_rotation, True
                        )

                        moved_count += 1

            # 第二轮：放置剩余图片
            for img in sorted_images:
                if img in self.placed_images:
                    continue  # 跳过已放置的图片

                img_width = img['width']
                img_height = img['height']
                img_width_with_spacing = img_width + self.image_spacing
                img_height_with_spacing = img_height + self.image_spacing

                # 尝试找到最佳位置
                try:
                    # 首先尝试填充空隙
                    gap_position = self._try_fill_gap(img_width_with_spacing, img_height_with_spacing, img.get('data', {}))

                    if gap_position[2]:  # 如果成功填充空隙
                        x, y, _ = gap_position
                        img['x'] = x
                        img['y'] = y

                        # 添加到已放置图片列表
                        self._update_after_placement(
                            x, y, img_width, img_height,
                            img_width_with_spacing, img_height_with_spacing,
                            img.get('data', {}), img.get('rotated', False), True
                        )

                        moved_count += 1
                    else:
                        # 如果无法填充空隙，尝试从上到下找位置
                        position = self._find_best_position(img_width_with_spacing, img_height_with_spacing, img.get('data', {}))

                        if position[2]:  # 如果找到位置
                            x, y, _ = position
                            img['x'] = x
                            img['y'] = y

                            # 添加到已放置图片列表
                            self._update_after_placement(
                                x, y, img_width, img_height,
                                img_width_with_spacing, img_height_with_spacing,
                                img.get('data', {}), img.get('rotated', False), False
                            )

                            moved_count += 1
                        else:
                            # 如果无法找到位置，恢复原始位置
                            original_x = img.get('original_x', img['x'])
                            original_y = img.get('original_y', img['y'])

                            # 添加到已放置图片列表
                            self._update_after_placement(
                                original_x, original_y, img_width, img_height,
                                img_width_with_spacing, img_height_with_spacing,
                                img.get('data', {}), img.get('rotated', False), False
                            )
                except Exception as e:
                    # 如果出现错误，使用原始位置
                    original_x = img.get('original_x', img['x'])
                    original_y = img.get('original_y', img['y'])

                    # 添加到已放置图片列表
                    self._update_after_placement(
                        original_x, original_y, img_width, img_height,
                        img_width_with_spacing, img_height_with_spacing,
                        img.get('data', {}), img.get('rotated', False), False
                    )

            # 第三轮：尝试填充剩余空隙
            # 重新计算空隙
            updated_gaps = []

            # 创建skyline，记录每个x坐标的最高点
            skyline = [0] * (self.container_width + 1)
            for img in self.placed_images:
                img_left = img['x']
                img_right = img['x'] + img['width']
                img_bottom = img['y'] + img['height']
                for x in range(img_left, min(img_right, self.container_width)):
                    skyline[x] = max(skyline[x], img_bottom)

            # 计算最大高度
            max_height = max(skyline)

            # 找到底部空隙
            x = 0
            while x < self.container_width:
                # 找到空隙的开始
                if x < self.container_width and skyline[x] < max_height:
                    gap_start = x
                    # 找到空隙的结束
                    while x < self.container_width and skyline[x] < max_height:
                        x += 1
                    gap_end = x

                    # 计算空隙的宽度和高度
                    gap_width = gap_end - gap_start
                    gap_height = max_height - max(skyline[gap_start:gap_end])

                    # 只考虑足够大的空隙
                    if gap_width >= 10 and gap_height >= 10:
                        updated_gaps.append({
                            'x': gap_start,
                            'y': max(skyline[gap_start:gap_end]),
                            'width': gap_width,
                            'height': gap_height,
                            'area': gap_width * gap_height
                        })
                else:
                    x += 1

            # 按面积降序排序空隙
            updated_gaps.sort(key=lambda g: -g['area'])

            # 尝试移动小图片填充底部空隙
            for gap in updated_gaps:
                # 找到最适合这个空隙的图片
                best_img_index = -1
                best_fit_score = -float('inf')
                best_rotation = False

                for i, img in enumerate(self.placed_images):
                    # 只考虑小图片和底部30%的图片
                    if (img['width'] * img['height'] > gap['area'] * 0.8 or
                        img['y'] < self.max_height * 0.7):
                        continue

                    img_width = img['width']
                    img_height = img['height']

                    # 考虑旋转
                    for rotation in [False, True]:
                        actual_width = img_height if rotation else img_width
                        actual_height = img_width if rotation else img_height

                        # 检查图片是否能放入空隙
                        if actual_width <= gap['width'] and actual_height <= gap['height']:
                            # 计算适应度分数
                            width_ratio = actual_width / gap['width']
                            height_ratio = actual_height / gap['height']
                            area_ratio = (actual_width * actual_height) / gap['area']

                            # 综合评分：面积利用率 + 形状匹配度
                            fit_score = area_ratio * 0.7 + (width_ratio * height_ratio) * 0.3

                            # 更新最佳匹配
                            if fit_score > best_fit_score:
                                best_fit_score = fit_score
                                best_img_index = i
                                best_rotation = rotation

                # 如果找到合适的图片，移动到空隙中
                if best_img_index >= 0 and best_fit_score > 0.6:  # 要求至少60%的适应度
                    img = self.placed_images[best_img_index]
                    img_width = img['width']
                    img_height = img['height']

                    # 考虑旋转
                    if best_rotation:
                        img_width, img_height = img_height, img_width

                    # 放置图片
                    old_x = img['x']
                    old_y = img['y']
                    new_x = gap['x']
                    new_y = gap['y']

                    # 从已放置图片列表中移除
                    self.placed_images.remove(img)

                    # 清除原位置的网格占用
                    for i in range(old_x, old_x + img_width):
                        for j in range(old_y, old_y + img_height):
                            if (i, j) in self.grid:
                                del self.grid[(i, j)]

                    # 检查是否与已放置的图片重叠
                    if not self._check_collision(new_x, new_y, img_width, img_height):
                        # 更新图片位置
                        img['x'] = new_x
                        img['y'] = new_y
                        img['rotated'] = best_rotation

                        # 添加到已放置图片列表
                        width_with_spacing = img_width + self.image_spacing
                        height_with_spacing = img_height + self.image_spacing
                        self._update_after_placement(
                            new_x, new_y, img_width, img_height,
                            width_with_spacing, height_with_spacing,
                            img.get('data', {}), best_rotation, True
                        )

                        moved_count += 1
                    else:
                        # 如果无法移动，恢复原位置
                        img['x'] = old_x
                        img['y'] = old_y

                        # 添加回已放置图片列表
                        width_with_spacing = img_width + self.image_spacing
                        height_with_spacing = img_height + self.image_spacing
                        self._update_after_placement(
                            old_x, old_y, img_width, img_height,
                            width_with_spacing, height_with_spacing,
                            img.get('data', {}), img.get('rotated', False), False
                        )

            # 如果没有移动任何图片，恢复原始布局
            if moved_count == 0:
                self.placed_images = original_layout
                self.max_height = original_max_height
                self.grid = original_grid
                return False

            # 如果有图片被移动，返回成功
            return True
        except Exception as e:
            # 如果出现错误，恢复原始布局
            self.placed_images = original_layout
            self.max_height = original_max_height
            self.grid = original_grid
            return False

    def _redistribute_images_fast(self, original_layout: List[Dict[str, Any]], original_max_height: int,
                                original_grid: Dict[Tuple[int, int], bool], images_to_process: List[Dict[str, Any]],
                                remaining_time: float, start_time: float) -> bool:
        """
        快速重新分配图片位置，用于在超时情况下使用

        Args:
            original_layout: 原始布局
            original_max_height: 原始最大高度
            original_grid: 原始网格
            images_to_process: 需要处理的图片列表
            remaining_time: 剩余时间（秒）
            start_time: 开始时间

        Returns:
            bool: 是否成功重新分配
        """
        # 尝试移动每个图片到更好的位置
        moved_count = 0

        # 按y坐标排序，从底部开始处理
        images_to_process.sort(key=lambda img: img['y'], reverse=True)

        for img in images_to_process:
            # 检查是否超时
            if time.time() - start_time > remaining_time:
                break

            # 获取图片信息
            img_x = img['x']
            img_y = img['y']
            img_width = img['width']
            img_height = img['height']
            need_rotation = img.get('need_rotation', False)

            # 暂时移除当前图片
            self.placed_images.remove(img)

            # 尝试找到更好的位置
            try:
                # 如果需要旋转，交换宽高
                width = img_height if need_rotation else img_width
                height = img_width if need_rotation else img_height

                # 尝试填充空隙
                gap_x, gap_y, gap_success = self._try_fill_gap_fast(width, height, img.get('data', {}))

                if gap_success:
                    # 如果新位置比原位置更好（更靠上或更靠左）
                    if gap_y < img_y or (gap_y == img_y and gap_x < img_x):
                        # 更新图片位置
                        img['x'] = gap_x
                        img['y'] = gap_y
                        moved_count += 1
                else:
                    # 尝试从上到下找位置
                    new_x, new_y, success = self.find_position(width, height, img.get('data', {}))

                    if success:
                        # 如果新位置比原位置更好（更靠上或更靠左）
                        if new_y < img_y or (new_y == img_y and new_x < img_x):
                            # 更新图片位置
                            img['x'] = new_x
                            img['y'] = new_y
                            moved_count += 1
            except Exception as e:
                # 如果出现错误，跳过当前图片
                pass

            # 重新添加图片
            self.placed_images.append(img)

            # 检查是否超时
            if time.time() - start_time > remaining_time:
                break

        # 如果没有移动任何图片，返回失败
        if moved_count == 0:
            return False

        # 重新计算最大高度
        new_max_height = 0
        for img in self.placed_images:
            new_max_height = max(new_max_height, img['y'] + img['height'])

        # 如果新布局的高度没有减少，返回失败
        if new_max_height >= original_max_height:
            return False

        # 更新最大高度
        self.max_height = new_max_height
        return True

    def _try_fill_gap_fast(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        快速尝试填充现有行中的空隙，提高水平利用率
        优化版本，减少计算量，提高性能

        Args:
            width: 图片宽度（包含间距）
            height: 图片高度（包含间距）
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 如果没有已放置的图片，从左上角开始放置
        if not self.placed_images:
            return 0, 0, True

        # 获取所有行的信息
        rows = {}
        for img in self.placed_images:
            y = img['y']
            height = img['height']

            # 记录每一行的图片
            for row in range(y, y + height):
                if row not in rows:
                    rows[row] = []
                rows[row].append(img)

        # 按行高度排序，优先填充底部行
        sorted_rows = sorted(rows.keys(), reverse=True)

        # 尝试在每一行找到合适的位置
        for row in sorted_rows:
            # 获取该行的所有图片
            row_images = rows[row]

            # 计算该行的所有已占用区间
            intervals = []
            for img in row_images:
                intervals.append((img['x'], img['x'] + img['width']))

            # 合并重叠的区间
            if intervals:
                intervals.sort()
                merged = []
                for start, end in intervals:
                    if not merged or start > merged[-1][1]:
                        merged.append((start, end))
                    else:
                        merged[-1] = (merged[-1][0], max(merged[-1][1], end))

                # 找到所有空隙
                gaps = []
                if merged[0][0] > 0:
                    gaps.append((0, merged[0][0]))

                for i in range(len(merged) - 1):
                    gaps.append((merged[i][1], merged[i+1][0]))

                if merged[-1][1] < self.container_width:
                    gaps.append((merged[-1][1], self.container_width))

                # 尝试在每个空隙放置图片
                for gap_start, gap_end in gaps:
                    gap_width = gap_end - gap_start

                    # 如果空隙足够大
                    if gap_width >= width:
                        # 检查是否可以放置
                        if not self._check_collision(gap_start, row, width, height):
                            return gap_start, row, True

        # 如果无法填充空隙，返回失败
        return 0, 0, False

    def _try_fill_gap(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        尝试填充现有行中的空隙
        优先考虑垂直空隙，避免图片垂直方向有空隙

        Args:
            width: 图片宽度（包含间距）
            height: 图片高度（包含间距）
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 如果没有已放置的图片，无法填充空隙
        if not self.placed_images:
            return 0, 0, False

        # 获取行空隙误差范围（默认为5%）
        gap_error_range = 0.05
        if image_data and 'gap_error_range' in image_data:
            gap_error_range = image_data['gap_error_range']
        elif image_data and 'c_gap_filling_priority' in image_data:
            # 使用空隙填充优先级作为误差范围的反比
            gap_error_range = 0.1 - (image_data['c_gap_filling_priority'] / 1000.0)

        # 获取行利用率阈值（默认为95%）
        row_utilization_threshold = 0.95
        if image_data and 'row_utilization_threshold' in image_data:
            row_utilization_threshold = image_data['row_utilization_threshold']
        elif image_data and 'c_high_utilization_threshold' in image_data:
            # 使用高利用率阈值
            row_utilization_threshold = image_data['c_high_utilization_threshold'] / 100.0

        # 记录日志
        if self.log_enabled:
            log.info(f"尝试填充空隙: 图片尺寸 {width}x{height}")

        # 创建skyline，记录每个x坐标的最高点
        skyline = [0] * (self.container_width + 1)
        for img in self.placed_images:
            img_left = img['x']
            img_right = img['x'] + img['width']
            img_bottom = img['y'] + img['height']
            for x in range(img_left, min(img_right, self.container_width)):
                skyline[x] = max(skyline[x], img_bottom)

        # 记录最佳空隙
        best_gap_score = -float('inf')
        best_gap_x = 0
        best_gap_y = 0
        best_gap_type = ""

        # 1. 首先尝试填充垂直空隙（优先级最高）
        # 查找skyline中的"山谷"，这些是垂直空隙
        for x in range(0, self.container_width - width + 1, max(1, width // 10)):  # 步长优化
            # 检查在x位置放置图片是否会超出容器边界
            if x + width > self.container_width:
                continue

            # 找到当前x位置下的最低可放置点
            y = 0
            for i in range(x, x + width):
                y = max(y, skyline[i])

            # 检查是否有足够的垂直空间
            if self.max_height_limit > 0 and y + height > self.max_height_limit:
                continue

            # 检查在(x,y)位置放置图片是否会与其他图片重叠
            if self._check_collision(x, y, width, height):
                continue

            # 检查是否是垂直空隙
            is_vertical_gap = False

            # 检查左侧和右侧是否有更高的图片
            left_higher = False
            right_higher = False

            # 检查左侧是否有更高的图片
            if x > 0:
                for i in range(max(0, x-width), x):
                    if skyline[i] > y + height * 0.5:  # 左侧至少有一半高度更高
                        left_higher = True
                        break

            # 检查右侧是否有更高的图片
            if x + width < self.container_width:
                for i in range(x + width, min(self.container_width, x + width * 2)):
                    if skyline[i] > y + height * 0.5:  # 右侧至少有一半高度更高
                        right_higher = True
                        break

            # 如果左侧或右侧有更高的图片，可能是垂直空隙
            is_vertical_gap = left_higher or right_higher

            # 计算空隙评分
            if is_vertical_gap:
                # 计算垂直空隙评分：优先考虑y值小的位置（更靠上）
                gap_score = 120 - y * 0.1  # 提高基础分数，更优先考虑垂直空隙

                # 如果左右两侧都有更高的图片，额外加分（更可能是垂直空隙）
                if left_higher and right_higher:
                    gap_score += 70  # 增加加分，更优先填充两侧都有图片的垂直空隙

                # 如果y值接近现有图片的底部，额外加分（填充垂直空隙）
                for img in self.placed_images:
                    img_bottom = img['y'] + img['height']
                    if abs(y - img_bottom) < 15:  # 增加接近阈值，更容易匹配到底部
                        gap_score += 50  # 增加加分，更优先填充图片底部的垂直空隙
                        break

                # 检查是否能形成完整的垂直列
                vertical_column_bonus = 0
                column_width = width
                column_x = x

                # 检查上方是否有图片
                has_image_above = False
                for img in self.placed_images:
                    if (img['x'] <= column_x < img['x'] + img['width'] or
                        img['x'] < column_x + column_width <= img['x'] + img['width'] or
                        column_x <= img['x'] < column_x + column_width):
                        if img['y'] + img['height'] <= y:
                            has_image_above = True
                            break

                # 检查下方是否有足够空间放置其他图片
                space_below = True
                max_height = self.get_max_height()
                if y + height >= max_height * 0.9:  # 如果接近画布底部，认为下方没有足够空间
                    space_below = False

                # 如果上方有图片且下方有空间，形成垂直列，给予额外奖励
                if has_image_above and space_below:
                    vertical_column_bonus = 40

                gap_score += vertical_column_bonus

                # 更新最佳空隙
                if gap_score > best_gap_score:
                    best_gap_score = gap_score
                    best_gap_x = x
                    best_gap_y = y
                    best_gap_type = "垂直空隙"

                    # 记录日志
                    if self.log_enabled:
                        log.info(f"找到垂直空隙: ({x}, {y}), 评分: {gap_score:.2f}")

        # 2. 然后尝试填充水平空隙
        # 按行分组已放置的图片
        rows = {}
        for img in self.placed_images:
            y = img['y']
            if y not in rows:
                rows[y] = []
            rows[y].append(img)

        # 遍历每一行
        for y, row_images in rows.items():
            # 按x坐标排序
            row_images.sort(key=lambda img: img['x'])

            # 计算行高度
            row_height = max(img['height'] + self.image_spacing for img in row_images)

            # 检查高度匹配度
            height_diff = abs(height - row_height) / max(height, row_height)
            if height_diff > gap_error_range * 2.0:  # 允许更大的高度误差
                continue

            # 找出行中的空隙
            gaps = []
            last_right = 0

            # 添加第一个图片前的空隙
            if row_images and row_images[0]['x'] > 0:
                gaps.append((0, row_images[0]['x']))

            # 添加图片之间的空隙
            for i in range(len(row_images) - 1):
                left_img = row_images[i]
                right_img = row_images[i + 1]
                gap_left = left_img['x'] + left_img['width'] + self.image_spacing
                gap_right = right_img['x']
                if gap_right > gap_left:
                    gaps.append((gap_left, gap_right))
                last_right = max(last_right, right_img['x'] + right_img['width'] + self.image_spacing)

            # 添加最后一个图片后的空隙
            if row_images:
                last_img = row_images[-1]
                last_right = last_img['x'] + last_img['width'] + self.image_spacing
                if last_right < self.container_width:
                    gaps.append((last_right, self.container_width))

            # 评估每个空隙
            for gap_left, gap_right in gaps:
                gap_width = gap_right - gap_left

                # 如果空隙宽度不足，跳过
                if gap_width < width:
                    continue

                # 检查在此位置放置图片是否会与其他图片重叠
                if self._check_collision(gap_left, y, width, height):
                    continue

                # 计算放置后的行利用率
                row_width = last_right
                current_utilization = row_width / self.container_width
                new_utilization = (row_width + width) / self.container_width

                # 计算空隙填充分数
                # 优先考虑能提高行利用率的空隙
                utilization_improvement = new_utilization - current_utilization

                # 优先考虑能提高整体利用率的空隙
                # 计算空隙填充后的剩余空间
                remaining_width = gap_width - width

                # 优先选择剩余空间小的空隙
                fit_score = 1.0 - (remaining_width / gap_width)

                # 计算垂直紧凑度评分
                vertical_compactness_score = 0

                # 检查上方是否有图片
                has_image_above = False
                for img in self.placed_images:
                    if (img['x'] <= gap_left < img['x'] + img['width'] or
                        img['x'] < gap_left + width <= img['x'] + img['width'] or
                        gap_left <= img['x'] < gap_left + width):
                        if img['y'] + img['height'] <= y:
                            has_image_above = True
                            # 计算垂直距离
                            vertical_distance = y - (img['y'] + img['height'])
                            # 距离越小，评分越高
                            vertical_compactness_score += max(0, 30 - vertical_distance * 0.5)
                            break

                # 检查下方是否有图片
                has_image_below = False
                for img in self.placed_images:
                    if (img['x'] <= gap_left < img['x'] + img['width'] or
                        img['x'] < gap_left + width <= img['x'] + img['width'] or
                        gap_left <= img['x'] < gap_left + width):
                        if img['y'] >= y + height:
                            has_image_below = True
                            # 计算垂直距离
                            vertical_distance = img['y'] - (y + height)
                            # 距离越小，评分越高
                            vertical_compactness_score += max(0, 30 - vertical_distance * 0.5)
                            break

                # 如果上下都有图片，形成垂直紧凑结构，给予额外奖励
                if has_image_above and has_image_below:
                    vertical_compactness_score += 40

                # 综合评分：增加垂直紧凑度的权重
                gap_score = utilization_improvement * 80 + fit_score * 40 + vertical_compactness_score

                # 如果是底部行，给予额外奖励
                if y >= self.max_height * 0.8:
                    gap_score += 30  # 增加底部行的奖励

                # 更新最佳空隙
                if gap_score > best_gap_score:
                    best_gap_score = gap_score
                    best_gap_x = gap_left
                    best_gap_y = y
                    best_gap_type = "水平空隙"

                    # 记录日志
                    if self.log_enabled:
                        log.info(f"找到水平空隙: ({gap_left}, {y}), 评分: {gap_score:.2f}")

        # 如果找到合适的空隙，返回位置
        if best_gap_score > -float('inf'):
            # 记录日志
            if self.log_enabled:
                log.info(f"选择最佳空隙: ({best_gap_x}, {best_gap_y}), 类型: {best_gap_type}, 评分: {best_gap_score:.2f}")
            return best_gap_x, best_gap_y, True

        # 如果没有找到合适的空隙，返回失败
        if self.log_enabled:
            log.info("未找到合适的空隙")
        return 0, 0, False

    def _find_best_position(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        找到最佳放置位置
        从左到右、从上到下扫描，找到第一个能放置的位置

        Args:
            width: 图片宽度（包含间距）
            height: 图片高度（包含间距）
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 如果没有已放置的图片，从左上角开始放置
        if not self.placed_images:
            return 0, 0, True

        # 获取当前所有行的高度
        row_heights = {}
        for img in self.placed_images:
            y = img['y']
            img_bottom = y + img['height'] + self.image_spacing
            row_heights[y] = max(row_heights.get(y, 0), img_bottom - y)

        # 获取当前所有行的右边界
        row_right_edges = {}
        for img in self.placed_images:
            y = img['y']
            img_right = img['x'] + img['width'] + self.image_spacing
            row_right_edges[y] = max(row_right_edges.get(y, 0), img_right)

        # 按y坐标排序行
        sorted_rows = sorted(row_heights.keys())

        # 尝试在现有行的右侧放置
        for y in sorted_rows:
            row_right = row_right_edges.get(y, 0)
            row_height = row_heights.get(y, 0)

            # 如果行右侧有足够空间
            if row_right + width <= self.container_width:
                # 检查是否可以放置
                if self._can_place_at(row_right, y, width, height):
                    return row_right, y, True

        # 如果无法在现有行放置，尝试创建新行
        # 找到最低的行底部
        max_bottom = 0
        for y, height in row_heights.items():
            bottom = y + height
            max_bottom = max(max_bottom, bottom)

        # 在最低行底部创建新行
        new_row_y = max_bottom

        # 检查是否可以放置
        if self._can_place_at(0, new_row_y, width, height):
            return 0, new_row_y, True

        # 如果无法放置，返回失败
        return 0, 0, False

    def _can_place_at(self, x: int, y: int, width: int, height: int) -> bool:
        """
        检查是否可以在指定位置放置图片
        使用网格优化来提高性能

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度（包含间距）
            height: 图片高度（包含间距）

        Returns:
            bool: 是否可以放置
        """
        # 检查是否超出容器边界
        if x < 0 or x + width > self.container_width:
            return False

        # 如果使用网格优化
        if self.use_grid_optimization and len(self.placed_images) > 10:  # 只有当图片数量较多时才使用网格优化
            try:
                # 计算当前矩形所在的网格单元格
                grid_x_start = x // self.grid_cell_size
                grid_x_end = (x + width) // self.grid_cell_size
                grid_y_start = y // self.grid_cell_size
                grid_y_end = (y + height) // self.grid_cell_size

                # 检查所有相关网格单元格中的图片
                for grid_x in range(grid_x_start, grid_x_end + 1):
                    for grid_y in range(grid_y_start, grid_y_end + 1):
                        grid_key = (grid_x, grid_y)
                        if grid_key in self.grid_cells:
                            for img_index in self.grid_cells[grid_key]:
                                # 防止索引错误
                                if img_index < 0 or img_index >= len(self.placed_images):
                                    continue

                                img = self.placed_images[img_index]
                                # 计算两个矩形的边界，应用碰撞边界缩小量
                                collision_margin = 0  # 不缩小碰撞边界

                                img_left = img['x'] + collision_margin
                                img_right = img['x'] + img['width'] + self.image_spacing - collision_margin
                                img_top = img['y'] + collision_margin
                                img_bottom = img['y'] + img['height'] + self.image_spacing - collision_margin

                                rect_left = x + collision_margin
                                rect_right = x + width - collision_margin
                                rect_top = y + collision_margin
                                rect_bottom = y + height - collision_margin

                                # 检查是否重叠，确保边界有效
                                if (img_right > img_left and img_bottom > img_top and
                                    rect_right > rect_left and rect_bottom > rect_top and
                                    not (rect_right <= img_left or rect_left >= img_right or
                                         rect_bottom <= img_top or rect_top >= img_bottom)):
                                    return False
            except Exception:
                # 如果网格优化出错，回退到传统方法
                pass
        else:
            # 使用传统方法检查是否与已放置的图片重叠
            for img in self.placed_images:
                # 计算两个矩形的边界，应用碰撞边界缩小量
                collision_margin = 0  # 不缩小碰撞边界

                img_left = img['x'] + collision_margin
                img_right = img['x'] + img['width'] + self.image_spacing - collision_margin
                img_top = img['y'] + collision_margin
                img_bottom = img['y'] + img['height'] + self.image_spacing - collision_margin

                rect_left = x + collision_margin
                rect_right = x + width - collision_margin
                rect_top = y + collision_margin
                rect_bottom = y + height - collision_margin

                # 检查是否重叠，确保边界有效
                if (img_right > img_left and img_bottom > img_top and
                    rect_right > rect_left and rect_bottom > rect_top and
                    not (rect_right <= img_left or rect_left >= img_right or
                         rect_bottom <= img_top or rect_top >= img_bottom)):
                    return False

        return True

    def _update_after_placement(self, x: int, y: int, width: int, height: int,
                               width_with_spacing: int, height_with_spacing: int,
                               image_data: Dict[str, Any], need_rotation: bool,
                               is_gap_fill: bool = False) -> None:
        """
        更新放置后的状态

        Args:
            x: x坐标
            y: y坐标
            width: 图片宽度
            height: 图片高度
            width_with_spacing: 图片宽度（包含间距）
            height_with_spacing: 图片高度（包含间距）
            image_data: 图片相关数据
            need_rotation: 是否需要旋转
            is_gap_fill: 是否是填充空隙
        """
        # 更新最大高度
        new_height = y + height_with_spacing

        # 获取最大高度限制（如果存在）
        max_height_limit = None
        if image_data and 'max_height_limit' in image_data:
            max_height_limit = image_data.get('max_height_limit')

        # 应用最大高度限制（如果存在）
        if max_height_limit is not None:
            self.max_height = min(max(self.max_height, new_height), max_height_limit)
        else:
            self.max_height = max(self.max_height, new_height)

        # 更新已放置图片信息
        img_index = len(self.placed_images)
        self.placed_images.append({
            'x': x,
            'y': y,
            'width': width,
            'height': height,
            'data': image_data,
            'is_gap_fill': is_gap_fill,
            'need_rotation': need_rotation
        })

        # 更新网格占用情况 - 使用稀疏矩阵优化
        # 只记录边界点，而不是所有点，减少内存占用
        for i in range(0, width_with_spacing, max(1, width_with_spacing // 10)):
            for j in range(0, height_with_spacing, max(1, height_with_spacing // 10)):
                self.grid[(x + i, y + j)] = True

        # 记录边界点
        for i in range(width_with_spacing):
            self.grid[(x + i, y)] = True  # 上边界
            self.grid[(x + i, y + height_with_spacing - 1)] = True  # 下边界

        for j in range(height_with_spacing):
            self.grid[(x, y + j)] = True  # 左边界
            self.grid[(x + width_with_spacing - 1, y + j)] = True  # 右边界

        # 更新网格单元格信息 - 用于碰撞检测优化
        if self.use_grid_optimization:
            # 计算图片所在的网格单元格
            grid_x_start = x // self.grid_cell_size
            grid_x_end = (x + width) // self.grid_cell_size
            grid_y_start = y // self.grid_cell_size
            grid_y_end = (y + height) // self.grid_cell_size

            # 更新网格单元格
            for grid_x in range(grid_x_start, grid_x_end + 1):
                for grid_y in range(grid_y_start, grid_y_end + 1):
                    grid_key = (grid_x, grid_y)
                    if grid_key not in self.grid_cells:
                        self.grid_cells[grid_key] = []
                    self.grid_cells[grid_key].append(img_index)

        # 更新行高度信息
        if y not in self.row_heights:
            self.row_heights[y] = height_with_spacing
        else:
            self.row_heights[y] = max(self.row_heights[y], height_with_spacing)

        # 更新行右边界信息
        if y not in self.row_right_edges:
            self.row_right_edges[y] = x + width_with_spacing
        else:
            self.row_right_edges[y] = max(self.row_right_edges[y], x + width_with_spacing)

    def redistribute_images_v2(self, max_time_seconds: float = 5.0, max_images_to_process: int = 100) -> bool:
        """
        重新分配图片位置，尝试填充中间空隙
        在所有图片放置完成后调用，优化整体布局
        这是一个简化版本，主要关注底部图片的移动

        Args:
            max_time_seconds: 最大执行时间（秒），超过此时间将提前返回
            max_images_to_process: 最大处理图片数量，超过此数量将只处理部分图片

        Returns:
            bool: 是否成功重新分配
        """
        if len(self.placed_images) < 2:
            return False

        # 记录开始时间
        start_time = time.time()

        try:
            # 清空网格单元格信息，避免索引错误
            self.grid_cells = {}

            # 按y坐标排序图片，从底部开始
            sorted_images = sorted(self.placed_images, key=lambda img: img['y'], reverse=True)

            # 限制处理的图片数量，避免处理时间过长
            # 优先处理底部30%区域的图片，这些图片对优化效果影响最大
            bottom_threshold = self.max_height * 0.7
            bottom_images = [img for img in sorted_images if img['y'] >= bottom_threshold]

            # 如果底部图片太多，只处理部分
            if len(bottom_images) > max_images_to_process:
                # 按面积降序排序，优先处理大图片
                bottom_images.sort(key=lambda img: img['width'] * img['height'], reverse=True)
                bottom_images = bottom_images[:max_images_to_process]

            # 使用筛选后的图片列表
            sorted_images = bottom_images

            # 记录原始布局
            original_layout = self.placed_images.copy()
            original_max_height = self.max_height

            # 尝试移动底部图片到中间空隙
            moved_count = 0
            # 从底部开始，尝试移动每个图片
            for img in sorted_images:
                # 检查是否超时
                if time.time() - start_time > max_time_seconds:
                    break

                # 获取图片信息
                img_x = img['x']
                img_y = img['y']
                img_width = img['width']
                img_height = img['height']
                img_width_with_spacing = img_width + self.image_spacing
                img_height_with_spacing = img_height + self.image_spacing

                # 暂时移除当前图片
                if img in self.placed_images:
                    self.placed_images.remove(img)

                # 尝试找到更好的位置
                try:
                    new_position = self._try_fill_gap(img_width_with_spacing, img_height_with_spacing, img.get('data', {}))

                    if not new_position[2]:  # 如果无法找到更好的位置
                        # 尝试从上到下找位置
                        new_position = self._find_best_position(img_width_with_spacing, img_height_with_spacing, img.get('data', {}))

                    if new_position[2]:  # 如果找到新位置
                        new_x, new_y, _ = new_position

                        # 如果新位置比原位置更好（更靠上或更靠左）
                        if new_y < img_y or (new_y == img_y and new_x < img_x):
                            # 更新图片位置
                            img['x'] = new_x
                            img['y'] = new_y
                            moved_count += 1
                except Exception as e:
                    # 如果出现错误，跳过当前图片
                    pass

                # 重新添加图片
                self.placed_images.append(img)

                # 检查是否超时
                if time.time() - start_time > max_time_seconds:
                    break
        except Exception as e:
            # 如果出现错误，恢复原始布局
            self.placed_images = original_layout
            self.max_height = original_max_height
            return False

        # 如果没有移动任何图片，恢复原始布局
        if moved_count == 0:
            self.placed_images = original_layout
            self.max_height = original_max_height
            return False

        # 重新计算最大高度
        self.max_height = 0
        max_height_limit = None

        # 尝试从图片数据中获取最大高度限制
        for img in self.placed_images:
            if img.get('data') and 'max_height_limit' in img.get('data', {}):
                max_height_limit = img.get('data', {}).get('max_height_limit')
                break

        # 计算新的最大高度
        for img in self.placed_images:
            new_height = img['y'] + img['height'] + self.image_spacing
            self.max_height = max(self.max_height, new_height)

        # 应用最大高度限制（如果存在）
        if max_height_limit is not None:
            self.max_height = min(self.max_height, max_height_limit)

        return True

    def get_max_height(self) -> int:
        """
        获取当前最大高度

        优化画布截断逻辑：
        1. 当图片超过画布最大高度时，以最大高度限制为画布高度
        2. 当全部图片的最后一张图片排列完成时，以画布最靠下的图片底部为画布高度
        3. 当底部只有一个图片时，特别是最后一批图片时，将其移动到下一画布
        4. 当所有图片都已排列完成时，无论画布底部是否还有空间，都以画布最底部图片的底部为画布高度
        5. 当画布底部剩余的水平空间已经无法放置任何新图片时，以画布最底部图片的底部为画布高度
        6. 当底部只有一个B类或C类图片时，根据智能判断决定是否移动到下一画布

        Returns:
            int: 当前最大高度
        """
        if not self.placed_images:
            return 0

        # 计算当前实际最大高度 - 以最底部图片的底边为截断高度
        current_max_height = 0
        for img in self.placed_images:
            img_bottom = img['y'] + img['height']
            current_max_height = max(current_max_height, img_bottom)

        # 更新当前最大高度
        self.current_max_height = current_max_height

        # 如果所有图片都已排列完成，直接返回当前最大高度
        # 这确保了当全部图片的最后一张图片排列完成时，以画布最靠下的图片底部为画布高度
        if self.is_all_images_arranged:
            if hasattr(self, 'log_signal') and self.log_signal:
                self.log_signal.emit(f"所有图片已排列完成，以当前最大高度 {current_max_height} 像素为画布高度")
            return current_max_height

        # 如果是最后一个画布，且已经放置了图片，检查是否还能放置更多图片
        if hasattr(self, 'is_last_canvas') and self.is_last_canvas and current_max_height > 0:
            # 检查底部是否只有一个图片
            is_single_bottom, bottom_image, image_class = self.is_single_image_at_bottom()

            # 如果底部只有一个B类或C类图片，进行智能判断
            if is_single_bottom and image_class in ['B', 'C']:
                # 获取底部图片宽度
                bottom_image_width = bottom_image['width']

                # 如果底部单图片宽度超过画布宽度的50%，考虑保留
                if bottom_image_width > self.container_width * 0.5:
                    if hasattr(self, 'log_signal') and self.log_signal:
                        self.log_signal.emit(f"底部单{image_class}类图片宽度较大 ({bottom_image_width}px，占画布宽度的{bottom_image_width/self.container_width:.2%})，保留在当前画布")
                    # 返回当前最大高度，确保以最底部图片的底边为截断高度
                    return current_max_height
                else:
                    # 底部单图片宽度较小，考虑移动到下一画布
                    if hasattr(self, 'log_signal') and self.log_signal:
                        self.log_signal.emit(f"底部单{image_class}类图片宽度较小 ({bottom_image_width}px，占画布宽度的{bottom_image_width/self.container_width:.2%})，考虑移动到下一画布")
                    # 返回底部图片的顶部位置作为截断高度
                    return bottom_image['y']

            # 检查底部是否还能放置更多图片
            if not self.can_place_more_images_at_bottom():
                if hasattr(self, 'log_signal') and self.log_signal:
                    self.log_signal.emit(f"底部无法放置更多图片，以当前最大高度 {current_max_height} 像素为画布高度")
                return current_max_height
            else:
                # 底部还能放置图片，不截断画布
                if hasattr(self, 'log_signal') and self.log_signal:
                    self.log_signal.emit(f"底部还能放置图片，不截断画布")
                # 如果有最大高度限制，返回最大高度限制；否则返回当前最大高度
                if self.max_height > 0:
                    return min(self.max_height, current_max_height)
                return current_max_height

        # 如果当前高度已经超过最大高度限制，则返回最大高度限制
        # 这确保了当图片超过画布最大高度时，以最大高度限制为画布高度
        if self.max_height > 0 and current_max_height > self.max_height:
            if hasattr(self, 'log_signal') and self.log_signal:
                self.log_signal.emit(f"当前高度 {current_max_height} 像素超过最大高度限制 {self.max_height} 像素，将以最大高度限制为画布高度")
            return self.max_height

        # 否则，返回当前高度
        # 这确保了当全部图片的最后一张图片排列完成时，以画布最靠下的图片底部为画布高度
        return current_max_height

    def is_single_image_at_bottom(self) -> Tuple[bool, Dict[str, Any], str]:
        """
        检查底部是否只有一个图片

        优化逻辑：
        1. 增加底部图片识别的容差范围，确保能准确识别底部单图片
        2. 使用统一的方法获取图片类别，确保类别信息一致
        3. 记录详细的日志信息，便于调试和分析

        Returns:
            Tuple[bool, Dict[str, Any], str]:
                - 是否底部只有一个图片
                - 底部图片对象（如果存在）
                - 底部图片类别（如果存在）
        """
        if not self.placed_images:
            return False, None, ""

        # 计算当前最大高度
        current_max_height = 0
        for img in self.placed_images:
            img_bottom = img['y'] + img['height']
            current_max_height = max(current_max_height, img_bottom)

        # 找出底部图片 - 增加容差范围，确保能准确识别底部图片
        bottom_images = []
        # 允许10像素的误差，增加容差范围以适应不同的图片尺寸
        bottom_y_threshold = current_max_height - 10

        for img in self.placed_images:
            img_bottom = img['y'] + img['height']
            if img_bottom >= bottom_y_threshold:
                bottom_images.append(img)

        # 如果底部只有一个图片
        if len(bottom_images) == 1:
            bottom_image = bottom_images[0]

            # 使用统一的方法获取图片类别
            image_class = self._get_image_class(bottom_image)

            if self.log_enabled:
                log.info(f"检测到底部只有一个图片，类别: {image_class}, 宽度: {bottom_image['width']}, 高度: {bottom_image['height']}, 位置: ({bottom_image['x']}, {bottom_image['y']})")

            return True, bottom_image, image_class
        elif len(bottom_images) > 1 and self.log_enabled:
            # 记录底部有多个图片的情况，便于调试
            log.info(f"底部有 {len(bottom_images)} 个图片，不是单图片情况")

            # 记录底部图片的详细信息
            for i, img in enumerate(bottom_images):
                img_class = self._get_image_class(img)
                log.info(f"底部图片 {i+1}: 类别: {img_class}, 宽度: {img['width']}, 高度: {img['height']}, 位置: ({img['x']}, {img['y']})")

        return False, None, ""

    def move_bottom_single_image(self) -> Tuple[bool, Dict[str, Any], int]:
        """
        移动底部单图片到下一画布

        此方法检查底部是否只有一个图片，如果是，则将其移除，并返回移除后的画布高度

        优化逻辑：
        1. 当底部只有一个图片时，特别是最后一批图片时，将其移动到下一画布
        2. 当底部单图片宽度超过画布宽度的80%，且不是最后一批图片时，考虑保留
        3. 当底部单图片是B类或C类图片时，根据智能判断决定是否移动

        Returns:
            Tuple[bool, Dict[str, Any], int]:
                - 是否成功移动
                - 移动的图片对象（如果成功）
                - 移动后的画布高度
        """
        # 检查底部是否只有一个图片
        is_single_bottom, bottom_image, bottom_image_class = self.is_single_image_at_bottom()

        # 如果底部没有单图片，返回失败
        if not is_single_bottom or not bottom_image:
            if self.log_enabled:
                log.info("底部没有单图片，无法移动")
            return False, None, self.get_max_height()

        # 获取底部图片信息
        bottom_image_width = bottom_image['width']
        bottom_image_height = bottom_image['height']

        # 记录日志
        if self.log_enabled:
            log.info(f"准备移动底部单{bottom_image_class}类图片，宽度: {bottom_image_width}px, 高度: {bottom_image_height}px")

        # 如果底部单图片宽度超过画布宽度的80%，且不是最后一批图片，考虑保留
        if bottom_image_width > self.container_width * 0.8 and not hasattr(self, 'is_last_canvas'):
            if self.log_enabled:
                log.info(f"底部单{bottom_image_class}类图片宽度较大 ({bottom_image_width}px，占画布宽度的{bottom_image_width/self.container_width:.2%})，决定保留")
            return False, None, self.get_max_height()

        # 如果是最后一批图片，或者底部单图片宽度较小，移动到下一画布
        # 从已放置图片列表中移除底部图片
        self.placed_images.remove(bottom_image)

        # 更新当前最大高度
        new_max_height = 0
        if self.placed_images:
            for img in self.placed_images:
                img_bottom = img['y'] + img['height']
                new_max_height = max(new_max_height, img_bottom)

        # 更新当前最大高度属性
        self.current_max_height = new_max_height

        # 设置底部图片已移动标志
        self.bottom_image_moved = True

        # 记录日志
        if self.log_enabled:
            log.info(f"成功移动底部单{bottom_image_class}类图片，新画布高度: {new_max_height}px")

        # 如果有日志信号，发送底部图片移动信息
        if hasattr(self, 'log_signal') and self.log_signal:
            self.log_signal.emit(f"底部单{bottom_image_class}类图片已移至下一画布")

        # 返回成功、移动的图片和新的画布高度
        return True, bottom_image, new_max_height

    def _get_image_class(self, image: Dict[str, Any]) -> str:
        """
        获取图片的类别，统一处理图片类别信息获取

        Args:
            image: 图片信息

        Returns:
            str: 图片类别，如'A', 'B', 'C'等，默认为'C'
        """
        try:
            # 按优先级顺序尝试从不同位置获取图片类别
            # 1. 首先尝试从data字典中获取
            if 'data' in image and isinstance(image['data'], dict):
                if 'image_class' in image['data']:
                    return image['data']['image_class']
                elif 'category' in image['data']:
                    return image['data']['category']

            # 2. 尝试直接从图片对象获取
            if 'image_class' in image:
                return image['image_class']
            elif 'category' in image:
                return image['category']

            # 3. 尝试从pattern中获取
            if 'pattern' in image and isinstance(image['pattern'], dict):
                pattern = image['pattern']
                if 'image_class' in pattern:
                    return pattern['image_class']
                elif 'category' in pattern:
                    return pattern['category']

            # 默认返回C类
            return 'C'
        except Exception as e:
            # 如果出现错误，记录日志并默认返回C类
            if self.log_enabled:
                log.warning(f"获取图片类别时出错: {str(e)}，默认返回C类")
            return 'C'

    def can_place_more_images_at_bottom(self) -> bool:
        """
        检查底部是否还能放置更多图片
        增强版：更精确地检测底部空间，处理特殊情况

        Returns:
            bool: 是否还能放置更多图片
        """
        if not self.placed_images:
            return True

        # 计算当前最大高度
        current_max_height = 0
        for img in self.placed_images:
            img_bottom = img['y'] + img['height']
            current_max_height = max(current_max_height, img_bottom)

        # 检查底部是否还有足够空间放置最小图片
        min_image_width = 10  # 最小图片宽度
        min_image_height = 10  # 最小图片高度

        # 检查是否超出最大高度限制
        if self.max_height > 0 and current_max_height + min_image_height > self.max_height:
            if self.log_enabled:
                log.info(f"底部高度 {current_max_height} + 最小图片高度 {min_image_height} 超过最大高度限制 {self.max_height}，无法放置更多图片")
            return False

        # 检查底部是否有足够宽度的空间
        # 获取底部所有已放置图片的水平范围
        bottom_ranges = []
        # 增加容差范围，确保能检测到所有底部图片
        bottom_y = current_max_height - min_image_height * 2  # 增加容差

        # 收集底部图片
        bottom_images = []
        for img in self.placed_images:
            img_bottom = img['y'] + img['height']
            if img_bottom > bottom_y:  # 如果图片底部在底部区域
                bottom_images.append(img)
                bottom_ranges.append((img['x'], img['x'] + img['width']))

        # 如果底部没有图片，可以放置
        if not bottom_images:
            return True

        # 合并重叠的范围
        if bottom_ranges:
            bottom_ranges.sort()
            merged = []
            for start, end in bottom_ranges:
                if not merged or start > merged[-1][1]:
                    merged.append((start, end))
                else:
                    merged[-1] = (merged[-1][0], max(merged[-1][1], end))

            # 检查是否有足够宽度的空间
            for i in range(len(merged)):
                if i == 0:
                    # 检查第一个范围之前的空间
                    if merged[i][0] >= min_image_width:
                        return True

                # 检查相邻范围之间的空间
                if i < len(merged) - 1:
                    if merged[i+1][0] - merged[i][1] >= min_image_width:
                        return True

            # 检查最后一个范围之后的空间
            if merged[-1][1] + min_image_width <= self.container_width:
                return True

            # 检查底部是否只有一个图片
            is_single_bottom, bottom_image, bottom_image_class = self.is_single_image_at_bottom()

            # 如果底部只有一个B类或C类图片，进行智能判断
            if is_single_bottom and bottom_image_class in ['B', 'C']:
                # 获取底部图片宽度
                bottom_image_width = bottom_image['width']

                # 如果底部单图片宽度超过画布宽度的50%，考虑保留
                if bottom_image_width > self.container_width * 0.5:
                    if self.log_enabled:
                        log.info(f"底部单{bottom_image_class}类图片宽度较大 ({bottom_image_width}px，占画布宽度的{bottom_image_width/self.container_width:.2%})，考虑保留")
                    return True

                # 计算底部剩余空间宽度
                remaining_width = self.container_width - bottom_image_width

                # 如果剩余空间足够大，可以放置更多图片
                if remaining_width >= min_image_width * 2:  # 至少能放置两个最小图片
                    if self.log_enabled:
                        log.info(f"底部单{bottom_image_class}类图片旁边有足够空间 ({remaining_width}px)，可以放置更多图片")
                    return True

            # 检查是否有B类图片组在底部
            has_b_class_group_at_bottom = False
            b_class_images_at_bottom = []

            for img in bottom_images:
                # 使用统一的方法获取图片类别
                img_class = self._get_image_class(img)

                if img_class == 'B':
                    b_class_images_at_bottom.append(img)
                    # 如果不是单个B类图片，则认为是B类图片组
                    if not (is_single_bottom and img == bottom_image):
                        has_b_class_group_at_bottom = True

            # 如果底部有B类图片组
            if has_b_class_group_at_bottom:
                # 计算B类图片组的总宽度
                b_group_width = sum(img['width'] for img in b_class_images_at_bottom)

                # 如果B类图片组宽度接近画布宽度，认为是完整的B类图片组
                if b_group_width >= self.container_width * 0.85:
                    if self.log_enabled:
                        log.info(f"底部有完整的B类图片组 (总宽度: {b_group_width}px，占画布宽度的{b_group_width/self.container_width:.2%})，无法放置更多图片")
                    return False
                else:
                    # 计算剩余空间
                    remaining_width = self.container_width - b_group_width

                    # 如果剩余空间足够大，可以放置更多图片
                    if remaining_width >= min_image_width:
                        if self.log_enabled:
                            log.info(f"底部B类图片组旁边有足够空间 ({remaining_width}px)，可以放置更多图片")
                        return True
                    else:
                        if self.log_enabled:
                            log.info(f"底部有B类图片组，且没有足够宽度的空间放置最小图片，无法放置更多图片")
                        return False

            if self.log_enabled:
                log.info(f"底部没有足够宽度的空间放置最小图片，无法放置更多图片")
            return False

        # 如果底部没有已放置的图片，则可以放置
        return True

    def get_placed_images(self) -> List[Dict[str, Any]]:
        """获取已放置的图片信息"""
        return self.placed_images

    def get_utilization(self) -> float:
        """
        计算画布利用率
        利用率 = (图片面积 + 间距面积 + 水平拓展面积) / 画布面积

        Returns:
            float: 利用率（0-1之间的浮点数）
        """
        if not self.placed_images:
            return 0.0

        # 计算图片面积
        image_area = sum(img['width'] * img['height'] for img in self.placed_images)

        # 计算间距面积 - 简化计算
        spacing_area = 0
        if len(self.placed_images) > 0 and self.image_spacing > 0:
            # 估算水平和垂直间距的总面积
            # 简化计算，使用图片数量和平均尺寸估算
            spacing_area = len(self.placed_images) * self.image_spacing * self.max_height * 0.1  # 简化估算

        # 计算水平拓展面积 - 从图片数据中获取水平拓展信息
        horizontal_expansion = 0
        for img in self.placed_images:
            if img.get('data') and 'horizontal_expansion_cm' in img.get('data', {}):
                # 如果找到水平拓展信息，就使用它
                horizontal_expansion_cm = img.get('data', {}).get('horizontal_expansion_cm', 0)
                # 假设PPI为72，将厘米转换为像素
                ppi = img.get('data', {}).get('ppi', 72)
                horizontal_expansion = horizontal_expansion_cm * ppi / 2.54
                break

        # 计算水平拓展面积
        expansion_area = horizontal_expansion * self.max_height if horizontal_expansion > 0 else 0

        # 计算总占用面积 = 图片面积 + 间距面积 + 水平拓展面积
        total_used_area = image_area + spacing_area + expansion_area

        # 计算画布面积
        canvas_area = self.container_width * self.max_height

        # 计算利用率
        if canvas_area > 0:
            utilization = total_used_area / canvas_area
            # 确保利用率不超过1.0
            return min(1.0, utilization)
        else:
            return 0.0

    def get_horizontal_utilization(self) -> Dict[int, float]:
        """
        计算每一行的水平利用率
        水平利用率 = 行中已放置图片总宽度 / 画布宽度
        重写父类方法，提供更精确的计算

        Returns:
            Dict[int, float]: 每一行的水平利用率，键为y坐标，值为利用率
        """
        if not self.placed_images:
            return {}

        # 按行分组已放置的图片
        rows = {}
        for img in self.placed_images:
            y = img['y']
            if y not in rows:
                rows[y] = []
            rows[y].append(img)

        # 计算每行的水平利用率
        horizontal_utilization = {}
        for y, row_images in rows.items():
            # 计算行中已放置图片的总宽度
            # 注意：这里需要考虑图片重叠的情况，所以不能简单相加
            # 使用一个数组记录每个x坐标是否被占用
            occupied = [False] * self.container_width
            for img in row_images:
                for x in range(img['x'], min(img['x'] + img['width'], self.container_width)):
                    occupied[x] = True

            # 计算已占用的宽度
            occupied_width = sum(1 for x in occupied if x)

            # 计算水平利用率
            horizontal_utilization[y] = occupied_width / self.container_width

        return horizontal_utilization

    def get_average_horizontal_utilization(self) -> float:
        """
        计算平均水平利用率

        Returns:
            float: 平均水平利用率
        """
        horizontal_utilization = self.get_horizontal_utilization()
        if not horizontal_utilization:
            return 0.0

        return sum(horizontal_utilization.values()) / len(horizontal_utilization)

    def place_image(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        放置图片，返回放置位置和是否成功

        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 调用find_position方法找到合适的位置
        return self.find_position(width, height, image_data)

    def find_position_for_a_class(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        为A类图片找到合适的位置，使用简化的算法
        A类图片默认横向放置，不需要复杂的计算，直接放在最底部

        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 性能监控开始
        start_time = time.time()
        self.placement_count += 1

        # 考虑图片间距
        width_with_spacing = width + self.image_spacing
        height_with_spacing = height + self.image_spacing

        # 记录日志
        if self.log_enabled:
            log.info(f"A类图片尺寸: {width}x{height}, 默认横向放置")

        # 如果没有已放置的图片，从左上角开始放置
        if not self.placed_images:
            # 更新已放置图片信息和网格
            self._update_after_placement(0, 0, width, height, width_with_spacing, height_with_spacing, image_data, False)

            # 性能监控结束
            self.placement_time += time.time() - start_time

            return 0, 0, True

        # 找到当前最大高度
        max_y = 0
        for img in self.placed_images:
            img_bottom = img['y'] + img['height'] + self.image_spacing
            max_y = max(max_y, img_bottom)

        # 检查是否超出最大高度限制
        max_height_limit = None
        if image_data and 'max_height_limit' in image_data:
            max_height_limit = image_data.get('max_height_limit')
            if max_height_limit is not None and max_y + height > max_height_limit:
                # 如果超出最大高度限制，返回失败
                return 0, 0, False

        # A类图片直接放在最底部
        x = 0
        y = max_y

        # 检查是否超出容器宽度
        if width > self.container_width:
            # 如果超出容器宽度，返回失败
            return 0, 0, False

        # 更新已放置图片信息和网格
        self._update_after_placement(x, y, width, height, width_with_spacing, height_with_spacing, image_data, False)

        # 记录日志
        if self.log_enabled:
            log.info(f"A类图片成功放置: ({x}, {y})")

        # 性能监控结束
        self.placement_time += time.time() - start_time

        return x, y, True

    def find_position_for_b_class(self, b_class_group: List[Dict[str, Any]]) -> List[Tuple[int, int, bool]]:
        """
        为B类图片组找到合适的位置，使用严格按组排列的算法
        B类图片按组排列，每组占据画布的一整行，不执行碰撞检测或空隙填充算法

        严格按照要求实现B类图片的排列策略：
        1. 约数边是高度（短边）时，图片需要旋转90度，一组排一行
        2. 约数边是宽度（长边）时，图片一组排一行，默认横着放，不用旋转
        3. 严格按照从大数组里取小数组，在画布上排列，不用空隙检测和填充逻辑

        B类图片排列规则：
        1. 旋转逻辑：
           - 约数边是高度（短边）时：图片需要旋转90度后排列（例如：130×40px的图片，约数边40px是短边，需旋转）
           - 约数边是宽度（长边）时：图片无需旋转（例如：80×45px的图片，约数边80px是长边，无需旋转）

        2. 排列方式：
           - 每组图片占据画布的一整行
           - 不进行碰撞检测或空隙填充
           - 严格按照二维数组中的顺序取出小数组并排列

        3. 宽度计算：
           - 使用原始画布宽度进行约数关系计算
           - 不使用水平拓展后的宽度

        注意：默认宽度(width)是长边，高度(height)是短边，默认图片为横向放置

        B类图片排列规则：
        1. 旋转逻辑：
           - 约数边是高度（短边）时：图片需要旋转90度后排列（例如：40×130px的图片，约数边40px是短边，需旋转）
           - 约数边是宽度（长边）时：图片无需旋转（例如：80×45px的图片，约数边80px是长边，无需旋转）

        2. 排列方式：
           - 每组图片占据画布的一整行
           - 不进行碰撞检测或空隙填充
           - 严格按照二维数组中的顺序取出小数组并排列
           - 同一组内的图片按宽度倒序排序

        3. 宽度计算：
           - 使用原始画布宽度进行约数关系计算
           - 不使用水平拓展后的宽度

        4. 换行处理：
           - 当一行放不下所有图片时，自动换行继续排列
           - 换行时更新最大高度，确保图片紧密排列

        5. 超宽图片处理：
           - 对于宽度超过画布宽度的图片，强制旋转或缩放
           - 确保超宽图片不会与其他图片重叠

        Args:
            b_class_group: B类图片组，包含具有相同约数关系的图片列表

        Returns:
            List[Tuple[int, int, bool]]: 每个图片的位置和是否成功的列表 [(x1, y1, success1), (x2, y2, success2), ...]
        """
        # 性能监控开始
        start_time = time.time()
        self.placement_count += 1

        # 添加超时机制，防止无限循环
        max_execution_time = 10.0  # 最大执行时间（秒），增加到10秒避免过早超时
        max_iterations = 2000      # 最大迭代次数，增加到2000次
        iteration_count = 0        # 迭代计数器

        # 如果组为空，直接返回空列表
        if not b_class_group:
            return []

        # 获取当前最大高度作为起始y坐标
        max_y = 0
        for img in self.placed_images:
            img_bottom = img['y'] + img['height'] + self.image_spacing
            max_y = max(max_y, img_bottom)

        # 获取组中第一个图片的信息
        first_pattern = b_class_group[0]
        need_rotation = first_pattern.get('need_rotation', False)
        divisor_edge = first_pattern.get('divisor_edge', 'width')
        divisor_edge_type = first_pattern.get('divisor_edge_type', 'unknown')
        is_last_b_group = first_pattern.get('is_last_b_group', False)

        # 记录约数边类型和旋转状态
        log.info(f"B类图片组约数边类型: {divisor_edge_type}，约数边: {divisor_edge}，旋转状态: {need_rotation}")

        # 获取最大高度限制
        max_height_limit = None
        for pattern in b_class_group:
            if 'max_height_limit' in pattern:
                max_height_limit = pattern.get('max_height_limit')
                break

        # 预先计算组中最高图片的高度，用于检查是否会超出最大高度限制
        max_group_height = 0
        for pattern in b_class_group:
            # 获取图片尺寸（像素）
            width_px = pattern.get('width_px', 0)
            height_px = pattern.get('height_px', 0)

            # 检查是否有像素尺寸，如果没有，尝试从厘米尺寸转换
            if width_px == 0 or height_px == 0:
                width_cm = pattern.get('width_cm', 0)
                height_cm = pattern.get('height_cm', 0)
                # 使用图片中的PPI，如果没有则使用默认值
                ppi = pattern.get('ppi', 72)
                width_px = int(width_cm * ppi / 2.54)
                height_px = int(height_cm * ppi / 2.54)

            # 获取旋转状态和约数边类型
            pattern_need_rotation = pattern.get('need_rotation', False)
            pattern_divisor_edge_type = pattern.get('divisor_edge_type', 'unknown')

            # 根据约数边类型和旋转状态确定最终尺寸
            # 获取约数边类型，但不使用此变量

            if pattern_divisor_edge_type == 'height':
                # 约数边是高度（短边），需要旋转90度
                # 确保旋转状态正确 - 约数边是高度（短边）时，需要旋转
                correct_rotation = True
                if pattern_need_rotation != correct_rotation:
                    log.info(f"警告: 图片 {pattern.get('pattern_name', '')} 约数边是高度(短边)，旋转状态为{pattern_need_rotation}，应为{correct_rotation}，进行修正")
                    pattern_need_rotation = correct_rotation
                    pattern['need_rotation'] = correct_rotation  # 更新图片的旋转状态

                # 根据旋转状态确定最终尺寸
                if pattern_need_rotation:
                    pattern_height = width_px  # 旋转后的高度是原来的宽度
                else:
                    pattern_height = height_px  # 不旋转，高度不变
            elif pattern_divisor_edge_type == 'width':
                # 约数边是宽度（长边），不需要旋转
                # 确保旋转状态正确 - 约数边是宽度（长边）时，不需要旋转
                correct_rotation = False
                if pattern_need_rotation != correct_rotation:
                    log.info(f"警告: 图片 {pattern.get('pattern_name', '')} 约数边是宽度(长边)，旋转状态为{pattern_need_rotation}，应为{correct_rotation}，进行修正")
                    pattern_need_rotation = correct_rotation
                    pattern['need_rotation'] = correct_rotation  # 更新图片的旋转状态

                # 根据旋转状态确定最终尺寸
                if pattern_need_rotation:
                    pattern_height = width_px  # 旋转后的高度是原来的宽度
                else:
                    pattern_height = height_px  # 不旋转，高度不变
            else:
                # 如果没有约数边类型信息，使用旋转状态判断
                if pattern_need_rotation:
                    pattern_height = width_px  # 旋转后的高度是原来的宽度
                else:
                    pattern_height = height_px  # 不旋转，高度不变

            # 更新组中最高图片的高度
            max_group_height = max(max_group_height, pattern_height)

        # 检查整个组是否会超出最大高度限制
        if max_height_limit is not None and max_y + max_group_height > max_height_limit:
            # 如果整个组会超出最大高度限制，所有图片都返回失败
            log.info(f"B类图片组超出最大高度限制: 当前高度={max_y}, 组高度={max_group_height}, 最大高度限制={max_height_limit}")
            log.info(f"B类图片组无法放置，将在新画布继续")
            return [(0, 0, False) for _ in b_class_group]

        # 计算整组图片的总宽度，用于检查是否能放在一行
        total_width = 0
        for pattern in b_class_group:
            # 获取图片尺寸（像素）
            width_px = pattern.get('width_px', 0)
            height_px = pattern.get('height_px', 0)

            # 检查是否有像素尺寸，如果没有，尝试从厘米尺寸转换
            if width_px == 0 or height_px == 0:
                width_cm = pattern.get('width_cm', 0)
                height_cm = pattern.get('height_cm', 0)
                # 使用图片中的PPI，如果没有则使用默认值
                ppi = pattern.get('ppi', 72)
                width_px = int(width_cm * ppi / 2.54)
                height_px = int(height_cm * ppi / 2.54)

            # 获取旋转状态
            need_rotation = pattern.get('need_rotation', False)

            # 根据旋转状态确定最终宽度
            if need_rotation:
                width = height_px
            else:
                width = width_px

            # 累加总宽度
            total_width += width + self.image_spacing

        # 检查是否是最后一个画布，且接近最大高度限制
        if hasattr(self, 'is_last_canvas') and self.is_last_canvas and max_height_limit is not None:
            # 如果当前高度加上组高度接近最大高度限制（例如，超过90%），且一行放不下整组图片
            if max_y + max_group_height > max_height_limit * 0.9 and total_width > self.container_width:
                # 计算每行能放置的图片数量
                images_per_row = self.container_width // ((total_width // len(b_class_group)) + 1)
                if images_per_row <= 1:  # 如果每行只能放一个图片，则整组图片都返回失败
                    log.info(f"B类图片组接近最大高度限制且每行只能放一个图片，将在新画布继续")
                    return [(0, 0, False) for _ in b_class_group]

        # 计算每个图片的位置
        positions = []
        current_x = 0

        # 对B类图片组按宽度降序排序
        sorted_patterns = sorted(b_class_group, key=lambda p: -p.get('width_cm', 0))

        # 替换原始组
        b_class_group = sorted_patterns

        # 记录日志
        log.info(f"B类图片组已按宽度降序排序")

        # 记录是否是最后一组B类图片
        if is_last_b_group:
            log.info(f"处理最后一组B类图片，共{len(b_class_group)}个图片")

        # 计算整组图片的总宽度，用于检查是否能放在一行
        total_width = 0
        for pattern in b_class_group:
            # 获取图片尺寸（像素）
            width_px = pattern.get('width_px', 0)
            height_px = pattern.get('height_px', 0)

            # 检查是否有像素尺寸，如果没有，尝试从厘米尺寸转换
            if width_px == 0 or height_px == 0:
                width_cm = pattern.get('width_cm', 0)
                height_cm = pattern.get('height_cm', 0)
                # 使用图片中的PPI，如果没有则使用默认值
                ppi = pattern.get('ppi', 72)
                width_px = int(width_cm * ppi / 2.54)
                height_px = int(height_cm * ppi / 2.54)

            # 获取旋转状态
            need_rotation = pattern.get('need_rotation', False)

            # 根据旋转状态确定最终宽度
            if need_rotation:
                width = height_px
            else:
                width = width_px

            # 累加总宽度
            total_width += width + self.image_spacing

        # 检查整组图片是否能放在当前行
        if total_width > self.container_width:
            log.info(f"B类图片组总宽度 {total_width} 超过容器宽度 {self.container_width}，需要分行处理")

        for pattern in b_class_group:
            # 检查超时和迭代次数
            iteration_count += 1
            current_time = time.time()
            elapsed_time = current_time - start_time

            if elapsed_time > max_execution_time:
                log.warning(f"B类图片处理超时: {elapsed_time:.2f}秒 > {max_execution_time}秒")
                # 返回所有剩余图片的失败状态
                return [(0, 0, True) for _ in positions] + [(0, 0, False) for _ in range(len(b_class_group) - len(positions))]

            if iteration_count > max_iterations:
                log.warning(f"B类图片处理迭代次数过多: {iteration_count} > {max_iterations}")
                # 返回所有剩余图片的失败状态
                return [(0, 0, True) for _ in positions] + [(0, 0, False) for _ in range(len(b_class_group) - len(positions))]

            # 获取图片尺寸（像素）
            width_px = pattern.get('width_px', 0)
            height_px = pattern.get('height_px', 0)

            # 检查是否有像素尺寸，如果没有，尝试从厘米尺寸转换
            if width_px == 0 or height_px == 0:
                width_cm = pattern.get('width_cm', 0)
                height_cm = pattern.get('height_cm', 0)
                # 使用图片中的PPI，如果没有则使用默认值
                ppi = pattern.get('ppi', 72)
                width_px = int(width_cm * ppi / 2.54)
                height_px = int(height_cm * ppi / 2.54)

            # 获取旋转状态和约数边信息
            need_rotation = pattern.get('need_rotation', False)
            divisor_edge = pattern.get('divisor_edge', 'width')
            divisor_edge_type = pattern.get('divisor_edge_type', '')
            pattern_name = pattern.get('pattern_name', '')

            # 根据约数边类型和旋转状态确定最终尺寸
            # 约数边是高度（短边）时，图片需要旋转90度
            # 约数边是宽度（长边）时，图片不需要旋转
            if divisor_edge_type == 'height':
                # 约数边是高度（短边），需要旋转90度
                # 确保旋转状态正确 - 约数边是高度（短边）时，需要旋转
                correct_rotation = True
                if need_rotation != correct_rotation:
                    log.info(f"警告: 图片 {pattern_name} 约数边是高度(短边)，旋转状态为{need_rotation}，应为{correct_rotation}，进行修正")
                    need_rotation = correct_rotation

                # 根据旋转状态确定最终尺寸
                if need_rotation:
                    width, height = height_px, width_px
                else:
                    width, height = width_px, height_px
            elif divisor_edge_type == 'width':
                # 约数边是宽度（长边），不需要旋转
                # 确保旋转状态正确 - 约数边是宽度（长边）时，不需要旋转
                correct_rotation = False
                if need_rotation != correct_rotation:
                    log.info(f"警告: 图片 {pattern_name} 约数边是宽度(长边)，旋转状态为{need_rotation}，应为{correct_rotation}，进行修正")
                    need_rotation = correct_rotation

                # 根据旋转状态确定最终尺寸
                if need_rotation:
                    width, height = height_px, width_px
                else:
                    width, height = width_px, height_px
            else:
                # 如果没有约数边类型信息，使用图片中的旋转标记
                if need_rotation:
                    width, height = height_px, width_px
                else:
                    width, height = width_px, height_px

                log.info(f"图片 {pattern_name} 没有约数边类型信息，使用旋转状态 {need_rotation} 确定尺寸")

            # 检查是否需要换行
            # 如果当前x坐标加上图片宽度超过容器宽度，则换行
            if current_x > 0 and current_x + width > self.container_width:
                # 检查是否是一组B类图片中的第一个图片
                is_first_in_group = pattern == b_class_group[0]

                # 获取当前已放置的图片数量
                current_row_images = len(positions)

                # 检查是否接近最大高度限制
                approaching_max_height = max_height_limit is not None and max_y + max_group_height > max_height_limit * 0.9

                # 如果接近最大高度限制，且当前行已经放置了一些图片，检查是否需要将整组剩余图片移到下一个画布
                if approaching_max_height and current_row_images > 0 and current_row_images < len(b_class_group):
                    # 计算剩余图片数量
                    remaining_images = len(b_class_group) - current_row_images

                    # 计算剩余图片的总宽度
                    remaining_width = 0
                    for i in range(current_row_images, len(b_class_group)):
                        p = b_class_group[i]
                        p_width_px = p.get('width_px', 0)
                        p_height_px = p.get('height_px', 0)
                        if p_width_px == 0 or p_height_px == 0:
                            p_width_cm = p.get('width_cm', 0)
                            p_height_cm = p.get('height_cm', 0)
                            ppi = p.get('ppi', 72)
                            p_width_px = int(p_width_cm * ppi / 2.54)
                            p_height_px = int(p_height_cm * ppi / 2.54)
                        p_need_rotation = p.get('need_rotation', False)
                        if p_need_rotation:
                            p_width = p_height_px
                        else:
                            p_width = p_width_px
                        remaining_width += p_width + self.image_spacing

                    # 如果剩余图片无法在一行内放置完，且剩余图片数量超过当前行已放置的图片数量，
                    # 则将整组图片移到下一个画布
                    if remaining_width > self.container_width and remaining_images > current_row_images:
                        log.info(f"B类图片组接近最大高度限制，且剩余图片无法在一行内放置完，将整组图片移到下一个画布")
                        # 返回所有图片的失败状态
                        return [(0, 0, False) for _ in b_class_group]

                # 如果不是第一个图片，且当前行已经放置了一些图片，则换行
                if not is_first_in_group:
                    # 换行，更新y坐标
                    max_y += max_group_height
                    current_x = 0
                    # 重新计算最大组高度
                    max_group_height = height + self.image_spacing
                    log.info(f"B类图片组换行: 当前x={current_x}, 图片宽度={width}, 容器宽度={self.container_width}, 新y={max_y}")

                    # 检查换行后是否会超出最大高度限制
                    if max_height_limit is not None and max_y + max_group_height > max_height_limit:
                        log.info(f"B类图片组换行后会超出最大高度限制: 当前高度={max_y}, 组高度={max_group_height}, 最大高度限制={max_height_limit}")
                        # 如果已经放置了一些图片，且剩余图片数量较多，则将整组图片移到下一个画布
                        if current_row_images > 0 and len(b_class_group) - current_row_images > current_row_images // 2:
                            log.info(f"B类图片组已放置 {current_row_images} 个图片，剩余 {len(b_class_group) - current_row_images} 个图片，将整组图片移到下一个画布")
                            # 返回所有图片的失败状态
                            return [(0, 0, False) for _ in b_class_group]
                else:
                    # 如果是第一个图片，且宽度超过容器宽度，则无法放置
                    log.info(f"B类图片宽度 {width} 超过容器宽度 {self.container_width}，无法放置")
                    positions.append((0, 0, False))
                    continue

            # 检查是否超出容器宽度（单个图片宽度超过容器宽度）
            if width > self.container_width:
                # 如果超出容器宽度，返回失败
                log.info(f"B类图片超出容器宽度: 图片宽度={width}, 容器宽度={self.container_width}")
                positions.append((0, 0, False))
                continue

            # 更新已放置图片信息和网格
            self._update_after_placement(
                current_x, max_y, width, height,
                width + self.image_spacing, height + self.image_spacing,
                pattern, need_rotation
            )

            # 记录放置信息
            log.info(f"B类图片 {pattern.get('pattern_name', '')} 放置在 ({current_x}, {max_y})，尺寸 {width}x{height}，旋转: {need_rotation}，约数边: {divisor_edge_type}")

            # 记录位置
            positions.append((current_x, max_y, True))

            # 更新当前x坐标
            current_x += width + self.image_spacing

            # 更新最大组高度
            max_group_height = max(max_group_height, height + self.image_spacing)

        # 性能监控结束
        self.placement_time += time.time() - start_time

        # 记录组高度和当前高度的日志
        log.info(f"B类图片组处理完成，组高度={max_group_height}，当前高度={max_y}")

        return positions

    def _create_grid(self) -> Tuple[List[List[bool]], int, int]:
        """
        创建二维网格表示画布占用情况

        Returns:
            Tuple[List[List[bool]], int, int]: (网格, 宽度, 高度)
        """
        grid_width = self.container_width
        grid_height = self.max_height

        # 使用稀疏表示优化内存使用
        if grid_height > 1000 or grid_width > 1000:
            # 对于大型画布，使用字典实现稀疏矩阵
            grid_dict = {}
            for img in self.placed_images:
                x = img['x']
                y = img['y']
                width = img['width']
                height = img['height']

                for i in range(x, min(x + width, grid_width)):
                    for j in range(y, min(y + height, grid_height)):
                        if 0 <= i < grid_width and 0 <= j < grid_height:
                            grid_dict[(j, i)] = True

            # 转换为二维数组
            grid = [[grid_dict.get((j, i), False) for i in range(grid_width)] for j in range(grid_height)]
        else:
            # 对于小型画布，直接使用二维数组
            grid = [[False for _ in range(grid_width)] for _ in range(grid_height)]

            # 标记已占用的区域
            for img in self.placed_images:
                x = img['x']
                y = img['y']
                width = img['width']
                height = img['height']

                for i in range(x, min(x + width, grid_width)):
                    for j in range(y, min(y + height, grid_height)):
                        if 0 <= i < grid_width and 0 <= j < grid_height:
                            grid[j][i] = True

        return grid, grid_width, grid_height

    def _find_gaps(self, grid: List[List[bool]], grid_width: int, grid_height: int, min_gap_size: int = 10) -> List[Dict[str, Any]]:
        """
        使用增强版连通区域算法找到所有空隙
        优化版本：更精确地识别各种形状的空隙，包括不规则空白区域

        Args:
            grid: 二维网格表示画布占用情况
            grid_width: 网格宽度
            grid_height: 网格高度
            min_gap_size: 最小空隙尺寸

        Returns:
            List[Dict[str, Any]]: 空隙列表
        """
        # 使用连通区域分析找出所有空隙
        connected_gaps = self._find_connected_gaps(grid, grid_width, grid_height, min_gap_size)

        # 使用最大矩形算法找出所有矩形空隙
        rectangle_gaps = self._find_rectangle_gaps(grid, grid_width, grid_height, min_gap_size)

        # 合并两种方法找到的空隙，去除重复
        all_gaps = self._merge_gaps(connected_gaps, rectangle_gaps)

        # 按面积降序排序
        all_gaps.sort(key=lambda g: -g['area'])

        return all_gaps

    def _find_connected_gaps(self, grid: List[List[bool]], grid_width: int, grid_height: int, min_gap_size: int = 10) -> List[Dict[str, Any]]:
        """
        使用连通区域算法找到所有空隙

        Args:
            grid: 二维网格表示画布占用情况
            grid_width: 网格宽度
            grid_height: 网格高度
            min_gap_size: 最小空隙尺寸

        Returns:
            List[Dict[str, Any]]: 空隙列表
        """
        all_gaps = []
        visited = [[False for _ in range(grid_width)] for _ in range(grid_height)]

        # 定义方向：上、右、下、左
        directions = [(-1, 0), (0, 1), (1, 0), (0, -1)]

        # 广度优先搜索找到连通区域
        for y in range(grid_height):
            for x in range(grid_width):
                if not grid[y][x] and not visited[y][x]:
                    # 找到一个空隙的起点
                    gap_cells = []
                    queue = [(y, x)]
                    visited[y][x] = True

                    while queue:
                        cy, cx = queue.pop(0)
                        gap_cells.append((cy, cx))

                        for dy, dx in directions:
                            ny, nx = cy + dy, cx + dx
                            if (0 <= ny < grid_height and 0 <= nx < grid_width and
                                not grid[ny][nx] and not visited[ny][nx]):
                                queue.append((ny, nx))
                                visited[ny][nx] = True

                    # 计算空隙的边界和属性
                    gap = self._calculate_gap_properties(gap_cells, grid_height)

                    # 只考虑足够大的空隙
                    if gap['width'] >= min_gap_size and gap['height'] >= min_gap_size:
                        all_gaps.append(gap)

        return all_gaps

    def _find_rectangle_gaps(self, grid: List[List[bool]], grid_width: int, grid_height: int, min_gap_size: int = 10) -> List[Dict[str, Any]]:
        """
        使用最大矩形算法找出所有矩形空隙

        Args:
            grid: 二维网格表示画布占用情况
            grid_width: 网格宽度
            grid_height: 网格高度
            min_gap_size: 最小空隙尺寸

        Returns:
            List[Dict[str, Any]]: 空隙列表
        """
        # 创建高度图，记录每个位置的连续空白高度
        height_map = [[0 for _ in range(grid_width)] for _ in range(grid_height)]

        # 计算每个位置的连续空白高度
        for x in range(grid_width):
            for y in range(grid_height):
                if not grid[y][x]:  # 如果该位置未被占用
                    height_map[y][x] = 1 + (height_map[y-1][x] if y > 0 else 0)

        # 找出所有最大矩形
        rectangle_gaps = []

        for y in range(grid_height):
            # 使用单调栈算法找出每一行的最大矩形
            stack = []  # (位置, 高度)

            for x in range(grid_width + 1):  # 多一个位置作为哨兵
                height = height_map[y][x] if x < grid_width else 0

                while stack and stack[-1][1] > height:
                    # 弹出栈顶元素
                    _, h = stack.pop()

                    # 计算矩形宽度
                    width = x - (stack[-1][0] + 1 if stack else 0)

                    # 计算矩形面积
                    area = width * h

                    # 如果矩形足够大，记录为空隙
                    if width >= min_gap_size and h >= min_gap_size:
                        left = stack[-1][0] + 1 if stack else 0
                        top = y - h + 1

                        # 判断空隙类型
                        gap_type = 'middle'
                        if top > grid_height * 0.7:  # 如果空隙在画布底部30%区域
                            gap_type = 'bottom'

                        rectangle_gaps.append({
                            'x': left,
                            'y': top,
                            'width': width,
                            'height': h,
                            'area': area,
                            'fill_ratio': 1.0,  # 矩形空隙的填充率为1
                            'type': gap_type
                        })

                stack.append((x, height))

        return rectangle_gaps

    def _merge_gaps(self, gaps1: List[Dict[str, Any]], gaps2: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        合并两个空隙列表，去除重复

        Args:
            gaps1: 第一个空隙列表
            gaps2: 第二个空隙列表

        Returns:
            List[Dict[str, Any]]: 合并后的空隙列表
        """
        # 复制第一个列表
        merged_gaps = gaps1.copy()

        # 遍历第二个列表
        for gap2 in gaps2:
            # 检查是否与第一个列表中的空隙重叠
            is_duplicate = False

            for gap1 in gaps1:
                # 如果两个空隙有显著重叠，认为是重复
                if (gap2['x'] < gap1['x'] + gap1['width'] and gap2['x'] + gap2['width'] > gap1['x'] and
                    gap2['y'] < gap1['y'] + gap1['height'] and gap2['y'] + gap2['height'] > gap1['y']):
                    # 计算重叠面积
                    overlap_width = min(gap2['x'] + gap2['width'], gap1['x'] + gap1['width']) - max(gap2['x'], gap1['x'])
                    overlap_height = min(gap2['y'] + gap2['height'], gap1['y'] + gap1['height']) - max(gap2['y'], gap1['y'])
                    overlap_area = overlap_width * overlap_height

                    # 如果重叠面积超过较小空隙面积的50%，认为是重复
                    smaller_area = min(gap2['area'], gap1['area'])
                    if overlap_area > smaller_area * 0.5:
                        is_duplicate = True
                        # 保留面积较大的空隙
                        if gap2['area'] > gap1['area']:
                            # 更新gap1的属性
                            for key, value in gap2.items():
                                gap1[key] = value
                        break

            # 如果不是重复，添加到合并列表
            if not is_duplicate:
                merged_gaps.append(gap2)

        return merged_gaps

    def _calculate_gap_properties(self, gap_cells: List[Tuple[int, int]], grid_height: int) -> Dict[str, Any]:
        """
        计算空隙的边界和属性

        Args:
            gap_cells: 空隙单元格列表
            grid_height: 网格高度

        Returns:
            Dict[str, Any]: 空隙属性
        """
        # 计算空隙的边界
        min_x = min(cx for _, cx in gap_cells)
        max_x = max(cx for _, cx in gap_cells) + 1
        min_y = min(cy for cy, _ in gap_cells)
        max_y = max(cy for cy, _ in gap_cells) + 1

        # 计算空隙的宽度和高度
        gap_width = max_x - min_x
        gap_height = max_y - min_y

        # 计算空隙的面积
        gap_area = len(gap_cells)

        # 计算空隙的填充率（实际空隙面积 / 边界矩形面积）
        fill_ratio = gap_area / (gap_width * gap_height)

        # 判断空隙类型
        gap_type = 'middle'
        if min_y > grid_height * 0.7:  # 如果空隙在画布底部30%区域
            gap_type = 'bottom'

        # 返回空隙信息
        return {
            'x': min_x,
            'y': min_y,
            'width': gap_width,
            'height': gap_height,
            'area': gap_area,
            'fill_ratio': fill_ratio,
            'type': gap_type,
            'cells': gap_cells
        }

    def _find_best_pattern_for_gap(self, gap: Dict[str, Any], remaining_patterns: List[Dict[str, Any]],
                                 successful_patterns: List[Dict[str, Any]]) -> Tuple[Dict[str, Any], bool, Tuple[int, int], float]:
        """
        找到最适合填充空隙的图片

        Args:
            gap: 空隙信息
            remaining_patterns: 剩余未放置的图片列表
            successful_patterns: 已成功放置的图片列表

        Returns:
            Tuple[Dict[str, Any], bool, Tuple[int, int], float]: (最佳图片, 是否旋转, 最佳位置, 适应度分数)
        """
        best_pattern = None
        best_fit_score = -float('inf')
        best_rotation = False
        best_position = (gap['x'], gap['y'])

        for pattern in remaining_patterns:
            if pattern in successful_patterns:
                continue  # 跳过已放置的图片

            # 获取图片尺寸
            width = pattern['width_px'] if 'width_px' in pattern else pattern.get('width', 0)
            height = pattern['height_px'] if 'height_px' in pattern else pattern.get('height', 0)

            # 考虑旋转
            for rotation in [False, True]:
                actual_width = height if rotation else width
                actual_height = width if rotation else height

                # 检查是否能放入空隙
                if actual_width <= gap['width'] and actual_height <= gap['height']:
                    # 使用自适应步长，确保不会跳过最优解
                    step_size = max(1, min(5, gap['width'] // 10, gap['height'] // 10))

                    # 尝试找到空隙内的最佳位置
                    for y_offset in range(0, gap['height'] - actual_height + 1, step_size):
                        for x_offset in range(0, gap['width'] - actual_width + 1, step_size):
                            test_x = gap['x'] + x_offset
                            test_y = gap['y'] + y_offset

                            # 检查是否与已放置的图片重叠
                            if not self._check_collision(test_x, test_y, actual_width, actual_height):
                                # 计算适应度分数
                                fit_score = self._calculate_fit_score(gap, actual_width, actual_height, test_x, test_y)

                                # 更新最佳匹配
                                if fit_score > best_fit_score:
                                    best_fit_score = fit_score
                                    best_pattern = pattern
                                    best_rotation = rotation
                                    best_position = (test_x, test_y)

        return best_pattern, best_rotation, best_position, best_fit_score

    def _calculate_fit_score(self, gap: Dict[str, Any], width: int, height: int, x: int, y: int) -> float:
        """
        计算图片在空隙中的适应度分数
        优化版本：更精确地评估图片与空隙的匹配度，考虑更多因素

        Args:
            gap: 空隙信息
            width: 图片宽度
            height: 图片高度
            x: 图片x坐标
            y: 图片y坐标

        Returns:
            float: 适应度分数
        """
        # 计算基本适应度指标
        width_ratio = width / gap['width']
        height_ratio = height / gap['height']
        area_ratio = (width * height) / gap['area']

        # 计算形状匹配度 - 考虑宽高比
        shape_ratio = min(width / height, height / width) / min(gap['width'] / gap['height'], gap['height'] / gap['width'])
        shape_match_score = shape_ratio * 0.5 + (width_ratio * height_ratio) * 0.5

        # 计算空间利用率 - 考虑剩余空间的可用性
        remaining_width = gap['width'] - width
        remaining_height = gap['height'] - height

        # 如果剩余空间太小，不太可能被其他图片利用，那么这是一个好的匹配
        small_waste_bonus = 0
        if remaining_width < 20 and remaining_width > 0:
            small_waste_bonus += 0.1
        if remaining_height < 20 and remaining_height > 0:
            small_waste_bonus += 0.1

        # 如果剩余空间足够大，可以放置其他图片，也是一个好的匹配
        large_space_bonus = 0
        if remaining_width >= 50 or remaining_height >= 50:
            large_space_bonus += 0.1

        # 底部空隙额外加分，促进扁平化布局
        bottom_bonus = 0
        if gap['type'] == 'bottom':
            bottom_bonus = 0.6  # 增加底部空隙的权重

        # 边缘接触评分 - 优先选择能与多个已放置图片接触的位置
        edge_contact_score = 0
        contact_count = 0

        # 检查八个方向的接触
        for dx, dy in [(0, -1), (1, -1), (1, 0), (1, 1), (0, 1), (-1, 1), (-1, 0), (-1, -1)]:  # 八个方向
            # 检查边缘接触
            for i in range(max(1, abs(dx) * width)):
                for j in range(max(1, abs(dy) * height)):
                    check_x = x + (0 if dx == 0 else (width if dx > 0 else 0)) + i * dx
                    check_y = y + (0 if dy == 0 else (height if dy > 0 else 0)) + j * dy

                    if self._is_occupied(check_x, check_y):
                        contact_count += 1
                        break
                else:
                    continue
                break

        # 根据接触点数量计算接触分数
        if contact_count > 0:
            edge_contact_score = min(0.3, contact_count * 0.05)  # 最多加0.3分

        # 优先选择能填满行的位置
        row_fill_bonus = 0
        if width_ratio > 0.95:  # 如果图片宽度接近空隙宽度
            row_fill_bonus = 0.2

        # 优先选择y坐标较小的位置，减少画布高度
        height_bonus = 0
        if y < self.current_max_height * 0.5:  # 如果在画布上半部分
            height_bonus = 0.1

        # 综合评分：
        # - 面积利用率（50%权重）：优先填充大部分空隙
        # - 形状匹配度（20%权重）：优先选择形状匹配的图片
        # - 边缘接触（15%权重）：优先选择能与多个已放置图片接触的位置
        # - 底部空隙（10%权重）：优先填充底部空隙
        # - 其他因素（5%权重）：小空间浪费、大空间利用、行填充、高度优化
        fit_score = (
            area_ratio * 0.5 +
            shape_match_score * 0.2 +
            edge_contact_score * 0.15 +
            bottom_bonus * 0.1 +
            (small_waste_bonus + large_space_bonus + row_fill_bonus + height_bonus) * 0.05
        )

        return fit_score

    def _place_pattern_in_gap(self, pattern: Dict[str, Any], rotation: bool, position: Tuple[int, int],
                            successful_patterns: List[Dict[str, Any]], grid: List[List[bool]],
                            grid_width: int, grid_height: int) -> None:
        """
        将图片放置到空隙中

        Args:
            pattern: 图片信息
            rotation: 是否旋转
            position: 放置位置
            successful_patterns: 成功放置的图片列表
            grid: 二维网格表示画布占用情况
            grid_width: 网格宽度
            grid_height: 网格高度
        """
        # 获取图片尺寸
        width = pattern['width_px'] if 'width_px' in pattern else pattern.get('width', 0)
        height = pattern['height_px'] if 'height_px' in pattern else pattern.get('height', 0)

        # 考虑旋转
        if rotation:
            width, height = height, width

        # 放置图片
        x, y = position

        # 检查是否与已放置的图片重叠
        if not self._check_collision(x, y, width, height):
            # 更新图片信息
            pattern['x'] = x
            pattern['y'] = y
            pattern['need_rotation'] = rotation

            # 添加到已放置图片列表
            width_with_spacing = width + self.image_spacing
            height_with_spacing = height + self.image_spacing
            self._update_after_placement(
                x, y, width, height,
                width_with_spacing, height_with_spacing,
                pattern, rotation, True
            )

            # 添加到成功放置列表
            successful_patterns.append(pattern)

            # 更新网格
            for i in range(x, min(x + width, grid_width)):
                for j in range(y, min(y + height, grid_height)):
                    if 0 <= i < grid_width and 0 <= j < grid_height:
                        grid[j][i] = True

    def _fill_gaps_with_exact_match(self, all_gaps: List[Dict[str, Any]], remaining_patterns: List[Dict[str, Any]],
                                  successful_patterns: List[Dict[str, Any]], grid: List[List[bool]],
                                  grid_width: int, grid_height: int) -> None:
        """
        尝试精确匹配空隙

        Args:
            all_gaps: 空隙列表
            remaining_patterns: 剩余未放置的图片列表
            successful_patterns: 成功放置的图片列表
            grid: 二维网格表示画布占用情况
            grid_width: 网格宽度
            grid_height: 网格高度
        """
        for gap in all_gaps:
            # 尝试找到最适合的图片填充空隙
            best_pattern, best_rotation, best_position, best_fit_score = self._find_best_pattern_for_gap(
                gap, remaining_patterns, successful_patterns)

            # 如果找到合适的图片，放置到空隙中
            if best_pattern and best_fit_score > 0.4:  # 降低适应度要求，允许更多图片放置
                self._place_pattern_in_gap(best_pattern, best_rotation, best_position,
                                         successful_patterns, grid, grid_width, grid_height)

    def _fill_gaps_with_combination(self, remaining_patterns: List[Dict[str, Any]],
                                   successful_patterns: List[Dict[str, Any]], grid: List[List[bool]],
                                   grid_width: int, grid_height: int) -> None:
        """
        尝试组合多个小图片填充剩余的大空隙
        优化版本：增强图片分组排列功能，确保相似尺寸的图片能够紧密排列在一起

        Args:
            remaining_patterns: 剩余未放置的图片列表
            successful_patterns: 成功放置的图片列表
            grid: 二维网格表示画布占用情况
            grid_width: 网格宽度
            grid_height: 网格高度
        """
        # 重新计算空隙
        remaining_gaps = self._find_gaps(grid, grid_width, grid_height, min_gap_size=10)

        # 按面积降序排序空隙
        remaining_gaps.sort(key=lambda g: -g['area'])

        # 对剩余图片进行分组，按照相似尺寸分组
        pattern_groups = self._group_patterns_by_size(remaining_patterns)

        # 尝试组合填充每个空隙
        for gap in remaining_gaps:
            # 如果空隙足够大，尝试组合多个小图片
            if gap['width'] >= 20 and gap['height'] >= 20 and gap['area'] >= 400:  # 最小阈值
                # 首先尝试使用相似尺寸的图片组填充
                filled = self._fill_gap_with_pattern_group(gap, pattern_groups, successful_patterns,
                                                        grid, grid_width, grid_height)

                # 如果使用图片组填充失败，尝试使用单个小图片填充
                if not filled:
                    self._fill_gap_with_small_patterns(gap, remaining_patterns, successful_patterns,
                                                     grid, grid_width, grid_height)

    def _group_patterns_by_size(self, patterns: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """
        按照相似尺寸对图片进行分组

        Args:
            patterns: 图片列表

        Returns:
            List[List[Dict[str, Any]]]: 分组后的图片列表
        """
        if not patterns:
            return []

        # 计算每个图片的尺寸和宽高比
        pattern_info = []
        for i, pattern in enumerate(patterns):
            width = pattern['width_px'] if 'width_px' in pattern else pattern.get('width', 0)
            height = pattern['height_px'] if 'height_px' in pattern else pattern.get('height', 0)

            # 确保宽度和高度有效
            if width <= 0 or height <= 0:
                continue

            # 计算宽高比和面积
            aspect_ratio = width / height
            area = width * height

            pattern_info.append({
                'index': i,
                'pattern': pattern,
                'width': width,
                'height': height,
                'aspect_ratio': aspect_ratio,
                'area': area
            })

        # 如果没有有效图片，返回空列表
        if not pattern_info:
            return []

        # 按照面积排序
        pattern_info.sort(key=lambda p: p['area'])

        # 使用聚类算法对图片进行分组
        groups = []
        visited = [False] * len(pattern_info)

        # 遍历所有图片
        for i in range(len(pattern_info)):
            if visited[i]:
                continue

            # 创建新组
            current_group = [pattern_info[i]['pattern']]
            visited[i] = True

            # 查找相似尺寸的图片
            for j in range(i + 1, len(pattern_info)):
                if visited[j]:
                    continue

                # 计算相似度
                area_ratio = min(pattern_info[i]['area'], pattern_info[j]['area']) / max(pattern_info[i]['area'], pattern_info[j]['area'])
                aspect_ratio_similarity = min(pattern_info[i]['aspect_ratio'], pattern_info[j]['aspect_ratio']) / max(pattern_info[i]['aspect_ratio'], pattern_info[j]['aspect_ratio'])

                # 如果面积和宽高比都相似，加入当前组
                if area_ratio > 0.7 and aspect_ratio_similarity > 0.7:
                    current_group.append(pattern_info[j]['pattern'])
                    visited[j] = True

                    # 限制每组最多10个图片，避免组过大
                    if len(current_group) >= 10:
                        break

            # 只有当组中有多个图片时才添加到结果中
            if len(current_group) > 1:
                groups.append(current_group)

        return groups

    def _fill_gap_with_pattern_group(self, gap: Dict[str, Any], pattern_groups: List[List[Dict[str, Any]]],
                                   successful_patterns: List[Dict[str, Any]], grid: List[List[bool]],
                                   grid_width: int, grid_height: int) -> bool:
        """
        使用相似尺寸的图片组填充空隙

        Args:
            gap: 空隙信息
            pattern_groups: 分组后的图片列表
            successful_patterns: 成功放置的图片列表
            grid: 二维网格表示画布占用情况
            grid_width: 网格宽度
            grid_height: 网格高度

        Returns:
            bool: 是否成功填充
        """
        # 如果没有图片组，返回失败
        if not pattern_groups:
            return False

        # 找到最适合空隙的图片组
        best_group = None
        best_fit_score = -float('inf')

        for group in pattern_groups:
            # 跳过已经全部放置的组
            if all(pattern in successful_patterns for pattern in group):
                continue

            # 计算组中图片的平均尺寸
            total_width = 0
            total_height = 0
            count = 0

            for pattern in group:
                if pattern in successful_patterns:
                    continue

                width = pattern['width_px'] if 'width_px' in pattern else pattern.get('width', 0)
                height = pattern['height_px'] if 'height_px' in pattern else pattern.get('height', 0)

                total_width += width
                total_height += height
                count += 1

            if count == 0:
                continue

            avg_width = total_width / count
            avg_height = total_height / count

            # 估算可以放置的图片数量
            estimated_cols = gap['width'] // (avg_width + self.image_spacing)
            estimated_rows = gap['height'] // (avg_height + self.image_spacing)
            estimated_count = estimated_cols * estimated_rows

            # 计算适应度分数
            # 优先选择能填满空隙的组
            fit_score = min(estimated_count / count, 1.0) * 100

            # 如果是底部空隙，给予额外奖励
            if gap['type'] == 'bottom':
                fit_score *= 1.2

            # 更新最佳组
            if fit_score > best_fit_score:
                best_fit_score = fit_score
                best_group = group

        # 如果找到合适的组，尝试放置
        if best_group and best_fit_score > 50:  # 只有当适应度分数足够高时才尝试放置
            # 过滤掉已放置的图片
            remaining_group = [pattern for pattern in best_group if pattern not in successful_patterns]

            # 按面积降序排序，优先放置大图片
            remaining_group.sort(key=lambda p: -(p['width_px'] if 'width_px' in p else p.get('width', 0)) *
                                            (p['height_px'] if 'height_px' in p else p.get('height', 0)))

            # 尝试放置组中的图片
            placed_count = 0

            # 使用网格布局放置图片
            current_x = gap['x']
            current_y = gap['y']
            row_height = 0

            for pattern in remaining_group:
                # 获取图片尺寸
                width = pattern['width_px'] if 'width_px' in pattern else pattern.get('width', 0)
                height = pattern['height_px'] if 'height_px' in pattern else pattern.get('height', 0)

                # 考虑旋转 - 更智能的旋转决策
                need_rotation = False

                # 计算旋转和不旋转的适应度
                non_rotated_fit = 0
                rotated_fit = 0

                # 不旋转的适应度
                if width <= gap['width'] - (current_x - gap['x']) and height <= gap['height'] - (current_y - gap['y']):
                    non_rotated_fit = (width * height) / ((gap['width'] - (current_x - gap['x'])) * (gap['height'] - (current_y - gap['y'])))

                # 旋转的适应度
                if height <= gap['width'] - (current_x - gap['x']) and width <= gap['height'] - (current_y - gap['y']):
                    rotated_fit = (width * height) / ((gap['width'] - (current_x - gap['x'])) * (gap['height'] - (current_y - gap['y'])))

                # 选择适应度更高的方式
                if rotated_fit > non_rotated_fit * 1.1:  # 旋转适应度至少高出10%
                    need_rotation = True
                    width, height = height, width

                # 检查是否需要换行
                if current_x + width > gap['x'] + gap['width']:
                    # 换行
                    current_x = gap['x']
                    current_y += row_height + self.image_spacing
                    row_height = 0

                # 检查是否超出空隙边界
                if current_y + height > gap['y'] + gap['height']:
                    break

                # 检查是否与已放置的图片重叠
                if not self._check_collision(current_x, current_y, width, height):
                    # 放置图片
                    self._update_after_placement(
                        current_x, current_y, width, height,
                        width + self.image_spacing, height + self.image_spacing,
                        pattern, need_rotation
                    )

                    # 更新网格
                    for i in range(current_y, min(current_y + height, grid_height)):
                        for j in range(current_x, min(current_x + width, grid_width)):
                            if 0 <= i < grid_height and 0 <= j < grid_width:
                                grid[i][j] = True

                    # 添加到成功放置列表
                    successful_patterns.append(pattern)
                    placed_count += 1

                    # 更新当前位置和行高
                    current_x += width + self.image_spacing
                    row_height = max(row_height, height)

            # 如果成功放置了至少一个图片，返回成功
            return placed_count > 0

        return False

    def _fill_gap_with_small_patterns(self, gap: Dict[str, Any], remaining_patterns: List[Dict[str, Any]],
                                    successful_patterns: List[Dict[str, Any]], grid: List[List[bool]],
                                    grid_width: int, grid_height: int) -> None:
        """
        使用多个小图片填充大空隙

        Args:
            gap: 空隙信息
            remaining_patterns: 剩余未放置的图片列表
            successful_patterns: 成功放置的图片列表
            grid: 二维网格表示画布占用情况
            grid_width: 网格宽度
            grid_height: 网格高度
        """
        # 按面积升序排序剩余图片，优先使用小图片
        small_patterns = sorted(
            [p for p in remaining_patterns if p not in successful_patterns and
             (p['width_px'] if 'width_px' in p else p.get('width', 0)) *
             (p['height_px'] if 'height_px' in p else p.get('height', 0)) <
             gap['area'] * 0.7],  # 只考虑面积小于空隙70%的图片
            key=lambda p: (p['width_px'] if 'width_px' in p else p.get('width', 0)) *
                         (p['height_px'] if 'height_px' in p else p.get('height', 0))
        )

        # 尝试组合填充
        current_x = gap['x']
        current_y = gap['y']
        row_height = 0

        # 使用贪心算法填充空隙
        for pattern in small_patterns[:]:  # 使用副本遍历，避免修改原列表
            if pattern in successful_patterns:
                continue  # 跳过已放置的图片

            # 获取图片尺寸
            width = pattern['width_px'] if 'width_px' in pattern else pattern.get('width', 0)
            height = pattern['height_px'] if 'height_px' in pattern else pattern.get('height', 0)

            # 考虑旋转 - 更智能的旋转决策
            need_rotation = False

            # 计算旋转和不旋转的适应度
            non_rotated_fit = 0
            rotated_fit = 0

            # 不旋转的适应度
            if width <= gap['width'] - (current_x - gap['x']) and height <= gap['height'] - (current_y - gap['y']):
                non_rotated_fit = (width * height) / ((gap['width'] - (current_x - gap['x'])) * (gap['height'] - (current_y - gap['y'])))

            # 旋转的适应度
            if height <= gap['width'] - (current_x - gap['x']) and width <= gap['height'] - (current_y - gap['y']):
                rotated_fit = (height * width) / ((gap['width'] - (current_x - gap['x'])) * (gap['height'] - (current_y - gap['y'])))

            # 选择适应度更高的旋转状态
            if rotated_fit > non_rotated_fit:
                width, height = height, width
                need_rotation = True

            # 检查是否需要换行
            if current_x + width > gap['x'] + gap['width']:
                current_x = gap['x']
                current_y += row_height + self.image_spacing
                row_height = 0

            # 检查是否超出空隙高度
            if current_y + height > gap['y'] + gap['height']:
                # 尝试在当前行找到更小的图片
                continue

            # 检查是否与已放置的图片重叠
            if not self._check_collision(current_x, current_y, width, height):
                # 更新图片信息
                pattern['x'] = current_x
                pattern['y'] = current_y
                pattern['need_rotation'] = need_rotation

                # 添加到已放置图片列表
                width_with_spacing = width + self.image_spacing
                height_with_spacing = height + self.image_spacing
                self._update_after_placement(
                    current_x, current_y, width, height,
                    width_with_spacing, height_with_spacing,
                    pattern, need_rotation, True
                )

                # 添加到成功放置列表
                successful_patterns.append(pattern)

                # 更新网格
                for i in range(current_x, min(current_x + width, grid_width)):
                    for j in range(current_y, min(current_y + height, grid_height)):
                        if 0 <= i < grid_width and 0 <= j < grid_height:
                            grid[j][i] = True

                # 更新当前位置
                current_x += width + self.image_spacing
                row_height = max(row_height, height)

    def _try_fill_bottom_gap(self, remaining_patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        尝试填充底部空隙，提高画布利用率

        Args:
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            List[Dict[str, Any]]: 成功放置的图片列表
        """
        # 如果没有已放置的图片或剩余图片，直接返回
        if not self.placed_images or not remaining_patterns:
            return []

        # 创建网格
        grid, grid_width, grid_height = self._create_grid()

        # 使用连通区域算法找到所有空隙
        all_gaps = self._find_gaps(grid, grid_width, grid_height, min_gap_size=5)

        # 按面积降序排序空隙，优先填充大空隙
        all_gaps.sort(key=lambda g: -g['area'])

        # 尝试填充每个空隙
        successful_patterns = []

        # 第一轮：尝试精确匹配空隙
        successful_patterns = []
        self._fill_gaps_with_exact_match(all_gaps, remaining_patterns, successful_patterns, grid, grid_width, grid_height)

        # 第二轮：尝试组合多个小图片填充剩余的大空隙
        self._fill_gaps_with_combination(remaining_patterns, successful_patterns, grid, grid_width, grid_height)

        return successful_patterns

    # ==================== 测试模式支持 ====================

    def create_test_mode_canvas(self, canvas_width_px: int, canvas_height_px: int,
                               canvas_name: str, miniature_ratio: float = 0.02) -> bool:
        """
        创建测试模式画布（matplotlib模式）

        Args:
            canvas_width_px: 画布宽度（像素）
            canvas_height_px: 画布高度（像素）
            canvas_name: 画布名称
            miniature_ratio: 缩小比率

        Returns:
            bool: 是否创建成功
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches

            # 设置中文字体
            self._setup_chinese_font()

            # 计算缩放后的尺寸
            scaled_width = max(1, int(canvas_width_px * miniature_ratio))
            scaled_height = max(1, int(canvas_height_px * miniature_ratio))

            # 限制最大尺寸
            max_size = 2000
            if scaled_width > max_size or scaled_height > max_size:
                scale_factor = min(max_size / scaled_width, max_size / scaled_height)
                scaled_width = max(1, int(scaled_width * scale_factor))
                scaled_height = max(1, int(scaled_height * scale_factor))
                miniature_ratio *= scale_factor

            # 创建图形和轴
            self.test_fig, self.test_ax = plt.subplots(figsize=(scaled_width/100, scaled_height/100), dpi=100)

            # 设置画布属性
            self.test_ax.set_xlim(0, scaled_width)
            self.test_ax.set_ylim(0, scaled_height)
            self.test_ax.set_aspect('equal')

            # 设置白色背景
            self.test_ax.set_facecolor('white')
            self.test_fig.patch.set_facecolor('white')

            # 移除坐标轴
            self.test_ax.set_xticks([])
            self.test_ax.set_yticks([])

            # 添加边框
            border = patches.Rectangle((0, 0), scaled_width, scaled_height,
                                     linewidth=2, edgecolor='black', facecolor='none')
            self.test_ax.add_patch(border)

            # 保存画布信息
            self.test_canvas_name = canvas_name
            self.test_miniature_ratio = miniature_ratio
            self.test_scaled_width = scaled_width
            self.test_scaled_height = scaled_height

            if self.log_signal:
                self.log_signal.emit(f"Tetris测试模式画布创建成功: {canvas_name} ({scaled_width}x{scaled_height}px)")

            return True

        except ImportError:
            if self.log_signal:
                self.log_signal.emit("matplotlib库不可用，无法创建测试模式画布")
            return False
        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"创建 Tetris 测试模式画布失败: {str(e)}")
            return False

    def _setup_chinese_font(self):
        """
        设置中文字体，解决中文乱码问题 - 增强版
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.font_manager as fm
            import platform
            import os

            # 根据操作系统选择合适的中文字体
            system = platform.system()

            if system == 'Windows':
                # Windows系统中文字体路径和名称
                font_configs = [
                    ('SimHei', ['C:/Windows/Fonts/simhei.ttf']),
                    ('Microsoft YaHei', ['C:/Windows/Fonts/msyh.ttc', 'C:/Windows/Fonts/msyh.ttf']),
                    ('SimSun', ['C:/Windows/Fonts/simsun.ttc', 'C:/Windows/Fonts/simsun.ttf']),
                    ('KaiTi', ['C:/Windows/Fonts/simkai.ttf']),
                    ('Microsoft JhengHei', ['C:/Windows/Fonts/msjh.ttc']),
                ]
            elif system == 'Darwin':  # macOS
                font_configs = [
                    ('PingFang SC', ['/System/Library/Fonts/PingFang.ttc']),
                    ('Heiti SC', ['/System/Library/Fonts/STHeiti Light.ttc']),
                    ('STHeiti', ['/System/Library/Fonts/STHeiti Medium.ttc']),
                    ('Arial Unicode MS', ['/Library/Fonts/Arial Unicode.ttf']),
                ]
            else:  # Linux
                font_configs = [
                    ('WenQuanYi Micro Hei', ['/usr/share/fonts/truetype/wqy/wqy-microhei.ttc']),
                    ('Droid Sans Fallback', ['/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf']),
                    ('Noto Sans CJK SC', ['/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc']),
                ]

            # 尝试设置字体
            font_set = False
            working_font = None

            for font_name, font_paths in font_configs:
                # 检查字体文件是否存在
                font_exists = any(os.path.exists(path) for path in font_paths)

                if font_exists:
                    try:
                        # 尝试设置字体
                        plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False

                        # 测试字体是否能正确显示中文
                        test_fig, test_ax = plt.subplots(figsize=(1, 1))
                        test_ax.text(0.5, 0.5, '测试', fontsize=12)
                        plt.close(test_fig)

                        font_set = True
                        working_font = font_name
                        if self.log_signal:
                            self.log_signal.emit(f"Tetris成功设置中文字体: {font_name}")
                        break

                    except Exception as e:
                        if self.log_signal:
                            self.log_signal.emit(f"Tetris字体 {font_name} 设置失败: {str(e)}")
                        continue

            if not font_set:
                # 如果都失败了，尝试系统默认字体
                try:
                    # 获取系统中所有可用的中文字体
                    available_fonts = [f.name for f in fm.fontManager.ttflist]
                    chinese_fonts = [f for f in available_fonts if any(keyword in f for keyword in
                                   ['SimHei', 'SimSun', 'Microsoft', 'YaHei', 'PingFang', 'Heiti', 'WenQuanYi', 'Noto'])]

                    if chinese_fonts:
                        plt.rcParams['font.sans-serif'] = chinese_fonts[:3] + ['DejaVu Sans']
                        plt.rcParams['axes.unicode_minus'] = False
                        working_font = chinese_fonts[0]
                        font_set = True
                        if self.log_signal:
                            self.log_signal.emit(f"Tetris使用系统检测到的中文字体: {working_font}")
                    else:
                        # 最后的备选方案
                        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False
                        if self.log_signal:
                            self.log_signal.emit("Tetris警告: 未找到中文字体，使用默认字体，中文可能显示为方块")

                except Exception as e:
                    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                    plt.rcParams['axes.unicode_minus'] = False
                    if self.log_signal:
                        self.log_signal.emit(f"Tetris字体检测失败: {str(e)}，使用默认字体")

            # 清除matplotlib字体缓存，确保新设置生效
            try:
                fm._rebuild()
            except:
                pass

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"Tetris设置中文字体失败: {str(e)}")

    def place_test_mode_image(self, image_info: Dict[str, Any]) -> bool:
        """
        在测试模式画布上放置色块图片

        Args:
            image_info: 图片信息

        Returns:
            bool: 是否放置成功
        """
        try:
            if not hasattr(self, 'test_fig') or self.test_fig is None:
                if self.log_signal:
                    self.log_signal.emit("Tetris测试模式画布未创建")
                return False

            import matplotlib.patches as patches

            # 获取图片信息
            x = image_info.get('x', 0)
            y = image_info.get('y', 0)
            width = image_info.get('width', 0)
            height = image_info.get('height', 0)
            name = image_info.get('name', 'Unknown')
            image_class = image_info.get('image_class', 'C')
            rotated = image_info.get('need_rotation', False)

            # 应用缩放
            scaled_x = x * self.test_miniature_ratio
            scaled_y = y * self.test_miniature_ratio
            scaled_width = max(1, width * self.test_miniature_ratio)
            scaled_height = max(1, height * self.test_miniature_ratio)

            # 根据图片类别选择颜色（Tetris主要处理C类图片）
            if image_class == 'A':
                color = (1.0, 0.4, 0.4)  # 红色
            elif image_class == 'B':
                color = (0.4, 1.0, 0.4)  # 绿色
            else:  # C类或其他（Tetris主要处理）
                color = (0.4, 0.4, 1.0)  # 蓝色

            # 绘制矩形
            rectangle = patches.Rectangle(
                (scaled_x, scaled_y),
                scaled_width,
                scaled_height,
                linewidth=1,
                edgecolor='black',
                facecolor=color,
                alpha=0.8
            )

            # 添加到轴
            self.test_ax.add_patch(rectangle)

            # 绘制文本（如果空间足够）
            if scaled_width > 30 and scaled_height > 15:
                try:
                    display_name = f"{name}{'(R)' if rotated else ''}"
                    text_x = scaled_x + scaled_width / 2
                    text_y = scaled_y + scaled_height / 2

                    # 计算合适的字体大小
                    fontsize = min(scaled_width / len(display_name) * 1.5, scaled_height * 0.3, 12)
                    fontsize = max(fontsize, 6)

                    self.test_ax.text(
                        text_x, text_y,
                        display_name,
                        ha='center',
                        va='center',
                        fontsize=fontsize,
                        color='white',
                        weight='bold',
                        bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7)
                    )
                except Exception:
                    pass  # 忽略文本错误

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"Tetris测试模式放置图片失败: {str(e)}")
            return False

    def save_test_mode_canvas(self, output_path: str) -> bool:
        """
        保存测试模式画布为JPG文件

        Args:
            output_path: 输出路径

        Returns:
            bool: 是否保存成功
        """
        try:
            if not hasattr(self, 'test_fig') or self.test_fig is None:
                if self.log_signal:
                    self.log_signal.emit("Tetris测试模式画布未创建")
                return False

            import matplotlib.pyplot as plt
            import os

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 保存为高质量JPG格式
            self.test_fig.savefig(
                output_path,
                format='jpeg',
                dpi=150,
                bbox_inches='tight',
                pad_inches=0.1,
                facecolor='white',
                edgecolor='none'
            )

            # 关闭图形以释放内存
            plt.close(self.test_fig)

            if self.log_signal:
                self.log_signal.emit(f"Tetris测试模式画布已保存: {output_path}")

            return True

        except Exception as e:
            if self.log_signal:
                self.log_signal.emit(f"Tetris保存测试模式画布失败: {str(e)}")
            return False

    def _is_occupied(self, x: int, y: int) -> bool:
        """
        检查指定位置是否被占用

        Args:
            x: x坐标
            y: y坐标

        Returns:
            bool: 是否被占用
        """
        # 检查是否超出容器边界
        if x < 0 or x >= self.container_width or y < 0:
            return True

        # 检查是否与已放置的图片重叠
        for img in self.placed_images:
            img_left = img['x']
            img_right = img['x'] + img['width']
            img_top = img['y']
            img_bottom = img['y'] + img['height']

            if (x >= img_left and x < img_right and
                y >= img_top and y < img_bottom):
                return True

        return False

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计信息
        """
        stats = {
            'placement_count': self.placement_count,
            'placement_time': self.placement_time,
            'avg_placement_time': self.placement_time / max(1, self.placement_count),
            'gap_fill_count': self.gap_fill_count,
            'gap_fill_time': self.gap_fill_time,
            'avg_gap_fill_time': self.gap_fill_time / max(1, self.gap_fill_count),
            'grid_cells_count': len(self.grid_cells),
            'grid_points_count': len(self.grid),
            'placed_images_count': len(self.placed_images)
        }

        return stats

    def reset(self):
        """
        重置俄罗斯方块式装箱器，清空所有已放置的图片和网格
        """
        # 清空已放置的图片
        self.placed_images = []

        # 清空网格
        self.grid = {}
        self.row_heights = {}
        self.row_right_edges = {}

        # 清空网格单元格
        self.grid_cells = {}

        # 重置性能监控
        self.placement_count = 0
        self.placement_time = 0
        self.gap_fill_count = 0
        self.gap_fill_time = 0
        self.drop_simulation_count = 0
        self.drop_simulation_time = 0

        # 重置当前最大高度，但保留最大高度限制
        self.current_max_height = 0

        # 重置画布已满标志
        self.canvas_is_full = False

        if self.log_enabled:
            log.info("俄罗斯方块式装箱器已重置")

    def get_current_height(self) -> int:
        """
        获取当前画布高度

        此方法调用统一的画布截断处理函数，获取截断后的画布高度

        优化逻辑：
        1. 当所有图片都已排列完成时，无论画布底部是否还有空间，都以画布最底部图片的底部为画布高度
        2. 当画布底部剩余的水平空间已经无法放置任何新图片时，以画布最底部图片的底部为画布高度
        3. 当底部只有一个图片时，将其移到下一个画布，以上一行图片的底部为画布高度
        4. 只有在底部放不下任何图片时，才以画布最靠下图片的底部为画布高度

        Returns:
            int: 当前画布高度（像素）
        """
        if not self.placed_images:
            # 如果没有放置图片，返回最小高度
            return 100  # 最小高度为100像素

        try:
            # 找到最低的图片底部
            max_bottom = 0
            for img in self.placed_images:
                if 'y' in img and 'height' in img:
                    bottom = img['y'] + img['height']
                    max_bottom = max(max_bottom, bottom)

            # 检查高度是否合理
            if max_bottom <= 0:
                log.warning("警告: 计算的画布高度为0或负值，使用默认最小高度")
                return 100  # 最小高度为100像素

            # 更新当前最大高度
            self.current_max_height = max_bottom

            # 使用统一画布截断处理函数获取截断高度
            try:
                # 导入统一画布截断处理函数
                from core.unified_canvas_truncation import unified_canvas_truncation

                # 调用统一画布截断处理函数获取截断高度
                truncation_result = unified_canvas_truncation.truncate_canvas(
                    self,
                    remaining_patterns=[],
                    is_last_batch=self.is_all_images_arranged if hasattr(self, 'is_all_images_arranged') else False
                )

                if truncation_result.get('success', False):
                    truncation_height = truncation_result.get('height', 0)
                    if truncation_height > 0:
                        log.info(f"使用统一画布截断处理函数获取的截断高度: {truncation_height} 像素")
                        log.info(f"截断操作: {truncation_result.get('action', '未知')}, 原因: {truncation_result.get('message', '未知')}")
                        return truncation_height
            except Exception as e:
                log.warning(f"使用统一画布截断处理函数获取截断高度失败: {str(e)}")

            # 如果统一画布截断处理函数失败，使用默认逻辑
            # 检查高度是否超过最大限制
            if self.max_height > 0 and max_bottom > self.max_height:
                log.warning(f"警告: 计算的画布高度 ({max_bottom}) 超过最大限制 ({self.max_height})，将使用最大限制值")
                return self.max_height

            # 添加一些底部边距
            padding = 50  # 底部边距（像素）

            # 确保不超过最大高度限制
            if self.max_height > 0:
                return min(max_bottom + padding, self.max_height)

            return max_bottom + padding

        except Exception as e:
            # 捕获任何异常，确保不会崩溃
            log.error(f"计算画布高度时出错: {str(e)}")
            return 100  # 出错时返回默认高度

    def find_position_for_a_class(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        为A类图片找到合适的位置
        A类图片通常是宽度接近或等于画布宽度的图片，直接放置在当前最低点

        Args:
            width: 图片宽度（像素）
            height: 图片高度（像素）
            image_data: 图片相关数据

        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        # 如果宽度超过容器宽度，无法放置
        if width > self.container_width:
            if self.log_enabled:
                log.warning(f"A类图片宽度 {width}px 超过容器宽度 {self.container_width}px，无法放置")
            return 0, 0, False

        # 找到当前最低点
        y = 0
        if self.placed_images:
            # 找到最低的图片底部
            max_bottom = 0
            for img in self.placed_images:
                bottom = img['y'] + img['height']
                max_bottom = max(max_bottom, bottom)

            y = max_bottom

        # 检查是否超出最大高度限制
        if self.max_height > 0 and y + height > self.max_height:
            if self.log_enabled:
                log.warning(f"A类图片放置位置 y={y} 加上高度 {height}px 超过最大高度限制 {self.max_height}px，无法放置")
            return 0, 0, False

        # 放置在画布左侧
        x = 0

        # 检查是否与已放置的图片重叠
        if self._check_collision(x, y, width, height):
            if self.log_enabled:
                log.warning(f"A类图片在位置 ({x}, {y}) 与已放置的图片重叠，无法放置")
            return 0, 0, False

        # 更新已放置图片信息和网格
        need_rotation = False
        if image_data and 'need_rotation' in image_data:
            need_rotation = image_data['need_rotation']

        self._update_after_placement(x, y, width, height, width + self.image_spacing, height + self.image_spacing, image_data, need_rotation)

        if self.log_enabled:
            log.info(f"成功放置A类图片: ({x}, {y}), 尺寸: {width}x{height}")

        return x, y, True
