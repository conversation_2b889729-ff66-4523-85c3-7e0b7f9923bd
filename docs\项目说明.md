# DeAI-智能排版 项目说明

## 1. 项目概述

DeAI-智能排版是一款专为设计师和印刷行业开发的智能排版应用程序，旨在解决大量图片排版的效率问题。该应用能够自动创建指定宽度的画布，从图库中检索图片并进行智能排列，最终保存为高质量的TIFF格式文件，大幅提高设计和排版效率。

## 2. 项目目标

- **提高排版效率**: 将手动排版工作自动化，节省大量时间
- **优化空间利用**: 通过先进算法最大化画布空间利用率
- **简化工作流程**: 提供一站式解决方案，从图片检索到最终输出
- **保证专业质量**: 与Photoshop无缝集成，确保专业级输出质量
- **降低技术门槛**: 简单易用的界面，降低使用门槛

## 3. 项目结构

```
robot_ps_smart/
├── core/                   # 核心算法和功能模块
│   ├── excel_processor.py      # Excel处理器
│   ├── image_classifier.py     # 图像分类器
│   ├── image_indexer_duckdb.py # 图像索引器
│   └── tetris_packer.py        # 俄罗斯方块式算法
├── docs/                   # 文档目录
│   ├── config_system.md        # 配置系统说明
│   ├── 技术架构.md              # 技术架构文档
│   ├── 项目说明.md              # 项目说明文档
│   └── 产品说明.md              # 产品说明文档
├── ui/                     # 用户界面组件
│   ├── index_library_worker.py # 索引库工作线程
│   ├── layout_worker.py        # 布局工作线程
│   ├── password_dialog.py      # 密码对话框
│   ├── retrieve_images_worker.py # 检索图像工作线程
│   └── settings_dialog.py      # 设置对话框
├── utils/                  # 工具类和辅助功能
│   ├── advanced_settings_auth.py # 高级设置授权
│   ├── config_manager_duckdb.py  # 配置管理器
│   ├── json_to_duckdb_migrator.py # JSON到DuckDB迁移工具
│   ├── log_file_creator.py       # 日志文件创建器
│   ├── photoshop_helper.py       # Photoshop辅助工具
│   └── supabase_helper.py        # Supabase云端同步
├── robot_ps_smart_app.py   # 应用主程序
└── README.md               # 项目说明文件
```

## 4. 核心功能模块

### 4.1 图像索引与检索

- **图库索引**: 扫描图像库，建立索引数据库
- **快速检索**: 根据图案名称快速检索匹配图像
- **模糊匹配**: 支持模糊匹配和精确匹配两种模式
- **批量处理**: 支持大规模图库的批量处理

### 4.2 Excel材质表处理

- **多格式支持**: 支持各种Excel格式的材质表
- **数据提取**: 自动提取图案名称、数量等信息
- **错误处理**: 智能处理格式错误和缺失数据
- **批量处理**: 支持多个Excel文件的批量处理

### 4.3 图像分类与排列

- **智能分类**: 将图像分为A类、B类和C类
- **A类图像**: 使用增强版装箱算法，单独占一行
- **B类图像**: 使用增强版装箱算法，按组排列
- **C类图像**: 使用俄罗斯方块式算法，最大化利用空间，包含超宽图片处理优化

### 4.4 Photoshop集成

- **自动连接**: 自动检测和连接Photoshop
- **画布创建**: 创建指定尺寸的高质量画布
- **图像处理**: 自动调整图像大小和位置
- **高质量输出**: 保存为专业级TIFF格式文件

### 4.5 配置管理

- **DuckDB存储**: 使用DuckDB数据库存储配置
- **云端同步**: 从Supabase云端同步配置
- **类型安全**: 支持类型安全的配置管理
- **配置迁移**: 支持从旧版JSON配置迁移

## 5. 技术实现

### 5.1 图像排列算法

项目实现了两种核心排列算法：

1. **增强版二维装箱算法 (EnhancedBinPacker)**
   - 智能行策略，根据图像尺寸决定排列方式
   - 前瞻性决策，考虑未来图像进行优化
   - 自适应布局，根据图像特性调整策略
   - 高空间利用率，减少画布高度

2. **俄罗斯方块式算法 (TetrisPacker)**
   - 最大化水平利用率，类似俄罗斯方块
   - 智能旋转决策，根据空间情况决定是否旋转
   - 空隙填充，识别并填充现有行中的空隙
   - 智能重分配，优化整体布局
   - 超宽图片处理，自动旋转或缩放超宽图片
   - 旋转优先级动态调整，对于接近画布宽度的图片自动提高旋转优先级
   - 碰撞检测优化，防止超宽图片被错误放置

### 5.2 数据库设计

项目使用DuckDB作为嵌入式数据库，具有以下优势：

- **高性能**: 分析型数据库，适合大量数据查询
- **嵌入式**: 无需单独安装数据库服务
- **列式存储**: 适合图像索引和配置管理
- **SQL支持**: 支持标准SQL查询

### 5.3 多线程架构

项目采用多线程设计，确保UI响应性和后台任务高效执行：

- **索引线程**: 处理图像库索引任务
- **检索线程**: 处理图像检索和Excel处理
- **布局线程**: 处理图像排列和Photoshop交互

### 5.4 云端集成

项目与Supabase云平台集成，提供以下功能：

- **配置同步**: 从云端获取最新配置
- **版本检查**: 确保应用版本最新
- **高级设置授权**: 通过云端验证授权

## 6. 开发环境

### 6.1 开发工具

- **IDE**: Visual Studio Code / PyCharm
- **版本控制**: Git (Gitee)
- **依赖管理**: pip

### 6.2 技术栈

- **编程语言**: Python 3.8+
- **UI框架**: PyQt6
- **数据处理**: Pandas, DuckDB
- **图像处理**: Photoshop Python API
- **云服务**: Supabase

### 6.3 依赖项

```
# 基本依赖
pip install pillow numpy pyqt6 pandas duckdb

# Photoshop API依赖
pip install photoshop-python-api

# 云服务依赖
pip install supabase
```

## 7. 部署说明

### 7.1 系统要求

- **操作系统**: Windows 10/11 (64位)
- **Python版本**: Python 3.8+
- **内存**: 至少8GB RAM，推荐16GB或更高
- **存储空间**: 至少1GB可用空间（不包括图库存储）
- **可选**: Adobe Photoshop CS3 ~ CC 2024版本

### 7.2 安装步骤

1. 安装Python 3.8+
2. 安装基础依赖
   ```bash
   pip install pillow numpy pyqt6 pandas duckdb
   ```
3. 安装Photoshop API依赖
   ```bash
   pip install photoshop-python-api
   ```
4. 克隆仓库
   ```bash
   git clone https://gitee.com/your-username/robot_ps_smart.git
   ```
5. 进入项目目录
   ```bash
   cd robot_ps_smart
   ```
6. 运行应用
   ```bash
   python robot_ps_smart_app.py
   ```

### 7.3 配置说明

应用首次启动时会创建默认配置，可通过"高级设置"按钮进行修改：

- **画布设置**: 画布宽度、PPI、图片间距等
- **Photoshop设置**: 是否使用Photoshop、保存格式等
- **排列设置**: 精确查询模式、算法参数等

## 8. 版本历史

| 版本 | 日期 | 主要更新 |
|------|------|----------|
| 1.0.0 | 2025-04-15 | 初始版本发布 |
| 1.1.0 | 2025-04-20 | 添加DuckDB支持 |
| 1.2.0 | 2025-04-21 | 增强版装箱算法 |
| 1.3.0 | 2025-04-23 | 俄罗斯方块式算法 |
| 1.4.0 | 2025-04-24 | Supabase云端集成 |
| 1.5.0 | 2025-04-25 | 性能优化和UI改进 |

## 9. 未来计划

- **AI增强**: 集成机器学习算法，提供更智能的排版建议
- **批处理优化**: 进一步优化大批量图片处理性能
- **多语言支持**: 添加英语等多语言界面
- **云端存储**: 支持云端存储和共享排版结果
- **插件系统**: 开发插件系统，支持功能扩展

## 10. 贡献指南

### 10.1 代码规范

- 遵循PEP 8编码规范
- 使用类型注解提高代码可读性
- 编写详细的文档字符串
- 保持模块的高内聚低耦合

### 10.2 提交流程

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 等待代码审查和合并

### 10.3 问题报告

如发现问题，请通过以下方式报告：

- 在Gitee上提交Issue
- 详细描述问题和复现步骤
- 如可能，提供截图或日志信息
