#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的RectPack改进验证脚本
"""

import os
import sys
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

def test_imports():
    """测试导入"""
    try:
        from managers.rectpack_manager import RectPackManager
        log.info("✓ RectPackManager 导入成功")
        
        from managers.photoshop_integration_manager import PhotoshopIntegrationManager
        log.info("✓ PhotoshopIntegrationManager 导入成功")
        
        from managers.task_manager import TaskManager
        log.info("✓ TaskManager 导入成功")
        
        return True
    except Exception as e:
        log.error(f"✗ 导入失败: {str(e)}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        # 模拟配置管理器
        class MockConfig:
            def get_rectpack_settings(self):
                return {'use_rectpack_algorithm': True}
            def get_test_mode_settings(self):
                return {'is_test_mode': True}
        
        config = MockConfig()
        
        # 测试RectPackManager
        from managers.rectpack_manager import RectPackManager
        rectpack_mgr = RectPackManager(config)
        log.info("✓ RectPackManager 初始化成功")
        
        # 测试PhotoshopIntegrationManager
        from managers.photoshop_integration_manager import PhotoshopIntegrationManager
        ps_mgr = PhotoshopIntegrationManager(config)
        success = ps_mgr.initialize_connection(test_mode=True)
        if success:
            log.info("✓ PhotoshopIntegrationManager 测试模式连接成功")
        else:
            log.warning("⚠ PhotoshopIntegrationManager 连接失败")
        
        # 测试TaskManager
        from managers.task_manager import TaskManager
        task_mgr = TaskManager(config)
        log.info("✓ TaskManager 初始化成功")
        
        return True
        
    except Exception as e:
        log.error(f"✗ 基本功能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    log.info("开始简单验证...")
    
    # 测试导入
    if not test_imports():
        return False
    
    # 测试基本功能
    if not test_basic_functionality():
        return False
    
    log.info("🎉 简单验证通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
