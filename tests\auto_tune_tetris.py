#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
俄罗斯方块算法自动调优脚本
根据测试结果自动调优算法参数，优化水平利用率和代码效率
"""

import os
import sys
import time
import json
import random
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.patches import Rectangle
from typing import List, Dict, Tuple, Any, Optional
import logging

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入俄罗斯方块算法
from core.tetris_packer import TetrisPacker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("TetrisAutoTuner")

class TetrisAutoTuner:
    """俄罗斯方块算法自动调优器"""

    def __init__(self,
                 canvas_width: int = 1600,
                 image_spacing: int = 5,
                 max_height: int = 0,
                 seed: Optional[int] = None,
                 test_mode: bool = False):
        """
        初始化自动调优器

        Args:
            canvas_width: 画布宽度（像素）
            image_spacing: 图片间距（像素）
            max_height: 最大高度限制（0表示无限制）
            seed: 随机种子，用于生成可重复的测试数据
            test_mode: 测试模式，使用更少的图片和更简单的算法
        """
        self.canvas_width = canvas_width
        self.image_spacing = image_spacing
        self.max_height = max_height
        self.test_mode = test_mode

        # 设置随机种子
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)

        # 记录测试结果
        self.test_results = []

        # 记录最佳参数
        self.best_params = None
        self.best_score = -float('inf')

        log.info(f"初始化自动调优器:")
        log.info(f"  - 画布宽度: {self.canvas_width}像素")
        log.info(f"  - 图片间距: {self.image_spacing}像素")
        log.info(f"  - 最大高度限制: {self.max_height if self.max_height > 0 else '无限制'}像素")
        log.info(f"  - 测试模式: {'启用' if self.test_mode else '禁用'}")

    def generate_test_images(self, count: int = 50) -> List[Dict[str, Any]]:
        """
        生成测试图片集合，包含多种类型的图片

        Args:
            count: 图片总数

        Returns:
            List[Dict[str, Any]]: 图片列表
        """
        images = []
        image_id = 1

        # 1. 小图片（20%）
        small_count = max(1, int(count * 0.2))
        for i in range(small_count):
            width = random.randint(20, 80)
            height = random.randint(20, 80)

            image = {
                'id': f"small_{image_id}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'small'
            }

            images.append(image)
            image_id += 1

        # 2. 中等图片（40%）
        medium_count = max(1, int(count * 0.4))
        for i in range(medium_count):
            width = random.randint(80, 150)
            height = random.randint(80, 150)

            image = {
                'id': f"medium_{image_id}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'medium'
            }

            images.append(image)
            image_id += 1

        # 3. 大图片（20%）
        large_count = max(1, int(count * 0.2))
        for i in range(large_count):
            width = random.randint(150, 300)
            height = random.randint(150, 300)

            image = {
                'id': f"large_{image_id}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'large'
            }

            images.append(image)
            image_id += 1

        # 4. 极端宽高比图片（20%）
        extreme_count = max(1, int(count * 0.2))
        for i in range(extreme_count):
            if random.random() < 0.5:
                # 极宽图片
                width = random.randint(200, 400)
                height = random.randint(20, 50)
            else:
                # 极高图片
                width = random.randint(20, 50)
                height = random.randint(200, 400)

            image = {
                'id': f"extreme_{image_id}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'extreme'
            }

            images.append(image)
            image_id += 1

        # 打乱图片顺序
        random.shuffle(images)

        log.info(f"生成了 {len(images)} 个测试图片")
        log.info(f"  - 小图片: {small_count}个")
        log.info(f"  - 中等图片: {medium_count}个")
        log.info(f"  - 大图片: {large_count}个")
        log.info(f"  - 极端宽高比图片: {extreme_count}个")

        return images

    def load_images(self, input_path: str) -> List[Dict[str, Any]]:
        """
        从JSON文件加载图片数据

        Args:
            input_path: 输入文件路径

        Returns:
            List[Dict[str, Any]]: 图片列表
        """
        with open(input_path, 'r', encoding='utf-8') as f:
            images = json.load(f)

        log.info(f"从 {input_path} 加载了 {len(images)} 个图片")

        return images

    def test_parameters(self,
                       images: List[Dict[str, Any]],
                       horizontal_priority: int = 80,
                       gap_filling_priority: int = 70,
                       rotation_priority: int = 60) -> Dict[str, Any]:
        """
        测试特定参数组合的效果

        Args:
            images: 图片列表
            horizontal_priority: 水平优先级（百分比）
            gap_filling_priority: 空隙填充优先级（百分比）
            rotation_priority: 旋转优先级（百分比）

        Returns:
            Dict[str, Any]: 测试结果
        """
        # 创建俄罗斯方块算法实例
        packer = TetrisPacker(
            container_width=self.canvas_width,
            image_spacing=self.image_spacing,
            max_height=self.max_height
        )

        # 设置算法参数
        packer.horizontal_priority = horizontal_priority
        packer.gap_filling_priority = gap_filling_priority
        packer.rotation_priority = rotation_priority

        # 记录开始时间
        start_time = time.time()

        # 放置图片
        placed_count = 0
        for image in images:
            # 准备图片数据
            image_data = {
                'id': image['id'],
                'c_horizontal_priority': horizontal_priority,
                'c_gap_filling_priority': gap_filling_priority,
                'c_rotation_priority': rotation_priority
            }

            # 尝试放置图片
            result = packer.place_image(image['width'], image['height'], image_data)

            if result[2]:  # 如果成功放置
                placed_count += 1

        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time

        # 获取布局结果
        max_height = packer.get_max_height()
        placed_images = packer.get_placed_images()
        utilization = packer.get_utilization() * 100  # 转换为百分比

        # 计算水平利用率（每一行的平均利用率）
        horizontal_utilization = packer.get_horizontal_utilization()
        row_utilizations = list(horizontal_utilization.values())
        avg_row_utilization = np.mean(row_utilizations) * 100 if row_utilizations else 0

        # 计算处理速度
        processing_speed = placed_count / processing_time if processing_time > 0 else 0

        # 记录测试结果
        result = {
            'parameters': {
                'horizontal_priority': horizontal_priority,
                'gap_filling_priority': gap_filling_priority,
                'rotation_priority': rotation_priority
            },
            'results': {
                'placed_count': placed_count,
                'total_count': len(images),
                'success_rate': placed_count / len(images) * 100,
                'max_height': max_height,
                'canvas_utilization': utilization,
                'avg_row_utilization': avg_row_utilization,
                'processing_time': processing_time,
                'processing_speed': processing_speed
            },
            'placed_images': placed_images
        }

        # 计算综合得分
        score = self.calculate_score(result)
        result['score'] = score

        # 记录日志
        log.info(f"测试参数组合:")
        log.info(f"  - 水平优先级: {horizontal_priority}%")
        log.info(f"  - 空隙填充优先级: {gap_filling_priority}%")
        log.info(f"  - 旋转优先级: {rotation_priority}%")
        log.info(f"测试结果:")
        log.info(f"  - 成功放置: {placed_count}/{len(images)} 个图片 ({result['results']['success_rate']:.2f}%)")
        log.info(f"  - 画布利用率: {utilization:.2f}%")
        log.info(f"  - 平均行利用率: {avg_row_utilization:.2f}%")
        log.info(f"  - 最大高度: {max_height}像素")
        log.info(f"  - 处理时间: {processing_time:.4f}秒")
        log.info(f"  - 处理速度: {processing_speed:.4f}图片/秒")
        log.info(f"  - 综合得分: {score:.4f}")

        # 添加到测试结果列表
        self.test_results.append(result)

        # 更新最佳参数
        if score > self.best_score:
            self.best_score = score
            self.best_params = result['parameters']
            log.info(f"发现新的最佳参数组合! 得分: {score:.4f}")

        return result

    def calculate_score(self, result: Dict[str, Any]) -> float:
        """
        计算测试结果的综合得分
        优化目标：水平利用率最优，代码效率最高

        Args:
            result: 测试结果

        Returns:
            float: 综合得分
        """
        # 提取结果
        canvas_util = result['results']['canvas_utilization']
        row_util = result['results']['avg_row_utilization']
        speed = result['results']['processing_speed']
        success_rate = result['results']['success_rate']

        # 归一化处理速度（假设最大处理速度为1000图片/秒）
        normalized_speed = min(1.0, speed / 1000)

        # 权重设置 - 根据需求调整
        row_util_weight = 0.5     # 水平利用率权重（最重要）
        canvas_util_weight = 0.2  # 画布利用率权重
        speed_weight = 0.2        # 处理速度权重
        success_weight = 0.1      # 成功率权重

        # 计算综合得分
        score = (row_util_weight * row_util +
                 canvas_util_weight * canvas_util +
                 speed_weight * (normalized_speed * 100) +
                 success_weight * success_rate)

        return score

    def grid_search(self,
                   images: List[Dict[str, Any]],
                   horizontal_priorities: List[int] = None,
                   gap_filling_priorities: List[int] = None,
                   rotation_priorities: List[int] = None) -> Dict[str, Any]:
        """
        网格搜索最优参数组合

        Args:
            images: 图片列表
            horizontal_priorities: 水平优先级列表
            gap_filling_priorities: 空隙填充优先级列表
            rotation_priorities: 旋转优先级列表

        Returns:
            Dict[str, Any]: 最优参数组合及其结果
        """
        # 默认参数范围
        if horizontal_priorities is None:
            # 测试模式下使用更少的参数值
            if self.test_mode:
                horizontal_priorities = [80, 90]
            else:
                horizontal_priorities = [70, 75, 80, 85, 90, 95]

        if gap_filling_priorities is None:
            # 测试模式下使用更少的参数值
            if self.test_mode:
                gap_filling_priorities = [70, 80]
            else:
                gap_filling_priorities = [60, 65, 70, 75, 80, 85]

        if rotation_priorities is None:
            # 测试模式下使用更少的参数值
            if self.test_mode:
                rotation_priorities = [50, 70]
            else:
                rotation_priorities = [40, 50, 60, 70, 80]

        log.info(f"开始网格搜索最优参数组合...")
        log.info(f"  - 水平优先级范围: {horizontal_priorities}")
        log.info(f"  - 空隙填充优先级范围: {gap_filling_priorities}")
        log.info(f"  - 旋转优先级范围: {rotation_priorities}")

        # 清空之前的测试结果
        self.test_results = []
        self.best_score = -float('inf')
        self.best_params = None

        # 测试所有参数组合
        total_combinations = len(horizontal_priorities) * len(gap_filling_priorities) * len(rotation_priorities)
        current_combination = 0

        # 记录搜索过程中的得分变化
        search_history = []

        for hp in horizontal_priorities:
            for gp in gap_filling_priorities:
                for rp in rotation_priorities:
                    current_combination += 1
                    log.info(f"测试参数组合 {current_combination}/{total_combinations}...")

                    # 测试当前参数组合
                    result = self.test_parameters(
                        images=images,
                        horizontal_priority=hp,
                        gap_filling_priority=gp,
                        rotation_priority=rp
                    )

                    # 记录搜索历史
                    search_history.append({
                        'iteration': current_combination,
                        'parameters': result['parameters'],
                        'score': result['score']
                    })

        log.info(f"网格搜索完成!")
        log.info(f"最优参数组合:")
        log.info(f"  - 水平优先级: {self.best_params['horizontal_priority']}%")
        log.info(f"  - 空隙填充优先级: {self.best_params['gap_filling_priority']}%")
        log.info(f"  - 旋转优先级: {self.best_params['rotation_priority']}%")
        log.info(f"最优得分: {self.best_score:.4f}")

        # 找到最佳结果
        best_result = next((r for r in self.test_results if r['parameters'] == self.best_params), None)

        # 返回最佳结果和搜索历史
        return {
            'best_result': best_result,
            'search_history': search_history
        }

    def visualize_search_history(self, search_history: List[Dict[str, Any]], output_path: str = None) -> None:
        """
        可视化参数搜索历史

        Args:
            search_history: 搜索历史
            output_path: 输出文件路径，如果为None则显示图像
        """
        if not search_history:
            log.warning("没有搜索历史可视化")
            return

        # 提取数据
        iterations = [h['iteration'] for h in search_history]
        scores = [h['score'] for h in search_history]

        # 创建图像
        plt.figure(figsize=(10, 6))
        plt.plot(iterations, scores, 'b-', marker='o', markersize=4)

        # 标记最佳点
        best_idx = np.argmax(scores)
        best_iteration = iterations[best_idx]
        best_score = scores[best_idx]
        best_params = search_history[best_idx]['parameters']

        plt.plot(best_iteration, best_score, 'ro', markersize=8)
        plt.annotate(f"最佳: {best_score:.2f}\nH:{best_params['horizontal_priority']} "
                    f"G:{best_params['gap_filling_priority']} "
                    f"R:{best_params['rotation_priority']}",
                    xy=(best_iteration, best_score),
                    xytext=(best_iteration + 1, best_score + 1),
                    arrowprops=dict(facecolor='black', shrink=0.05))

        # 设置标题和标签（使用中文字体）
        plt.title('参数搜索历史', fontproperties='SimHei')
        plt.xlabel('迭代次数', fontproperties='SimHei')
        plt.ylabel('得分', fontproperties='SimHei')
        plt.grid(True)

        # 保存或显示图像
        if output_path:
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"搜索历史可视化已保存到: {output_path}")
        else:
            plt.show()

        plt.close()

    def visualize_result(self,
                        result: Dict[str, Any],
                        output_path: str = None,
                        show_grid: bool = True,
                        show_stats: bool = True) -> None:
        """
        可视化测试结果

        Args:
            result: 测试结果
            output_path: 输出文件路径，如果为None则显示图像
            show_grid: 是否显示网格
            show_stats: 是否显示统计信息
        """
        placed_images = result['placed_images']

        if not placed_images:
            log.warning("没有放置任何图片，无法可视化")
            return

        # 计算画布高度
        max_height = max(img['y'] + img['height'] for img in placed_images)

        # 创建图像
        fig, ax = plt.subplots(figsize=(12, max_height / 100 + 2))

        # 绘制画布边界
        ax.add_patch(Rectangle((0, 0), self.canvas_width, max_height,
                              fill=False, edgecolor='black', linewidth=2))

        # 绘制网格
        if show_grid:
            grid_size = 50
            for x in range(0, self.canvas_width, grid_size):
                ax.axvline(x, color='lightgray', linestyle='-', linewidth=0.5)
            for y in range(0, max_height, grid_size):
                ax.axhline(y, color='lightgray', linestyle='-', linewidth=0.5)

        # 绘制已放置的图片
        colors = plt.cm.tab20(np.linspace(0, 1, 20))

        for i, img in enumerate(placed_images):
            color = colors[i % len(colors)]

            # 绘制图片矩形
            rect = Rectangle((img['x'], img['y']), img['width'], img['height'],
                           fill=True, edgecolor='black', facecolor=color, linewidth=1, alpha=0.7)
            ax.add_patch(rect)

            # 添加图片ID
            if 'data' in img and 'id' in img['data']:
                img_id = img['data']['id']
                # 如果data中有name字段，优先使用name
                if 'name' in img['data']:
                    img_id = img['data']['name']
                # 使用支持中文的字体
                ax.text(img['x'] + img['width'] / 2, img['y'] + img['height'] / 2,
                       img_id, ha='center', va='center', fontsize=8, family='SimHei')

        # 添加统计信息
        if show_stats:
            stats_text = [
                f"参数: 水平优先级={result['parameters']['horizontal_priority']}%, "
                f"空隙填充优先级={result['parameters']['gap_filling_priority']}%, "
                f"旋转优先级={result['parameters']['rotation_priority']}%",
                f"画布: {self.canvas_width}x{max_height} 像素",
                f"图片: {result['results']['placed_count']}/{result['results']['total_count']} 个",
                f"画布利用率: {result['results']['canvas_utilization']:.2f}%",
                f"平均行利用率: {result['results']['avg_row_utilization']:.2f}%",
                f"处理时间: {result['results']['processing_time']:.4f}秒",
                f"处理速度: {result['results']['processing_speed']:.4f}图片/秒",
                f"综合得分: {result['score']:.4f}"
            ]

            plt.figtext(0.02, 0.02, '\n'.join(stats_text), fontsize=10,
                      bbox=dict(facecolor='white', alpha=0.8, boxstyle='round'),
                      family='SimHei')

        # 设置坐标轴
        ax.set_xlim(0, self.canvas_width)
        ax.set_ylim(0, max_height)
        ax.set_aspect('equal')

        # 设置标题（使用中文字体）
        plt.title('俄罗斯方块算法布局结果', fontproperties='SimHei')

        # 保存或显示图像
        if output_path:
            # 确保目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            # 保存图像，使用高质量设置
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"布局可视化已保存到: {output_path}")
        else:
            plt.show()

        plt.close()

    def save_best_params(self, output_path: str) -> None:
        """
        保存最佳参数到JSON文件

        Args:
            output_path: 输出文件路径
        """
        if self.best_params is None:
            log.warning("没有最佳参数可保存")
            return

        # 创建包含最佳参数和得分的字典
        data = {
            'best_parameters': self.best_params,
            'best_score': self.best_score,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        # 保存到JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)

        log.info(f"最佳参数已保存到: {output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='俄罗斯方块算法自动调优')

    # 基本参数
    parser.add_argument('--width', type=int, default=1600, help='画布宽度（像素）')
    parser.add_argument('--spacing', type=int, default=5, help='图片间距（像素）')
    parser.add_argument('--max-height', type=int, default=0, help='最大高度限制（0表示无限制）')
    parser.add_argument('--seed', type=int, default=None, help='随机种子')

    # 图片生成参数
    parser.add_argument('--count', type=int, default=50, help='图片数量')
    parser.add_argument('--input', type=str, default=None, help='输入图片数据文件路径')

    # 搜索参数
    parser.add_argument('--horizontal-range', type=str, default='70,75,80,85,90,95',
                      help='水平优先级范围，逗号分隔')
    parser.add_argument('--gap-filling-range', type=str, default='60,65,70,75,80,85',
                      help='空隙填充优先级范围，逗号分隔')
    parser.add_argument('--rotation-range', type=str, default='40,50,60,70,80',
                      help='旋转优先级范围，逗号分隔')

    # 测试模式
    parser.add_argument('--test-mode', action='store_true', help='启用测试模式，使用更少的图片和更简单的算法')

    # 输出参数
    parser.add_argument('--output-dir', type=str, default='results', help='输出目录')
    parser.add_argument('--save-params', action='store_true', help='保存最佳参数到文件')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 创建自动调优器
    tuner = TetrisAutoTuner(
        canvas_width=args.width,
        image_spacing=args.spacing,
        max_height=args.max_height,
        seed=args.seed,
        test_mode=args.test_mode
    )

    # 获取图片数据
    if args.input:
        # 从文件加载图片数据
        images = tuner.load_images(args.input)
    else:
        # 生成测试图片
        # 测试模式下使用更少的图片
        if args.test_mode:
            actual_count = min(args.count, 20)  # 限制最大数量为20
            if actual_count < args.count:
                log.warning(f"测试模式：图片数量已限制为{actual_count}（原始请求: {args.count}）")
            images = tuner.generate_test_images(count=actual_count)
        else:
            images = tuner.generate_test_images(count=args.count)

    # 解析参数范围
    horizontal_priorities = [int(x) for x in args.horizontal_range.split(',')]
    gap_filling_priorities = [int(x) for x in args.gap_filling_range.split(',')]
    rotation_priorities = [int(x) for x in args.rotation_range.split(',')]

    # 网格搜索
    search_result = tuner.grid_search(
        images=images,
        horizontal_priorities=horizontal_priorities,
        gap_filling_priorities=gap_filling_priorities,
        rotation_priorities=rotation_priorities
    )

    # 可视化搜索历史
    history_output = os.path.join(args.output_dir, 'search_history.png')
    tuner.visualize_search_history(search_result['search_history'], history_output)

    # 可视化最佳结果
    best_output = os.path.join(args.output_dir, 'best_result.png')
    tuner.visualize_result(search_result['best_result'], best_output)

    # 保存最佳参数
    if args.save_params:
        params_output = os.path.join(args.output_dir, 'best_params.json')
        tuner.save_best_params(params_output)


if __name__ == "__main__":
    main()
