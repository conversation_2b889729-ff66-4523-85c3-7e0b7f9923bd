#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
俄罗斯方块算法高级自动调优脚本
使用贝叶斯优化算法自动调优俄罗斯方块算法参数
"""

import os
import sys
import time
import json
import random
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import List, Dict, Tuple, Any, Optional
import logging

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入俄罗斯方块算法
from core.tetris_packer import TetrisPacker
from tests.bayesian_optimizer import BayesianOptimizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("TetrisAdvancedTuner")

class TetrisAdvancedTuner:
    """俄罗斯方块算法高级自动调优器"""

    def __init__(self,
                 canvas_width: int = 1600,
                 image_spacing: int = 5,
                 max_height: int = 0,
                 seed: Optional[int] = None,
                 test_mode: bool = False):
        """
        初始化高级自动调优器

        Args:
            canvas_width: 画布宽度（像素）
            image_spacing: 图片间距（像素）
            max_height: 最大高度限制（0表示无限制）
            seed: 随机种子，用于生成可重复的测试数据
            test_mode: 测试模式，使用更少的图片和更简单的算法
        """
        self.canvas_width = canvas_width
        self.image_spacing = image_spacing
        self.max_height = max_height
        self.test_mode = test_mode

        # 设置随机种子
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)

        # 创建贝叶斯优化器
        self.optimizer = BayesianOptimizer()

        # 记录测试图片
        self.images = []

        # 记录测试结果
        self.test_results = []

        # 记录最佳参数
        self.best_params = None
        self.best_score = -float('inf')
        self.best_result = None

        log.info(f"初始化高级自动调优器:")
        log.info(f"  - 画布宽度: {self.canvas_width}像素")
        log.info(f"  - 图片间距: {self.image_spacing}像素")
        log.info(f"  - 最大高度限制: {self.max_height if self.max_height > 0 else '无限制'}像素")
        log.info(f"  - 测试模式: {'启用' if self.test_mode else '禁用'}")

    def generate_test_images(self, count: int = 50) -> List[Dict[str, Any]]:
        """
        生成测试图片集合，包含多种类型的图片

        Args:
            count: 图片总数

        Returns:
            List[Dict[str, Any]]: 图片列表
        """
        images = []
        image_id = 1

        # 1. 小图片（20%）
        small_count = max(1, int(count * 0.2))
        for i in range(small_count):
            width = random.randint(20, 80)
            height = random.randint(20, 80)

            image = {
                'id': f"small_{image_id}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'small'
            }

            images.append(image)
            image_id += 1

        # 2. 中等图片（40%）
        medium_count = max(1, int(count * 0.4))
        for i in range(medium_count):
            width = random.randint(80, 150)
            height = random.randint(80, 150)

            image = {
                'id': f"medium_{image_id}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'medium'
            }

            images.append(image)
            image_id += 1

        # 3. 大图片（20%）
        large_count = max(1, int(count * 0.2))
        for i in range(large_count):
            width = random.randint(150, 300)
            height = random.randint(150, 300)

            image = {
                'id': f"large_{image_id}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'large'
            }

            images.append(image)
            image_id += 1

        # 4. 极端宽高比图片（20%）
        extreme_count = max(1, int(count * 0.2))
        for i in range(extreme_count):
            if random.random() < 0.5:
                # 极宽图片
                width = random.randint(200, 400)
                height = random.randint(20, 50)
            else:
                # 极高图片
                width = random.randint(20, 50)
                height = random.randint(200, 400)

            image = {
                'id': f"extreme_{image_id}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'extreme'
            }

            images.append(image)
            image_id += 1

        # 打乱图片顺序
        random.shuffle(images)

        log.info(f"生成了 {len(images)} 个测试图片")
        log.info(f"  - 小图片: {small_count}个")
        log.info(f"  - 中等图片: {medium_count}个")
        log.info(f"  - 大图片: {large_count}个")
        log.info(f"  - 极端宽高比图片: {extreme_count}个")

        # 保存测试图片
        self.images = images

        return images

    def load_images(self, input_path: str) -> List[Dict[str, Any]]:
        """
        从JSON文件加载图片数据

        Args:
            input_path: 输入文件路径

        Returns:
            List[Dict[str, Any]]: 图片列表
        """
        with open(input_path, 'r', encoding='utf-8') as f:
            images = json.load(f)

        log.info(f"从 {input_path} 加载了 {len(images)} 个图片")

        # 保存测试图片
        self.images = images

        return images

    def evaluate_parameters(self, params: Dict[str, int]) -> float:
        """
        评估参数组合的效果，返回得分

        Args:
            params: 参数字典，包含 horizontal_priority, gap_filling_priority, rotation_priority

        Returns:
            float: 评估得分
        """
        # 提取参数
        horizontal_priority = params.get('horizontal_priority', 80)
        gap_filling_priority = params.get('gap_filling_priority', 70)
        rotation_priority = params.get('rotation_priority', 60)

        log.info(f"开始评估参数组合: H={horizontal_priority}, G={gap_filling_priority}, R={rotation_priority}")

        # 检查是否已经评估过相同或相近的参数组合
        for result in self.test_results:
            prev_params = result['parameters']
            # 如果参数非常接近（差距小于5%），直接返回之前的结果
            if (abs(prev_params['horizontal_priority'] - horizontal_priority) <= 5 and
                abs(prev_params['gap_filling_priority'] - gap_filling_priority) <= 5 and
                abs(prev_params['rotation_priority'] - rotation_priority) <= 5):
                log.info(f"发现相似参数组合，使用缓存结果: {prev_params}")
                log.info(f"缓存得分: {result['score']:.4f}")
                return result['score']

        # 创建俄罗斯方块算法实例
        packer = TetrisPacker(
            container_width=self.canvas_width,
            image_spacing=self.image_spacing,
            max_height=self.max_height
        )

        # 设置算法参数
        packer.horizontal_priority = horizontal_priority
        packer.gap_filling_priority = gap_filling_priority
        packer.rotation_priority = rotation_priority

        # 记录开始时间
        start_time = time.time()

        # 放置图片
        placed_count = 0
        total_images = len(self.images)

        # 优化：对于测试，我们可以只使用部分图片来加速评估
        # 在测试模式下使用更少的图片，在正常模式下使用全部图片
        if self.test_mode:
            test_sample_size = min(total_images, 20)  # 测试模式下最多使用20张图片
            log.info(f"测试模式：使用 {test_sample_size}/{total_images} 张图片进行快速评估")
            # 确保样本具有代表性，选择不同类型的图片
            test_images = []
            types = {}
            for img in self.images:
                img_type = img.get('type', 'unknown')
                if img_type not in types:
                    types[img_type] = []
                types[img_type].append(img)

            # 从每种类型中选择一定比例的图片
            for type_name, type_images in types.items():
                sample_count = max(1, int(len(type_images) * test_sample_size / total_images))
                test_images.extend(type_images[:sample_count])

            # 如果样本数量不足，随机添加更多图片
            import random
            while len(test_images) < test_sample_size and len(self.images) > len(test_images):
                remaining = [img for img in self.images if img not in test_images]
                test_images.append(random.choice(remaining))
        else:
            # 正常模式下，使用全部图片
            test_images = self.images
            log.info(f"正常模式：使用全部 {total_images} 张图片进行评估")

        log_interval = max(1, len(test_images) // 5)  # 每处理20%的图片记录一次日志

        log.info(f"开始放置 {len(test_images)} 个图片...")
        start_process_time = time.time()

        for i, image in enumerate(test_images):
            # 准备图片数据
            image_data = {
                'id': image['id'],
                'c_horizontal_priority': horizontal_priority,
                'c_gap_filling_priority': gap_filling_priority,
                'c_rotation_priority': rotation_priority
            }

            # 尝试放置图片
            result = packer.place_image(image['width'], image['height'], image_data)

            if result[2]:  # 如果成功放置
                placed_count += 1

            # 定期输出进度
            if (i + 1) % log_interval == 0:
                progress = (i + 1) / len(test_images) * 100
                success_rate = placed_count / (i + 1) * 100
                elapsed = time.time() - start_process_time
                estimated_total = elapsed / (i + 1) * len(test_images)
                remaining = estimated_total - elapsed
                log.info(f"进度: {progress:.1f}% ({i + 1}/{len(test_images)}), 当前成功率: {success_rate:.1f}%")
                log.info(f"已用时间: {elapsed:.1f}秒, 预计剩余: {remaining:.1f}秒")

        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time

        # 获取布局结果
        max_height = packer.get_max_height()
        placed_images = packer.get_placed_images()
        utilization = packer.get_utilization() * 100  # 转换为百分比

        # 计算水平利用率（每一行的平均利用率）
        horizontal_utilization = packer.get_horizontal_utilization()
        row_utilizations = list(horizontal_utilization.values())
        avg_row_utilization = np.mean(row_utilizations) * 100 if row_utilizations else 0

        # 计算处理速度
        processing_speed = placed_count / processing_time if processing_time > 0 else 0

        # 计算成功率
        # 如果使用了样本，需要调整成功率计算
        if test_sample_size < total_images:
            # 使用样本成功率作为估计值
            success_rate = placed_count / len(test_images) * 100
            log.info(f"基于样本的估计成功率: {success_rate:.2f}%")
        else:
            success_rate = placed_count / len(self.images) * 100

        # 记录测试结果
        result = {
            'parameters': {
                'horizontal_priority': horizontal_priority,
                'gap_filling_priority': gap_filling_priority,
                'rotation_priority': rotation_priority
            },
            'results': {
                'placed_count': placed_count,
                'total_count': len(self.images),
                'success_rate': success_rate,
                'max_height': max_height,
                'canvas_utilization': utilization,
                'avg_row_utilization': avg_row_utilization,
                'processing_time': processing_time,
                'processing_speed': processing_speed
            },
            'placed_images': placed_images
        }

        # 计算综合得分
        score = self.calculate_score(result)
        result['score'] = score

        # 记录日志
        log.info(f"评估参数组合:")
        log.info(f"  - 水平优先级: {horizontal_priority}%")
        log.info(f"  - 空隙填充优先级: {gap_filling_priority}%")
        log.info(f"  - 旋转优先级: {rotation_priority}%")
        log.info(f"评估结果:")
        log.info(f"  - 成功放置: {placed_count}/{len(self.images)} 个图片 ({success_rate:.2f}%)")
        log.info(f"  - 画布利用率: {utilization:.2f}%")
        log.info(f"  - 平均行利用率: {avg_row_utilization:.2f}%")
        log.info(f"  - 最大高度: {max_height}像素")
        log.info(f"  - 处理时间: {processing_time:.4f}秒")
        log.info(f"  - 处理速度: {processing_speed:.4f}图片/秒")
        log.info(f"  - 综合得分: {score:.4f}")

        # 更新最佳参数
        if score > self.best_score:
            self.best_score = score
            self.best_params = result['parameters']
            self.best_result = result
            log.info(f"发现新的最佳参数组合! 得分: {score:.4f}")

        return score

    def calculate_score(self, result: Dict[str, Any]) -> float:
        """
        计算测试结果的综合得分
        优化目标：水平利用率最优，代码效率最高

        Args:
            result: 测试结果

        Returns:
            float: 综合得分
        """
        # 提取结果
        canvas_util = result['results']['canvas_utilization']
        row_util = result['results']['avg_row_utilization']
        speed = result['results']['processing_speed']
        success_rate = result['results']['success_rate']

        # 归一化处理速度（假设最大处理速度为1000图片/秒）
        normalized_speed = min(1.0, speed / 1000)

        # 权重设置 - 根据需求调整
        row_util_weight = 0.5     # 水平利用率权重（最重要）
        canvas_util_weight = 0.2  # 画布利用率权重
        speed_weight = 0.2        # 处理速度权重
        success_weight = 0.1      # 成功率权重

        # 计算综合得分
        score = (row_util_weight * row_util +
                 canvas_util_weight * canvas_util +
                 speed_weight * (normalized_speed * 100) +
                 success_weight * success_rate)

        return score

    def run_optimization(self, n_calls: int = 30) -> Dict[str, Any]:
        """
        运行贝叶斯优化

        Args:
            n_calls: 调用目标函数的次数

        Returns:
            Dict[str, Any]: 优化结果
        """
        if not self.images:
            raise ValueError("请先生成或加载测试图片")

        log.info(f"准备开始优化，图片数量: {len(self.images)}")

        # 定义参数范围
        param_ranges = {
            'horizontal_priority': (60, 95),
            'gap_filling_priority': (50, 90),
            'rotation_priority': (30, 80)
        }

        log.info(f"参数搜索范围: {param_ranges}")
        log.info(f"将执行 {n_calls} 次参数评估...")

        # 首先测试几个预设的参数组合，这些组合在实践中表现良好
        preset_params = [
            {'horizontal_priority': 80, 'gap_filling_priority': 70, 'rotation_priority': 60},  # 默认参数
            {'horizontal_priority': 90, 'gap_filling_priority': 80, 'rotation_priority': 50},  # 高水平优先级
            {'horizontal_priority': 85, 'gap_filling_priority': 85, 'rotation_priority': 40}   # 高空隙填充优先级
        ]

        log.info("首先测试预设参数组合...")
        for params in preset_params:
            score = self.evaluate_parameters(params)
            log.info(f"预设参数 {params} 得分: {score:.4f}")

        # 如果n_calls小于等于3，我们已经测试了预设参数，可以直接返回结果
        if n_calls <= 3:
            log.info("已完成预设参数测试，跳过贝叶斯优化")
            # 找出最佳结果
            best_result = max(self.test_results, key=lambda x: x['score'])
            return {
                'best_params': best_result['parameters'],
                'best_score': best_result['score'],
                'search_history': self.test_results
            }

        # 否则，继续运行贝叶斯优化
        log.info(f"继续运行贝叶斯优化，剩余 {n_calls - 3} 次评估...")
        result = self.optimizer.optimize(
            objective_function=self.evaluate_parameters,
            param_ranges=param_ranges,
            n_calls=n_calls - 3  # 减去已经测试的预设参数数量
        )

        return result

    def visualize_result(self,
                        result: Dict[str, Any],
                        output_path: str = None,
                        show_grid: bool = True,
                        show_stats: bool = True) -> None:
        """
        可视化测试结果

        Args:
            result: 测试结果
            output_path: 输出文件路径，如果为None则显示图像
            show_grid: 是否显示网格
            show_stats: 是否显示统计信息
        """
        placed_images = result['placed_images']

        if not placed_images:
            log.warning("没有放置任何图片，无法可视化")
            return

        # 计算画布高度
        max_height = max(img['y'] + img['height'] for img in placed_images)

        # 创建图像
        fig, ax = plt.subplots(figsize=(12, max_height / 100 + 2))

        # 绘制画布边界
        ax.add_patch(plt.Rectangle((0, 0), self.canvas_width, max_height,
                              fill=False, edgecolor='black', linewidth=2))

        # 绘制网格
        if show_grid:
            grid_size = 50
            for x in range(0, self.canvas_width, grid_size):
                ax.axvline(x, color='lightgray', linestyle='-', linewidth=0.5)
            for y in range(0, max_height, grid_size):
                ax.axhline(y, color='lightgray', linestyle='-', linewidth=0.5)

        # 绘制已放置的图片
        colors = plt.cm.tab20(np.linspace(0, 1, 20))

        for i, img in enumerate(placed_images):
            color = colors[i % len(colors)]

            # 绘制图片矩形
            rect = plt.Rectangle((img['x'], img['y']), img['width'], img['height'],
                           fill=True, edgecolor='black', facecolor=color, linewidth=1, alpha=0.7)
            ax.add_patch(rect)

            # 添加图片ID
            if 'data' in img and 'id' in img['data']:
                img_id = img['data']['id']
                # 如果data中有name字段，优先使用name
                if 'name' in img['data']:
                    img_id = img['data']['name']
                # 使用支持中文的字体
                ax.text(img['x'] + img['width'] / 2, img['y'] + img['height'] / 2,
                       img_id, ha='center', va='center', fontsize=8, family='SimHei')

        # 添加统计信息
        if show_stats:
            stats_text = [
                f"参数: 水平优先级={result['parameters']['horizontal_priority']}%, "
                f"空隙填充优先级={result['parameters']['gap_filling_priority']}%, "
                f"旋转优先级={result['parameters']['rotation_priority']}%",
                f"画布: {self.canvas_width}x{max_height} 像素",
                f"图片: {result['results']['placed_count']}/{result['results']['total_count']} 个",
                f"画布利用率: {result['results']['canvas_utilization']:.2f}%",
                f"平均行利用率: {result['results']['avg_row_utilization']:.2f}%",
                f"处理时间: {result['results']['processing_time']:.4f}秒",
                f"处理速度: {result['results']['processing_speed']:.4f}图片/秒",
                f"综合得分: {result['score']:.4f}"
            ]

            plt.figtext(0.02, 0.02, '\n'.join(stats_text), fontsize=10,
                      bbox=dict(facecolor='white', alpha=0.8, boxstyle='round'),
                      family='SimHei')

        # 设置坐标轴
        ax.set_xlim(0, self.canvas_width)
        ax.set_ylim(0, max_height)
        ax.set_aspect('equal')

        # 设置标题（使用中文字体）
        plt.title('俄罗斯方块算法布局结果', fontproperties='SimHei')

        # 保存或显示图像
        if output_path:
            # 确保目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            # 保存图像，使用高质量设置
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"布局可视化已保存到: {output_path}")
        else:
            plt.show()

        plt.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='俄罗斯方块算法高级自动调优')

    # 基本参数
    parser.add_argument('--width', type=int, default=1600, help='画布宽度（像素）')
    parser.add_argument('--spacing', type=int, default=5, help='图片间距（像素）')
    parser.add_argument('--max-height', type=int, default=0, help='最大高度限制（0表示无限制）')
    parser.add_argument('--seed', type=int, default=None, help='随机种子')

    # 图片生成参数
    parser.add_argument('--count', type=int, default=50, help='图片数量')
    parser.add_argument('--input', type=str, default=None, help='输入图片数据文件路径')

    # 优化参数
    parser.add_argument('--n-calls', type=int, default=5, help='贝叶斯优化调用次数')

    # 测试模式
    parser.add_argument('--test-mode', action='store_true', help='启用测试模式，使用更少的图片和更简单的算法')

    # 输出参数
    parser.add_argument('--output-dir', type=str, default='results', help='输出目录')
    parser.add_argument('--save-params', action='store_true', help='保存最佳参数到文件')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 创建高级自动调优器
    tuner = TetrisAdvancedTuner(
        canvas_width=args.width,
        image_spacing=args.spacing,
        max_height=args.max_height,
        seed=args.seed,
        test_mode=args.test_mode
    )

    # 获取图片数据
    if args.input:
        # 从文件加载图片数据
        tuner.load_images(args.input)
    else:
        # 生成测试图片
        tuner.generate_test_images(count=args.count)

    # 运行贝叶斯优化
    result = tuner.run_optimization(n_calls=args.n_calls)

    # 可视化搜索历史
    history_output = os.path.join(args.output_dir, 'search_history.png')
    tuner.optimizer.visualize_search_history(result['search_history'], history_output)

    # 可视化目标函数（仅贝叶斯优化可用）
    objective_output = os.path.join(args.output_dir, 'objective.png')
    tuner.optimizer.visualize_objective(result, objective_output)

    # 可视化最佳结果
    if tuner.best_result:
        best_output = os.path.join(args.output_dir, 'best_result.png')
        tuner.visualize_result(tuner.best_result, best_output)

    # 保存最佳参数
    if args.save_params and result['best_params']:
        params_output = os.path.join(args.output_dir, 'best_params.json')
        tuner.optimizer.save_best_params(result['best_params'], result['best_score'], params_output)


if __name__ == "__main__":
    main()
