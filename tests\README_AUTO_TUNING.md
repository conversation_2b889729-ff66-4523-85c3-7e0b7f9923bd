# 俄罗斯方块算法自动调优工具

本目录包含一系列用于自动调优俄罗斯方块算法参数的工具，旨在优化水平利用率和代码效率。

## 工具说明

### 1. 自动调优工具

- `auto_tune_tetris.py`: 基本自动调优工具，使用网格搜索寻找最优参数
- `auto_tune_tetris_advanced.py`: 高级自动调优工具，使用贝叶斯优化寻找最优参数
- `bayesian_optimizer.py`: 贝叶斯优化器实现，提供更高效的参数搜索

### 2. 测试数据生成工具

- `generate_test_data.py`: 生成各种类型的测试图片，包括小图、大图、极端宽高比等

### 3. 参数应用工具

- `apply_best_params.py`: 将优化后的最佳参数应用到配置文件或DuckDB数据库中

### 4. 测试脚本

- `run_auto_tuning.py`: 一键运行自动调优测试，包括生成测试数据、运行调优、应用参数等步骤

## 使用方法

### 一键运行自动调优测试

```bash
python tests/run_auto_tuning.py --width 1600 --count 50 --method advanced --output-dir results
```

参数说明：
- `--width`: 画布宽度（像素）
- `--spacing`: 图片间距（像素）
- `--count`: 测试图片数量
- `--seed`: 随机种子
- `--method`: 调优方法，可选 `basic`（网格搜索）或 `advanced`（贝叶斯优化）
- `--n-calls`: 贝叶斯优化调用次数（仅当 `method=advanced` 时有效）
- `--output-dir`: 输出目录
- `--db`: DuckDB数据库文件路径（可选）

### 单独运行基本自动调优

```bash
python tests/auto_tune_tetris.py --width 1600 --count 50 --output-dir results --save-params
```

参数说明：
- `--width`: 画布宽度（像素）
- `--spacing`: 图片间距（像素）
- `--count`: 测试图片数量（如果不提供 `--input` 参数）
- `--input`: 输入图片数据文件路径（可选）
- `--horizontal-range`: 水平优先级范围，逗号分隔
- `--gap-filling-range`: 空隙填充优先级范围，逗号分隔
- `--rotation-range`: 旋转优先级范围，逗号分隔
- `--output-dir`: 输出目录
- `--save-params`: 保存最佳参数到文件

### 单独运行高级自动调优

```bash
python tests/auto_tune_tetris_advanced.py --width 1600 --input test_data.json --n-calls 30 --output-dir results --save-params
```

参数说明：
- `--width`: 画布宽度（像素）
- `--spacing`: 图片间距（像素）
- `--count`: 测试图片数量（如果不提供 `--input` 参数）
- `--input`: 输入图片数据文件路径（可选）
- `--n-calls`: 贝叶斯优化调用次数
- `--output-dir`: 输出目录
- `--save-params`: 保存最佳参数到文件

### 生成测试数据

```bash
python tests/generate_test_data.py --mode challenging --count 50 --output test_data.json
```

参数说明：
- `--mode`: 生成模式，可选 `random`、`fixed`、`challenging`、`real_world`
- `--count`: 图片数量
- `--seed`: 随机种子（可选）
- `--output`: 输出文件路径

### 应用最佳参数

```bash
python tests/apply_best_params.py --input results/best_params.json --output config/tetris_params.json --db database.duckdb
```

参数说明：
- `--input`: 输入最佳参数文件路径
- `--output`: 输出配置文件路径
- `--db`: DuckDB数据库文件路径（可选）

## 输出结果

自动调优工具会生成以下输出：

1. `search_history.png`: 参数搜索历史可视化
2. `objective.png`: 贝叶斯优化目标函数可视化（仅高级调优）
3. `best_result.png`: 最佳参数布局结果可视化
4. `best_params.json`: 最佳参数JSON文件
5. `tetris_params.json`: 应用到配置文件的参数

## 调优指标

自动调优工具使用以下指标评估参数组合的效果：

1. **水平利用率**（权重：50%）：每一行的平均利用率，反映水平空间利用效率
2. **画布利用率**（权重：20%）：整个画布的利用率，反映整体空间利用效率
3. **处理速度**（权重：20%）：每秒处理图片数量，反映代码效率
4. **成功率**（权重：10%）：成功放置图片的比例，反映算法稳定性

## 依赖库

- NumPy
- Matplotlib
- scikit-optimize（可选，用于贝叶斯优化）
- DuckDB（可选，用于参数存储）

可以使用以下命令安装依赖：

```bash
pip install numpy matplotlib scikit-optimize duckdb
```
