@echo off
REM 设置控制台代码页为UTF-8
chcp 65001

REM 设置环境变量
set PYTHONIOENCODING=utf-8

REM 创建结果目录
mkdir results_quick_test

REM 运行C类俄罗斯方块算法参数优化，迭代10次，缩放比例0.05
echo 开始运行C类俄罗斯方块算法快速测试...
echo 迭代次数: 10
echo 缩放比例: 0.05
echo 画布宽度: 160厘米
echo 最大高度: 5000厘米
echo 图片间距: 0.1厘米
echo 输出目录: results_quick_test

python tests\test_c_class_tetris.py --width 160 --max-height 5000 --input tests\test_data_sizes.txt --output-dir results_quick_test --auto-tune --iterations 10 --scale-factor 0.05

REM 完成后暂停以查看结果
echo 快速测试完成，请查看结果目录中的报告。
pause
