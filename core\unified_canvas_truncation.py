"""
统一的画布截断处理模块

此模块提供了一个全局统一的画布截断处理函数，确保所有需要截断画布的场景都调用这个统一函数。
主要功能包括：
1. 确定画布截断高度
2. 处理底部图片（包括单个图片的情况）
3. 优化B类和C类图片的放置
4. 处理画布最大高度限制

设计原则：
1. 单一职责：每个函数只负责一个特定任务
2. 清晰明确：逻辑清晰，易于理解和维护
3. 统一入口：所有画布截断相关的操作都通过此模块进行
"""

import logging
import copy
from typing import Dict, List, Tuple, Any, Optional

# 设置日志
log = logging.getLogger(__name__)


class UnifiedCanvasTruncation:
    """
    统一的画布截断处理类

    提供全局统一的画布截断处理函数，确保所有需要截断画布的场景都调用这个统一函数。
    """

    def __init__(self):
        """初始化画布截断处理类"""
        pass

    def _get_image_class(self, image: Dict[str, Any]) -> str:
        """
        获取图片的类别

        Args:
            image: 图片信息

        Returns:
            str: 图片类别，如'A', 'B', 'C'等，默认为'C'
        """
        # 尝试从不同位置获取图片类别
        if 'image_class' in image:
            return image['image_class']

        if 'category' in image:
            return image['category']

        if 'pattern' in image and isinstance(image['pattern'], dict):
            pattern = image['pattern']
            if 'image_class' in pattern:
                return pattern['image_class']
            if 'category' in pattern:
                return pattern['category']

        # 默认返回C类
        return 'C'

    def _handle_single_bottom_image(self, tetris_packer, bottom_image: Dict[str, Any],
                                   is_last_batch: bool) -> Optional[Dict[str, Any]]:
        """
        处理底部单图片的情况

        Args:
            tetris_packer: Tetris算法实例
            bottom_image: 底部图片信息
            is_last_batch: 是否是最后一批图片

        Returns:
            Optional[Dict[str, Any]]: 处理结果，如果不需要特殊处理则返回None
        """
        # 获取图片类别
        image_class = self._get_image_class(bottom_image)

        # 获取底部图片宽度
        bottom_image_width = bottom_image['width']

        # 情况1: 如果是最后一批图片，直接移动到下一画布
        if is_last_batch:
            # 调用tetris_packer的move_bottom_single_image方法
            success, moved_image, new_height = tetris_packer.move_bottom_single_image()

            if success and moved_image:
                log.info(f"最后一批图片，底部只有一个{image_class}类图片，已移动到下一画布")

                # 获取图片对应的pattern
                pattern = moved_image.get('pattern', {})
                moved_patterns = [pattern] if pattern else []

                # 返回移动后的画布高度
                return {
                    'height': new_height,
                    'success': True,
                    'action': "移动底部单图片",
                    'message': f"最后一批图片，底部单{image_class}类图片已移至下一画布",
                    'moved_patterns': moved_patterns
                }

        # 情况2: 如果底部单图片宽度较小，考虑移动到下一画布
        if bottom_image_width < tetris_packer.container_width * 0.5:
            # 如果是B类图片，检查是否是完整组的一部分
            if image_class == 'B' and self._is_part_of_complete_b_group(bottom_image, tetris_packer.container_width):
                log.info(f"底部单B类图片是完整组的一部分，保留在当前画布")
                return None

            # 调用tetris_packer的move_bottom_single_image方法
            success, moved_image, new_height = tetris_packer.move_bottom_single_image()

            if success and moved_image:
                log.info(f"底部单{image_class}类图片宽度较小 ({bottom_image_width}px)，已移动到下一画布")

                # 获取图片对应的pattern
                pattern = moved_image.get('pattern', {})
                moved_patterns = [pattern] if pattern else []

                # 返回移动后的画布高度
                return {
                    'height': new_height,
                    'success': True,
                    'action': "移动底部单图片",
                    'message': f"底部单{image_class}类图片宽度较小，已移至下一画布",
                    'moved_patterns': moved_patterns
                }

        # 不需要特殊处理
        return None

    def _is_part_of_complete_b_group(self, image: Dict[str, Any], container_width: int) -> bool:
        """
        检查B类图片是否是完整组的一部分

        Args:
            image: 图片信息
            container_width: 容器宽度

        Returns:
            bool: 是否是完整组的一部分
        """
        # 获取B类图片组信息
        group_index = image.get('group_index', -1)
        in_group_index = image.get('in_group_index', -1)
        group_size = image.get('group_size', 0)

        # 如果没有组信息，不是完整组的一部分
        if group_index < 0 or in_group_index < 0 or group_size <= 0:
            return False

        # 获取图片宽度
        image_width = image['width']

        # 计算组的总宽度
        group_width = image_width * group_size

        # 如果组的总宽度接近容器宽度，认为是完整组的一部分
        return group_width >= container_width * 0.85

    def _handle_b_class_bottom_image(self, tetris_packer, bottom_image: Dict[str, Any],
                                    remaining_patterns: Optional[List[Dict[str, Any]]]) -> Optional[Dict[str, Any]]:
        """
        处理底部B类图片的情况

        优化逻辑：
        1. 确保B类图片在底部时能完整放置一组数据，占满一行水平空间
        2. 检查B类图片是否属于完整组的一部分
        3. 检查是否有足够的剩余图片能与底部B类图片形成完整组
        4. 如果底部B类图片无法形成完整组，考虑移动到下一画布

        Args:
            tetris_packer: Tetris算法实例
            bottom_image: 底部图片信息
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            Optional[Dict[str, Any]]: 处理结果，如果不需要特殊处理则返回None
        """
        # 检查是否是B类图片
        if self._get_image_class(bottom_image) != 'B':
            return None

        # 获取底部图片宽度和高度
        bottom_image_width = bottom_image['width']
        bottom_image_height = bottom_image['height']

        # 获取B类图片组信息
        group_index = bottom_image.get('group_index', -1)
        in_group_index = bottom_image.get('in_group_index', -1)
        group_size = bottom_image.get('group_size', 0)

        # 检查是否有组信息
        has_group_info = group_index >= 0 and in_group_index >= 0 and group_size > 0

        # 情况1: 如果底部B类图片宽度接近画布宽度，保留在当前画布
        if bottom_image_width >= tetris_packer.container_width * 0.85:
            log.info(f"底部B类图片宽度接近画布宽度 ({bottom_image_width}px，占画布宽度的{bottom_image_width/tetris_packer.container_width:.2%})，保留在当前画布")
            return {
                'height': tetris_packer.get_max_height(),
                'success': True,
                'action': "保留底部B类图片",
                'message': f"底部B类图片宽度接近画布宽度，保留在当前画布",
                'moved_patterns': []
            }

        # 情况2: 如果底部B类图片是完整组的一部分，保留在当前画布
        if has_group_info:
            # 计算组的总宽度
            group_width = bottom_image_width * group_size

            # 如果组的总宽度接近容器宽度，认为是完整组的一部分
            if group_width >= tetris_packer.container_width * 0.85:
                log.info(f"底部B类图片是完整组的一部分 (组:{group_index}, 索引:{in_group_index}/{group_size})，保留在当前画布")
                return {
                    'height': tetris_packer.get_max_height(),
                    'success': True,
                    'action': "保留底部B类图片",
                    'message': f"底部B类图片是完整组的一部分，保留在当前画布",
                    'moved_patterns': []
                }

        # 情况3: 如果有剩余图片，检查是否能与底部B类图片形成完整组
        if remaining_patterns:
            # 查找与底部B类图片尺寸相似的图片
            similar_patterns = []
            for pattern in remaining_patterns:
                # 获取图片尺寸
                pattern_width = pattern.get('width_px', 0)
                if pattern_width == 0 and 'width_cm' in pattern:
                    # 如果没有像素尺寸，尝试从厘米尺寸转换
                    width_cm = pattern.get('width_cm', 0)
                    ppi = pattern.get('ppi', 72)
                    pattern_width = int(width_cm * ppi / 2.54)

                pattern_height = pattern.get('height_px', 0)
                if pattern_height == 0 and 'height_cm' in pattern:
                    # 如果没有像素尺寸，尝试从厘米尺寸转换
                    height_cm = pattern.get('height_cm', 0)
                    ppi = pattern.get('ppi', 72)
                    pattern_height = int(height_cm * ppi / 2.54)

                # 检查尺寸是否相似
                width_match = abs(pattern_width - bottom_image_width) / max(pattern_width, bottom_image_width)
                height_match = abs(pattern_height - bottom_image_height) / max(pattern_height, bottom_image_height)

                # 如果宽度和高度都匹配，添加到相似图片列表
                if width_match < 0.1 and height_match < 0.1:
                    similar_patterns.append(pattern)

            # 计算需要多少个图片才能形成完整组
            needed_count = max(1, int(tetris_packer.container_width / bottom_image_width) - 1)

            # 如果有足够的相似图片，可以形成完整组
            if len(similar_patterns) >= needed_count:
                log.info(f"底部B类图片可以与剩余图片形成完整组 (需要{needed_count}个，有{len(similar_patterns)}个)，保留在当前画布")
                return {
                    'height': tetris_packer.get_max_height(),
                    'success': True,
                    'action': "保留底部B类图片",
                    'message': f"底部B类图片可以与剩余图片形成完整组，保留在当前画布",
                    'moved_patterns': []
                }

        # 情况4: 如果底部B类图片无法形成完整组，考虑移动到下一画布
        # 但只有当底部图片宽度较小时才移动
        if bottom_image_width < tetris_packer.container_width * 0.5:
            log.info(f"底部B类图片无法形成完整组且宽度较小 ({bottom_image_width}px)，考虑移动到下一画布")

            # 调用tetris_packer的move_bottom_single_image方法
            success, moved_image, new_height = tetris_packer.move_bottom_single_image()

            if success and moved_image:
                log.info(f"底部B类图片已移动到下一画布")

                # 获取图片对应的pattern
                pattern = moved_image.get('pattern', {})
                moved_patterns = [pattern] if pattern else []

                # 返回移动后的画布高度
                return {
                    'height': new_height,
                    'success': True,
                    'action': "移动底部B类图片",
                    'message': f"底部B类图片无法形成完整组且宽度较小，已移至下一画布",
                    'moved_patterns': moved_patterns
                }

        # 不需要特殊处理
        return None

    def _handle_c_class_bottom_image(self, tetris_packer, bottom_image: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理底部C类图片的情况

        优化逻辑：
        1. 确保C类图片在底部时优先横向放置，不旋转90度
        2. 只在旋转90度能填充更多图片时，才考虑旋转
        3. 如果图片已经旋转，检查是否能在右侧填充更多图片
        4. 如果图片未旋转，检查是否应该旋转以提高空间利用率

        Args:
            tetris_packer: Tetris算法实例
            bottom_image: 底部图片信息

        Returns:
            Optional[Dict[str, Any]]: 处理结果，如果不需要特殊处理则返回None
        """
        # 检查是否是C类图片
        if self._get_image_class(bottom_image) != 'C':
            return None

        # 获取底部图片宽度和高度
        bottom_image_width = bottom_image['width']
        bottom_image_height = bottom_image['height']

        # 检查图片是否已经旋转
        is_rotated = bottom_image.get('need_rotation', False)

        # 计算画布剩余宽度
        container_width = tetris_packer.container_width

        # 情况1: 如果图片已经旋转，检查是否能填充更多图片
        if is_rotated:
            # 计算旋转后的宽度
            rotated_width = bottom_image_height

            # 检查旋转后是否能在右侧填充更多图片
            remaining_width = container_width - rotated_width

            # 如果右侧有足够空间，保持旋转状态
            if remaining_width >= rotated_width * 0.5:
                log.info(f"底部C类图片已旋转，右侧有足够空间填充更多图片 (剩余宽度:{remaining_width}px)，保持旋转状态")
                return {
                    'height': tetris_packer.get_max_height(),
                    'success': True,
                    'action': "保留底部C类图片旋转状态",
                    'message': f"底部C类图片已旋转，右侧有足够空间填充更多图片，保持旋转状态",
                    'moved_patterns': []
                }
            else:
                # 如果右侧空间不足，考虑取消旋转
                # 计算不旋转时的宽度
                non_rotated_width = bottom_image_height

                # 计算不旋转时的剩余宽度
                non_rotated_remaining_width = container_width - non_rotated_width

                # 如果不旋转后右侧有更多空间，建议取消旋转
                if non_rotated_remaining_width > remaining_width * 1.2:
                    log.info(f"底部C类图片已旋转，但取消旋转后右侧有更多空间 (当前剩余:{remaining_width}px, 取消旋转后剩余:{non_rotated_remaining_width}px)，建议取消旋转")
                    return {
                        'height': tetris_packer.get_max_height(),
                        'success': True,
                        'action': "建议取消底部C类图片旋转",
                        'message': f"底部C类图片已旋转，但取消旋转后右侧有更多空间，建议取消旋转",
                        'moved_patterns': []
                    }

        # 情况2: 如果图片未旋转，检查是否应该旋转
        else:
            # 计算旋转后的宽度
            rotated_width = bottom_image_height

            # 检查当前和旋转后的剩余宽度
            current_remaining_width = container_width - bottom_image_width
            rotated_remaining_width = container_width - rotated_width

            # 如果旋转后右侧能填充更多图片，考虑旋转
            # 只有当旋转后的剩余宽度显著大于当前剩余宽度时才考虑旋转
            if rotated_remaining_width > current_remaining_width * 1.5:
                log.info(f"底部C类图片未旋转，旋转后右侧能填充更多图片 (当前剩余:{current_remaining_width}px, 旋转后剩余:{rotated_remaining_width}px)，考虑旋转")
                return {
                    'height': tetris_packer.get_max_height(),
                    'success': True,
                    'action': "建议旋转底部C类图片",
                    'message': f"底部C类图片未旋转，旋转后右侧能填充更多图片，建议旋转",
                    'moved_patterns': []
                }

            # 如果当前宽度接近画布宽度，保持不旋转状态
            if bottom_image_width >= container_width * 0.85:
                log.info(f"底部C类图片宽度接近画布宽度 ({bottom_image_width}px，占画布宽度的{bottom_image_width/container_width:.2%})，保持不旋转状态")
                return {
                    'height': tetris_packer.get_max_height(),
                    'success': True,
                    'action': "保留底部C类图片不旋转状态",
                    'message': f"底部C类图片宽度接近画布宽度，保持不旋转状态",
                    'moved_patterns': []
                }

        # 情况3: 如果底部C类图片宽度较小，考虑移动到下一画布
        if bottom_image_width < container_width * 0.3:
            log.info(f"底部C类图片宽度较小 ({bottom_image_width}px，占画布宽度的{bottom_image_width/container_width:.2%})，考虑移动到下一画布")

            # 调用tetris_packer的move_bottom_single_image方法
            success, moved_image, new_height = tetris_packer.move_bottom_single_image()

            if success and moved_image:
                log.info(f"底部C类图片已移动到下一画布")

                # 获取图片对应的pattern
                pattern = moved_image.get('pattern', {})
                moved_patterns = [pattern] if pattern else []

                # 返回移动后的画布高度
                return {
                    'height': new_height,
                    'success': True,
                    'action': "移动底部C类图片",
                    'message': f"底部C类图片宽度较小，已移至下一画布",
                    'moved_patterns': moved_patterns
                }

        # 不需要特殊处理
        return None

    def _handle_max_height_limit(self, tetris_packer) -> Optional[Dict[str, Any]]:
        """
        处理最大高度限制的情况

        优化逻辑：
        1. 当图片超过画布最大高度时，以最大高度限制为画布高度
        2. 在接近最大高度限制时，尝试在图片右侧放置更多图片
        3. 当无法放置更多图片时，以画布最靠下图片的底部为画布高度

        Args:
            tetris_packer: Tetris算法实例

        Returns:
            Optional[Dict[str, Any]]: 处理结果，如果不需要特殊处理则返回None
        """
        # 获取当前最大高度
        current_max_height = tetris_packer.get_max_height()

        # 情况1: 如果设置了最大高度限制，且当前高度超过限制
        if tetris_packer.max_height > 0 and current_max_height > tetris_packer.max_height:
            log.info(f"当前高度 {current_max_height} 像素超过最大高度限制 {tetris_packer.max_height} 像素，将以最大高度限制为画布高度")
            return {
                'height': tetris_packer.max_height,
                'success': True,
                'action': "应用最大高度限制",
                'message': f"当前高度超过最大高度限制，以最大高度限制 {tetris_packer.max_height} 像素为画布高度",
                'moved_patterns': []
            }

        # 情况2: 如果设置了最大高度限制，且当前高度接近限制（超过90%）
        if tetris_packer.max_height > 0 and current_max_height > tetris_packer.max_height * 0.9:
            # 检查是否还能放置更多图片
            can_place_more = False
            if hasattr(tetris_packer, 'can_place_more_images_at_bottom'):
                can_place_more = tetris_packer.can_place_more_images_at_bottom()

            # 如果无法放置更多图片，以当前最大高度为画布高度
            if not can_place_more:
                log.info(f"当前高度 {current_max_height} 像素接近最大高度限制 {tetris_packer.max_height} 像素，且无法放置更多图片，以当前最大高度为画布高度")
                return {
                    'height': current_max_height,
                    'success': True,
                    'action': "接近最大高度限制",
                    'message': f"当前高度接近最大高度限制，且无法放置更多图片，以当前最大高度为画布高度",
                    'moved_patterns': []
                }

        # 不需要特殊处理
        return None

    def _handle_all_images_arranged(self, tetris_packer) -> Optional[Dict[str, Any]]:
        """
        处理所有图片都已排列完成的情况

        优化逻辑：
        1. 当全部图片排列完毕时，以画布最靠下图片的底部为画布高度
        2. 考虑B类图片在画布最底部时，要放置一组数据，占满一行水平空间
        3. 考虑C类图片在最底部时，尽可能图片横向放置，不旋转90度

        Args:
            tetris_packer: Tetris算法实例

        Returns:
            Optional[Dict[str, Any]]: 处理结果，如果不需要特殊处理则返回None
        """
        # 检查是否所有图片都已排列完成
        if hasattr(tetris_packer, 'is_all_images_arranged') and tetris_packer.is_all_images_arranged:
            # 获取当前最大高度
            current_max_height = tetris_packer.get_max_height()

            # 检查底部是否只有一个图片
            is_single_bottom, bottom_image, bottom_image_class = tetris_packer.is_single_image_at_bottom()

            # 如果底部只有一个图片，进行特殊处理
            if is_single_bottom and bottom_image:
                # 获取图片类别
                image_class = self._get_image_class(bottom_image)

                # 获取底部图片宽度
                bottom_image_width = bottom_image['width']

                # 如果是B类图片，检查是否是完整组的一部分
                if image_class == 'B' and self._is_part_of_complete_b_group(bottom_image, tetris_packer.container_width):
                    log.info(f"所有图片都已排列完成，底部B类图片是完整组的一部分，以画布最底部图片的底边 {current_max_height} 像素为截断高度")
                    return {
                        'height': current_max_height,
                        'success': True,
                        'action': "所有图片排列完成",
                        'message': f"所有图片都已排列完成，底部B类图片是完整组的一部分，以画布最底部图片的底边为截断高度",
                        'moved_patterns': []
                    }

                # 如果底部单图片宽度较小，考虑移动到下一画布
                if bottom_image_width < tetris_packer.container_width * 0.5:
                    # 调用tetris_packer的move_bottom_single_image方法
                    success, moved_image, new_height = tetris_packer.move_bottom_single_image()

                    if success and moved_image:
                        log.info(f"所有图片都已排列完成，底部单{image_class}类图片宽度较小 ({bottom_image_width}px)，已移动到下一画布")

                        # 获取图片对应的pattern
                        pattern = moved_image.get('pattern', {})
                        moved_patterns = [pattern] if pattern else []

                        # 返回移动后的画布高度
                        return {
                            'height': new_height,
                            'success': True,
                            'action': "移动底部单图片",
                            'message': f"所有图片都已排列完成，底部单{image_class}类图片宽度较小，已移至下一画布",
                            'moved_patterns': moved_patterns
                        }

            # 默认情况：以画布最底部图片的底边为截断高度
            log.info(f"所有图片都已排列完成，以画布最底部图片的底边 {current_max_height} 像素为截断高度")
            return {
                'height': current_max_height,
                'success': True,
                'action': "所有图片排列完成",
                'message': f"所有图片都已排列完成，以画布最底部图片的底边为截断高度",
                'moved_patterns': []
            }

        # 不需要特殊处理
        return None

    def truncate_canvas(self, tetris_packer, remaining_patterns: Optional[List[Dict[str, Any]]] = None,
                        is_last_batch: bool = False) -> Dict[str, Any]:
        """
        统一的画布截断处理函数

        此函数集中处理所有画布截断相关的逻辑，包括：
        1. 确定画布截断高度
        2. 处理底部图片（包括单个图片的情况）
        3. 优化B类和C类图片的放置

        截断逻辑优化：
        1. 当图片超过画布最大高度时，以最大高度限制为画布高度
        2. 当全部图片的最后一张图片排列完成时，以画布最靠下的图片底部为画布高度
        3. 当底部只有一个图片时，特别是最后一批图片时，将其移动到下一画布
        4. 当底部行利用率较低时，考虑移动整行到下一画布
        5. 当底部行利用率较高时，保留底部行

        Args:
            tetris_packer: Tetris算法实例
            remaining_patterns: 剩余未放置的图片列表
            is_last_batch: 是否是最后一批图片

        Returns:
            Dict[str, Any]: 截断结果，包含以下字段：
                - height: 截断高度（像素）
                - success: 是否成功截断
                - action: 执行的操作（如"移动底部行"、"保留底部行"等）
                - message: 操作说明
                - moved_patterns: 移动到下一画布的图片列表（如果有）
        """
        if not tetris_packer or not tetris_packer.placed_images:
            return {
                'height': 0,
                'success': False,
                'action': "无操作",
                'message': "未提供有效的Tetris算法实例或画布为空",
                'moved_patterns': []
            }

        try:
            # 记录原始状态
            original_placed_images = copy.deepcopy(tetris_packer.placed_images)
            original_max_height = tetris_packer.get_max_height()

            # 处理所有图片都已排列完成的情况
            result = self._handle_all_images_arranged(tetris_packer)
            if result:
                return result

            # 处理最大高度限制的情况
            result = self._handle_max_height_limit(tetris_packer)
            if result:
                return result

            # 检查底部是否只有一个图片
            is_single_bottom, bottom_image, image_class = tetris_packer.is_single_image_at_bottom()

            # 如果底部只有一个图片，进行特殊处理
            if is_single_bottom and bottom_image:
                # 处理底部单图片的情况
                result = self._handle_single_bottom_image(tetris_packer, bottom_image, is_last_batch)
                if result:
                    return result

                # 处理底部B类图片的情况
                result = self._handle_b_class_bottom_image(tetris_packer, bottom_image, remaining_patterns)
                if result:
                    return result

                # 处理底部C类图片的情况
                result = self._handle_c_class_bottom_image(tetris_packer, bottom_image)
                if result:
                    return result

            # 默认以画布最底部图片的底边为截断高度
            current_max_height = tetris_packer.get_max_height()
            return {
                'height': current_max_height,
                'success': True,
                'action': "使用当前最大高度",
                'message': f"以画布最底部图片的底边 {current_max_height} 像素为截断高度",
                'moved_patterns': []
            }

        except Exception as e:
            log.error(f"画布截断处理失败: {str(e)}")
            # 恢复原始状态
            if 'original_placed_images' in locals():
                tetris_packer.placed_images = original_placed_images

            # 返回当前最大高度
            return {
                'height': tetris_packer.get_max_height(),
                'success': False,
                'action': "处理失败",
                'message': f"画布截断处理失败: {str(e)}",
                'moved_patterns': []
            }


# 创建全局实例，方便其他模块调用
unified_canvas_truncation = UnifiedCanvasTruncation()
