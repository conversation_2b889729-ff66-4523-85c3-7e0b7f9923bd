#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tetris算法优化器扩展模块

提供Tetris算法的布局优化功能：
1. 多次尝试优化布局
2. 参数组合优化
3. 利用率提升评估
"""

import logging
import copy
from typing import List, Dict, Any, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("TetrisOptimizerExtension")

class TetrisOptimizerExtension:
    """
    Tetris算法优化器扩展，提供布局优化功能
    
    特性：
    1. 多次尝试优化布局
    2. 参数组合优化
    3. 利用率提升评估
    """
    
    def __init__(self, tetris_packer=None):
        """
        初始化Tetris算法优化器扩展
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        log.info("Tetris算法优化器扩展初始化完成")
    
    def optimize_layout(self, tetris_packer=None, iterations: int = 3):
        """
        优化图片布局，提高空间利用率
        增强版：实现多次优化尝试功能，对同一组图片进行多种排列方案比较，选择利用率最高的方案
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            iterations: 优化尝试次数，默认为3次
            
        Returns:
            bool: 是否成功优化
        """
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return False
        
        try:
            # 记录原始布局
            original_layout = packer.placed_images.copy()
            original_utilization = packer.get_utilization()
            
            # 记录最佳布局
            best_layout = original_layout.copy()
            best_utilization = original_utilization
            
            # 记录日志
            log.info(f"开始布局优化，原始利用率: {original_utilization:.4f}, 尝试次数: {iterations}")
            
            # 多次尝试不同的优化策略
            for iteration in range(iterations):
                # 清空当前布局
                packer.placed_images = []
                packer.current_max_height = 0
                
                # 根据不同的迭代次数选择不同的排序和放置策略
                if iteration == 0:
                    # 策略1: 按面积降序排序，优先放置大图片
                    sorted_images = sorted(original_layout, key=lambda img: -(img['width'] * img['height']))
                    horizontal_priority = 80
                    gap_filling_priority = 70
                    rotation_priority = 60
                elif iteration == 1:
                    # 策略2: 按宽度降序排序，优先填满行
                    sorted_images = sorted(original_layout, key=lambda img: -img['width'])
                    horizontal_priority = 90
                    gap_filling_priority = 60
                    rotation_priority = 50
                else:
                    # 策略3: 按高度降序排序，优先减少画布高度
                    sorted_images = sorted(original_layout, key=lambda img: -img['height'])
                    horizontal_priority = 70
                    gap_filling_priority = 80
                    rotation_priority = 70
                
                # 保存原始优先级设置
                original_horizontal_priority = packer.horizontal_priority
                original_gap_filling_priority = packer.gap_filling_priority
                original_rotation_priority = packer.rotation_priority
                
                # 设置新的优先级
                packer.horizontal_priority = horizontal_priority
                packer.gap_filling_priority = gap_filling_priority
                packer.rotation_priority = rotation_priority
                
                # 记录日志
                log.info(f"尝试优化策略 {iteration+1}/{iterations}: 水平优先级={horizontal_priority}, 空隙填充优先级={gap_filling_priority}, 旋转优先级={rotation_priority}")
                
                # 重新放置图片
                success_count = 0
                for img in sorted_images:
                    # 获取图片尺寸
                    width = img['width']
                    height = img['height']
                    need_rotation = img.get('need_rotation', False)
                    
                    # 如果图片已旋转，交换宽度和高度
                    if need_rotation:
                        width, height = height, width
                    
                    # 尝试找到最佳的旋转和位置
                    x, y, success, rotation = packer.find_best_rotation_and_position(width, height, img)
                    
                    if success:
                        # 确定最终尺寸
                        final_width = height if rotation else width
                        final_height = width if rotation else height
                        
                        # 更新已放置图片信息
                        packer._update_after_placement(x, y, final_width, final_height, final_width, final_height, img, rotation)
                        success_count += 1
                
                # 计算新布局的利用率
                new_utilization = packer.get_utilization()
                
                # 记录日志
                log.info(f"优化策略 {iteration+1}/{iterations} 结果: 利用率={new_utilization:.4f}, 成功放置={success_count}/{len(sorted_images)}")
                
                # 如果新布局更好，更新最佳布局
                if new_utilization > best_utilization:
                    best_layout = packer.placed_images.copy()
                    best_utilization = new_utilization
                    log.info(f"找到更好的布局: 利用率从 {best_utilization:.4f} 提高到 {new_utilization:.4f}")
                
                # 恢复原始优先级设置
                packer.horizontal_priority = original_horizontal_priority
                packer.gap_filling_priority = original_gap_filling_priority
                packer.rotation_priority = original_rotation_priority
            
            # 使用最佳布局
            packer.placed_images = best_layout
            packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
            
            # 如果最佳布局比原始布局更好，返回成功
            if best_utilization > original_utilization * 1.05:  # 至少提高5%
                log.info(f"布局优化成功: 利用率从 {original_utilization:.4f} 提高到 {best_utilization:.4f}")
                return True
            else:
                log.info(f"布局优化未能显著提高利用率: 最佳利用率 {best_utilization:.4f} vs 原始利用率 {original_utilization:.4f}")
                return False
                
        except Exception as e:
            log.error(f"优化布局失败: {str(e)}")
            # 恢复原始布局
            if 'original_layout' in locals():
                packer.placed_images = original_layout
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
            return False
