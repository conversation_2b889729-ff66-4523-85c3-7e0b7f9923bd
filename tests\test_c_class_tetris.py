#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
C类俄罗斯方块算法测试脚本
用于测试C类图片的俄罗斯方块式排列算法效果

特点：
1. 支持多种尺寸格式解析（如"40*48"、"120✘45"、"60+150"等）
2. 可视化排列结果
3. 自动调优算法参数
4. 计算并显示利用率
"""

import os
import re
import time
import random
import logging
import argparse
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import sys
import platform
from typing import List, Dict, Any, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入俄罗斯方块装箱器
from core.tetris_packer import TetrisPacker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("C类俄罗斯方块测试")

# 配置中文字体
def setup_chinese_font():
    """配置matplotlib中文字体，避免显示乱码"""
    # 检测操作系统
    system = platform.system()

    if system == 'Windows':
        # Windows系统常见中文字体
        font_paths = [
            r'C:\Windows\Fonts\simhei.ttf',  # 黑体
            r'C:\Windows\Fonts\simsun.ttc',  # 宋体
            r'C:\Windows\Fonts\msyh.ttc',    # 微软雅黑
            r'C:\Windows\Fonts\simkai.ttf'   # 楷体
        ]
    elif system == 'Darwin':  # macOS
        # macOS系统常见中文字体
        font_paths = [
            '/System/Library/Fonts/PingFang.ttc',
            '/Library/Fonts/Arial Unicode.ttf'
        ]
    else:  # Linux等其他系统
        # Linux系统常见中文字体
        font_paths = [
            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
            '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'
        ]

    # 尝试设置中文字体
    font_found = False
    for font_path in font_paths:
        if Path(font_path).exists():
            plt.rcParams['font.family'] = ['sans-serif']
            if system == 'Windows':
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
            elif system == 'Darwin':
                plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Arial Unicode MS']
            else:
                plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Droid Sans Fallback']

            # 解决负号显示问题
            plt.rcParams['axes.unicode_minus'] = False

            log.info(f"成功配置中文字体: {font_path}")
            font_found = True
            break

    if not font_found:
        log.warning("未找到合适的中文字体，可能会出现乱码")
        # 尝试使用matplotlib内置的字体
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['axes.unicode_minus'] = False

# 设置中文字体
setup_chinese_font()


def parse_size(size_str: str) -> Tuple[float, float]:
    """
    解析尺寸字符串，支持多种格式

    Args:
        size_str: 尺寸字符串，如"40*48"、"120✘45"、"60+150"等

    Returns:
        Tuple[float, float]: (宽度, 高度)
    """
    # 去除空格
    size_str = size_str.strip()

    # 替换各种分隔符为统一的'x'
    size_str = size_str.replace('*', 'x').replace('X', 'x').replace('✘', 'x')
    size_str = size_str.replace('+', 'x').replace('×', 'x')

    # 尝试匹配"宽x高"格式
    match = re.match(r'(\d+\.?\d*)x(\d+\.?\d*)', size_str)
    if match:
        width = float(match.group(1))
        height = float(match.group(2))
        return width, height

    # 尝试匹配纯数字（正方形）
    if size_str.isdigit():
        size = float(size_str)
        return size, size

    # 尝试匹配带小数点的数字
    try:
        size = float(size_str)
        return size, size
    except ValueError:
        pass

    # 如果无法解析，返回默认值
    log.warning(f"无法解析尺寸: {size_str}，使用默认值(50x50)")
    return 50.0, 50.0


def parse_size_list(size_list: List[str]) -> List[Dict[str, Any]]:
    """
    解析尺寸列表，生成图片数据

    Args:
        size_list: 尺寸字符串列表

    Returns:
        List[Dict[str, Any]]: 图片数据列表
    """
    images = []
    for i, size_str in enumerate(size_list):
        # 跳过空字符串
        if not size_str.strip():
            continue

        # 解析尺寸
        width, height = parse_size(size_str)

        # 创建图片数据
        image = {
            'id': f"image_{i+1}",
            'name': f"图片_{i+1}_{width}x{height}",
            'width': width,
            'height': height,
            'aspect_ratio': width / height,
            'area': width * height,
            'original_size': size_str
        }

        images.append(image)

    log.info(f"解析了 {len(images)} 个图片尺寸")
    return images


def visualize_layout(packer, output_path=None, title=None, show_grid=True, show_details=True):
    """
    可视化布局结果

    Args:
        packer: 俄罗斯方块装箱器实例
        output_path: 输出文件路径，如果为None则显示图像
        title: 图像标题
        show_grid: 是否显示网格
        show_details: 是否显示详细信息
    """
    # 获取已放置的图片
    placed_images = packer.get_placed_images()

    if not placed_images:
        log.warning("没有放置任何图片，无法可视化")
        return

    # 创建图像
    fig, ax = plt.subplots(figsize=(12, 8))

    # 设置坐标轴
    ax.set_xlim(0, packer.container_width)
    ax.set_ylim(0, packer.max_height + 100)  # 添加一些额外空间

    # 绘制画布边界
    ax.add_patch(plt.Rectangle((0, 0), packer.container_width, packer.max_height,
                              fill=False, edgecolor='black', linewidth=2))

    # 绘制每个图片
    for img in placed_images:
        x = img['x']
        y = img['y']
        width = img['width']
        height = img['height']

        # 检查图片是否被旋转
        is_rotated = img.get('rotated', False)

        # 获取图片颜色，如果没有则随机生成
        # 使用更好的颜色方案，旋转的图片使用不同的颜色
        if is_rotated:
            # 旋转的图片使用偏蓝色
            color = img.get('color', (0.2, 0.4, 0.8, 0.7))
        else:
            # 未旋转的图片使用偏绿色
            color = img.get('color', (0.2, 0.7, 0.4, 0.7))

        # 绘制矩形
        rect = plt.Rectangle((x, y), width, height, facecolor=color, alpha=0.7, edgecolor='black')
        ax.add_patch(rect)

        # 添加图片ID或名称
        name = img.get('name', img.get('id', ''))
        original_size = img.get('original_size', f"{width}x{height}")

        # 如果图片足够大且需要显示详细信息，在内部添加文本
        if width > 40 and height > 20 and show_details:
            rotation_text = "旋转" if is_rotated else ""
            ax.text(x + width/2, y + height/2, f"{name}\n{original_size}\n{rotation_text}",
                   ha='center', va='center', fontsize=8, color='black')

    # 计算利用率和其他指标
    utilization = packer.get_utilization() * 100

    # 计算行利用率
    row_heights = {}
    for img in placed_images:
        y = img['y']
        height = img['height']
        # 更新行高度
        for row_y in range(y, y + height):
            row_heights[row_y] = row_heights.get(row_y, 0) + 1

    # 计算非空行数
    non_empty_rows = len(row_heights)

    # 计算平均行利用率
    total_row_utilization = 0
    for y in range(packer.max_height):
        if y in row_heights:
            # 计算该行的利用率
            row_util = sum(img['width'] for img in placed_images
                          if img['y'] <= y < img['y'] + img['height']) / packer.container_width
            total_row_utilization += row_util

    avg_row_utilization = (total_row_utilization / non_empty_rows * 100) if non_empty_rows > 0 else 0

    # 添加标题和信息
    if title:
        ax.set_title(f"{title}")
    else:
        ax.set_title(f"C类俄罗斯方块式布局")

    # 添加详细信息文本
    info_text = (
        f"画布利用率: {utilization:.2f}%\n"
        f"行利用率: {avg_row_utilization:.2f}%\n"
        f"最大高度: {packer.max_height}像素\n"
        f"图片数量: {len(placed_images)}个"
    )

    # 在图像右上角添加信息文本
    ax.text(0.98, 0.98, info_text,
           transform=ax.transAxes,
           ha='right', va='top',
           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # 添加网格
    if show_grid:
        ax.grid(True, linestyle='--', alpha=0.3)

    # 添加坐标轴标签
    ax.set_xlabel('宽度 (像素)')
    ax.set_ylabel('高度 (像素)')

    # 保存或显示图像
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        log.info(f"布局可视化已保存到: {output_path}")
    else:
        plt.show()

    plt.close()


def test_tetris_algorithm(
    images: List[Dict[str, Any]],
    canvas_width: float = 160,
    max_height: float = 5000,
    image_spacing: float = 0.1,
    horizontal_priority: int = 80,
    gap_filling_priority: int = 70,
    rotation_priority: int = 60,
    output_path: str = None,
    visualize: bool = True,
    ppi: int = 72
):
    """
    测试俄罗斯方块算法

    Args:
        images: 图片列表
        canvas_width: 画布宽度（厘米）
        max_height: 最大高度（厘米）
        image_spacing: 图片间距（厘米）
        horizontal_priority: 水平优先级（百分比）
        gap_filling_priority: 空隙填充优先级（百分比）
        rotation_priority: 旋转优先级（百分比）
        output_path: 输出文件路径
        visualize: 是否可视化结果
        ppi: 每英寸像素数

    Returns:
        Dict[str, Any]: 测试结果
    """
    # 转换单位：厘米 -> 像素
    def cm_to_px(cm):
        inches = cm / 2.54
        return int(round(inches * ppi))

    # 转换画布宽度和图片间距
    canvas_width_px = cm_to_px(canvas_width)
    max_height_px = cm_to_px(max_height)
    image_spacing_px = cm_to_px(image_spacing)

    log.info(f"测试参数:")
    log.info(f"  - 画布宽度: {canvas_width}厘米 ({canvas_width_px}像素)")
    log.info(f"  - 最大高度: {max_height}厘米 ({max_height_px}像素)")
    log.info(f"  - 图片间距: {image_spacing}厘米 ({image_spacing_px}像素)")
    log.info(f"  - 水平优先级: {horizontal_priority}%")
    log.info(f"  - 空隙填充优先级: {gap_filling_priority}%")
    log.info(f"  - 旋转优先级: {rotation_priority}%")

    # 创建俄罗斯方块式装箱器
    packer = TetrisPacker(
        container_width=canvas_width_px,
        image_spacing=image_spacing_px,
        max_height=max_height_px
    )

    # 设置算法参数
    packer.horizontal_priority = horizontal_priority
    packer.gap_filling_priority = gap_filling_priority
    packer.rotation_priority = rotation_priority

    # 转换图片尺寸：厘米 -> 像素
    for img in images:
        img['width_px'] = cm_to_px(img['width'])
        img['height_px'] = cm_to_px(img['height'])

    # 按面积降序排序
    images.sort(key=lambda img: -(img['width'] * img['height']))

    # 放置图片
    start_time = time.time()
    successful_count = 0

    for i, img in enumerate(images):
        width_px = img['width_px']
        height_px = img['height_px']

        # 添加算法参数到图片数据
        img_data = {
            'c_horizontal_priority': horizontal_priority,
            'c_gap_filling_priority': gap_filling_priority,
            'c_rotation_priority': rotation_priority,
            'name': img['name'],
            'id': img['id'],
            'original_size': img.get('original_size', '')
        }

        # 尝试放置图片
        x, y, success = packer.find_position(width_px, height_px, img_data)

        if success:
            successful_count += 1
            log.info(f"成功放置图片 {img['name']} ({width_px}x{height_px}) 在 ({x}, {y})")
        else:
            log.warning(f"无法放置图片 {img['name']} ({width_px}x{height_px})")

    # 尝试重新分配图片
    log.info("尝试重新分配图片，优化整体布局...")
    redistribution_success = packer.redistribute_images()
    if redistribution_success:
        log.info("重新分配图片成功")
    else:
        log.info("重新分配图片失败或无需重新分配")

    # 计算性能
    elapsed_time = time.time() - start_time
    images_per_second = successful_count / elapsed_time if elapsed_time > 0 else 0

    # 计算利用率
    utilization = packer.get_utilization() * 100

    # 计算行利用率
    row_heights = {}

    for img in packer.get_placed_images():
        y = img['y']
        height = img['height']

        # 更新行高度
        for row_y in range(y, y + height):
            row_heights[row_y] = row_heights.get(row_y, 0) + 1

    # 计算非空行数
    non_empty_rows = len(row_heights)

    # 计算平均行利用率
    total_row_utilization = 0
    for y in range(packer.max_height):
        if y in row_heights:
            # 计算该行的利用率
            row_util = sum(img['width'] for img in packer.get_placed_images()
                          if img['y'] <= y < img['y'] + img['height']) / packer.container_width
            total_row_utilization += row_util

    avg_row_utilization = (total_row_utilization / non_empty_rows * 100) if non_empty_rows > 0 else 0

    # 输出结果
    log.info(f"布局完成:")
    log.info(f"  - 成功放置: {successful_count}/{len(images)} 个图片 ({successful_count/len(images)*100:.2f}%)")
    log.info(f"  - 画布利用率: {utilization:.2f}%")
    log.info(f"  - 平均行利用率: {avg_row_utilization:.2f}%")
    log.info(f"  - 最大高度: {packer.max_height}像素 ({packer.max_height/ppi*2.54:.2f}厘米)")
    log.info(f"  - 处理时间: {elapsed_time:.2f}秒")
    log.info(f"  - 处理速度: {images_per_second:.2f}图片/秒")

    # 可视化结果
    if visualize:
        title = f"C类俄罗斯方块 (H={horizontal_priority}, G={gap_filling_priority}, R={rotation_priority})"
        visualize_layout(packer, output_path, title, show_grid=True, show_details=True)

    # 返回测试结果
    return {
        'utilization': utilization,
        'row_utilization': avg_row_utilization,
        'success_rate': successful_count / len(images) * 100,
        'max_height': packer.max_height,
        'max_height_cm': packer.max_height / ppi * 2.54,
        'processing_time': elapsed_time,
        'processing_speed': images_per_second,
        'horizontal_priority': horizontal_priority,
        'gap_filling_priority': gap_filling_priority,
        'rotation_priority': rotation_priority,
        'placed_count': successful_count,
        'total_count': len(images)
    }


def auto_tune_parameters(
    images: List[Dict[str, Any]],
    canvas_width: float = 160,
    max_height: float = 5000,
    image_spacing: float = 0.1,
    output_dir: str = 'results',
    visualize: bool = True,
    ppi: int = 72,
    iterations: int = 50,
    scale_factor: float = 0.05
):
    """
    自动调优算法参数

    Args:
        images: 图片列表
        canvas_width: 画布宽度（厘米）
        max_height: 最大高度（厘米）
        image_spacing: 图片间距（厘米）
        output_dir: 输出目录
        visualize: 是否可视化结果
        ppi: 每英寸像素数
        iterations: 迭代次数
        scale_factor: 缩放比例，用于创建等比例缩小模型

    Returns:
        Dict[str, Any]: 最佳参数
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 创建迭代子目录
    iterations_dir = os.path.join(output_dir, 'iterations')
    os.makedirs(iterations_dir, exist_ok=True)

    # 缩放图片尺寸
    scaled_images = []
    for img in images:
        scaled_img = img.copy()
        scaled_img['width'] = img['width'] * scale_factor
        scaled_img['height'] = img['height'] * scale_factor
        # 确保最小尺寸
        scaled_img['width'] = max(1, scaled_img['width'])
        scaled_img['height'] = max(1, scaled_img['height'])
        scaled_images.append(scaled_img)

    log.info(f"创建了缩放比例为 {scale_factor} 的等比例模型")
    log.info(f"原始画布宽度: {canvas_width}厘米, 缩放后: {canvas_width * scale_factor}厘米")
    log.info(f"原始图片数量: {len(images)}, 缩放后图片数量: {len(scaled_images)}")

    # 显示部分缩放后的图片尺寸
    for i, img in enumerate(scaled_images[:5]):
        log.info(f"  缩放后图片 {i+1}: {img['width']:.2f}x{img['height']:.2f}厘米")

    # 使用智能参数搜索策略
    # 首先测试一些预设的"好"参数组合，然后在有希望的区域进行更细致的搜索

    # 预设参数组合
    preset_combinations = [
        (80, 70, 60),  # 默认参数
        (90, 80, 50),  # 高水平优先级
        (85, 85, 40),  # 高空隙填充优先级
        (70, 60, 70),  # 高旋转优先级
        (95, 75, 55),  # 极高水平优先级
        (75, 85, 45)   # 平衡参数
    ]

    # 参数范围
    horizontal_range = range(60, 100, 5)  # 60, 65, 70, ..., 95
    gap_filling_range = range(50, 90, 5)  # 50, 55, 60, ..., 85
    rotation_range = range(30, 80, 5)     # 30, 35, 40, ..., 75

    # 存储结果
    all_results = []

    # 计算总迭代次数
    total_combinations = len(horizontal_range) * len(gap_filling_range) * len(rotation_range)
    log.info(f"参数组合总数: {total_combinations}")

    # 首先测试预设参数组合
    log.info(f"首先测试 {len(preset_combinations)} 个预设参数组合")

    # 如果迭代次数小于预设组合数，只使用部分预设组合
    if iterations <= len(preset_combinations):
        selected_combinations = preset_combinations[:iterations]
        log.info(f"迭代次数较少，只使用前 {iterations} 个预设参数组合")
    else:
        # 使用所有预设组合，然后随机选择剩余的组合
        selected_combinations = preset_combinations.copy()

        # 生成所有可能的组合（排除预设组合）
        all_combinations = []
        for h in horizontal_range:
            for g in gap_filling_range:
                for r in rotation_range:
                    if (h, g, r) not in preset_combinations:
                        all_combinations.append((h, g, r))

        # 随机选择剩余的组合
        remaining_iterations = iterations - len(preset_combinations)
        if remaining_iterations > 0:
            if remaining_iterations >= len(all_combinations):
                # 如果剩余迭代次数足够多，使用所有组合
                selected_combinations.extend(all_combinations)
                log.info(f"将测试所有 {len(selected_combinations)} 个参数组合")
            else:
                # 随机选择剩余迭代次数个组合
                import random
                random.shuffle(all_combinations)
                selected_combinations.extend(all_combinations[:remaining_iterations])
                log.info(f"随机选择了 {remaining_iterations} 个额外参数组合，总共 {len(selected_combinations)} 个组合")

    # 测试选定的参数组合
    for i, (h, g, r) in enumerate(selected_combinations):
        log.info(f"迭代 {i+1}/{len(selected_combinations)}: 测试参数组合 H={h}, G={g}, R={r}")

        # 输出文件路径
        output_path = os.path.join(iterations_dir, f"iter_{i+1}_H{h}_G{g}_R{r}.png")

        # 测试算法
        result = test_tetris_algorithm(
            images=scaled_images,
            canvas_width=canvas_width * scale_factor,
            max_height=max_height * scale_factor,
            image_spacing=image_spacing * scale_factor,
            horizontal_priority=h,
            gap_filling_priority=g,
            rotation_priority=r,
            output_path=output_path if visualize else None,
            visualize=visualize,
            ppi=ppi
        )

        # 添加迭代信息和参数
        result['iteration'] = i + 1
        result['horizontal_priority'] = h
        result['gap_filling_priority'] = g
        result['rotation_priority'] = r

        # 添加到结果列表
        all_results.append(result)

        # 计算综合得分
        # 归一化各项指标
        norm_utilization = result['utilization'] / 100
        norm_row_util = result['row_utilization'] / 100
        norm_success = result['success_rate'] / 100

        # 归一化高度（越低越好）
        max_height_cm = result['max_height_cm']
        norm_height = 1.0 - min(1.0, max_height_cm / (max_height * scale_factor))

        # 计算综合得分
        # 权重可以根据需要调整
        util_weight = 0.4       # 画布利用率权重
        row_util_weight = 0.3   # 行利用率权重
        height_weight = 0.2     # 高度权重
        success_weight = 0.1    # 成功率权重

        score = (
            util_weight * norm_utilization +
            row_util_weight * norm_row_util +
            height_weight * norm_height +
            success_weight * norm_success
        ) * 100

        result['score'] = score

        log.info(f"  - 综合得分: {score:.2f}")
        log.info(f"  - 画布利用率: {result['utilization']:.2f}%")
        log.info(f"  - 平均行利用率: {result['row_utilization']:.2f}%")
        log.info(f"  - 最大高度: {result['max_height_cm']:.2f}厘米")
        log.info(f"  - 成功率: {result['success_rate']:.2f}%")

    # 按得分降序排序
    all_results.sort(key=lambda x: -x['score'])

    # 输出最佳参数
    best_result = all_results[0]
    log.info(f"\n最佳参数组合 (迭代 {best_result['iteration']}):")
    log.info(f"  - 水平优先级: {best_result['horizontal_priority']}%")
    log.info(f"  - 空隙填充优先级: {best_result['gap_filling_priority']}%")
    log.info(f"  - 旋转优先级: {best_result['rotation_priority']}%")
    log.info(f"  - 综合得分: {best_result['score']:.2f}")
    log.info(f"  - 画布利用率: {best_result['utilization']:.2f}%")
    log.info(f"  - 平均行利用率: {best_result['row_utilization']:.2f}%")
    log.info(f"  - 最大高度: {best_result['max_height_cm']:.2f}厘米")
    log.info(f"  - 成功率: {best_result['success_rate']:.2f}%")

    # 保存所有结果
    all_results_path = os.path.join(output_dir, 'all_results.json')
    import json
    with open(all_results_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)

    log.info(f"所有迭代结果已保存到: {all_results_path}")

    # 保存最佳参数
    best_params_path = os.path.join(output_dir, 'best_params.json')
    with open(best_params_path, 'w', encoding='utf-8') as f:
        json.dump(best_result, f, indent=2, ensure_ascii=False)

    log.info(f"最佳参数已保存到: {best_params_path}")

    # 使用最佳参数在原始尺寸上测试
    log.info("\n使用最佳参数在原始尺寸上测试...")
    best_output_path = os.path.join(output_dir, 'best_result.png')

    # 使用最佳参数在原始尺寸上测试
    test_tetris_algorithm(
        images=images,
        canvas_width=canvas_width,
        max_height=max_height,
        image_spacing=image_spacing,
        horizontal_priority=best_result['horizontal_priority'],
        gap_filling_priority=best_result['gap_filling_priority'],
        rotation_priority=best_result['rotation_priority'],
        output_path=best_output_path if visualize else None,
        visualize=visualize,
        ppi=ppi
    )

    # 生成参数调优报告
    generate_tuning_report(all_results, output_dir)

    return best_result

def generate_tuning_report(results: List[Dict[str, Any]], output_dir: str):
    """
    生成参数调优报告

    Args:
        results: 所有迭代结果
        output_dir: 输出目录
    """
    import matplotlib.pyplot as plt
    import numpy as np
    import json
    from datetime import datetime

    # 创建报告目录
    report_dir = os.path.join(output_dir, 'report')
    os.makedirs(report_dir, exist_ok=True)

    # 记录报告生成时间
    report_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 保存原始结果数据为JSON，方便后续分析
    with open(os.path.join(report_dir, 'raw_results.json'), 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    log.info(f"原始结果数据已保存到: {os.path.join(report_dir, 'raw_results.json')}")

    # 提取数据
    iterations = [r['iteration'] for r in results]
    scores = [r['score'] for r in results]
    utilizations = [r['utilization'] for r in results]
    row_utilizations = [r['row_utilization'] for r in results]
    heights = [r['max_height_cm'] for r in results]
    h_priorities = [r['horizontal_priority'] for r in results]
    g_priorities = [r['gap_filling_priority'] for r in results]
    r_priorities = [r['rotation_priority'] for r in results]
    success_rates = [r.get('success_rate', 0) for r in results]
    processing_times = [r.get('processing_time', 0) for r in results]

    # 计算额外的统计数据
    avg_score = np.mean(scores)
    max_score = np.max(scores)
    min_score = np.min(scores)
    std_score = np.std(scores)

    avg_utilization = np.mean(utilizations)
    max_utilization = np.max(utilizations)
    min_utilization = np.min(utilizations)

    avg_row_utilization = np.mean(row_utilizations)
    max_row_utilization = np.max(row_utilizations)
    min_row_utilization = np.min(row_utilizations)

    avg_height = np.mean(heights)
    min_height = np.min(heights)
    max_height = np.max(heights)

    # 按迭代顺序排序
    sorted_indices = np.argsort(iterations)
    iterations = [iterations[i] for i in sorted_indices]
    scores = [scores[i] for i in sorted_indices]
    utilizations = [utilizations[i] for i in sorted_indices]
    row_utilizations = [row_utilizations[i] for i in sorted_indices]
    heights = [heights[i] for i in sorted_indices]
    h_priorities = [h_priorities[i] for i in sorted_indices]
    g_priorities = [g_priorities[i] for i in sorted_indices]
    r_priorities = [r_priorities[i] for i in sorted_indices]
    success_rates = [success_rates[i] for i in sorted_indices]
    processing_times = [processing_times[i] for i in sorted_indices]

    # 创建图表目录
    charts_dir = os.path.join(report_dir, 'charts')
    os.makedirs(charts_dir, exist_ok=True)

    # 1. 绘制得分变化图
    plt.figure(figsize=(10, 6))
    plt.plot(iterations, scores, 'o-', label='综合得分', color='#3498db', linewidth=2)

    # 添加最佳得分标记
    best_idx = scores.index(max(scores))
    plt.scatter([iterations[best_idx]], [scores[best_idx]], color='red', s=100, zorder=5, label='最佳得分')
    plt.annotate(f'最佳: {scores[best_idx]:.2f}',
                xy=(iterations[best_idx], scores[best_idx]),
                xytext=(iterations[best_idx]+0.5, scores[best_idx]+2),
                arrowprops=dict(facecolor='black', shrink=0.05, width=1.5))

    # 添加平均得分线
    plt.axhline(y=np.mean(scores), color='green', linestyle='--', label=f'平均: {np.mean(scores):.2f}')

    plt.xlabel('迭代次数')
    plt.ylabel('得分')
    plt.title('参数调优过程中的得分变化')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.savefig(os.path.join(charts_dir, 'scores.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # 2. 绘制利用率变化图
    plt.figure(figsize=(10, 6))
    plt.plot(iterations, utilizations, 'o-', label='画布利用率', color='#2ecc71', linewidth=2)
    plt.plot(iterations, row_utilizations, 'o-', label='行利用率', color='#e74c3c', linewidth=2)

    # 添加最佳利用率标记
    best_util_idx = utilizations.index(max(utilizations))
    plt.scatter([iterations[best_util_idx]], [utilizations[best_util_idx]],
                color='purple', s=100, zorder=5, label='最佳画布利用率')

    plt.xlabel('迭代次数')
    plt.ylabel('利用率 (%)')
    plt.title('参数调优过程中的利用率变化')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.savefig(os.path.join(charts_dir, 'utilizations.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # 3. 绘制高度变化图
    plt.figure(figsize=(10, 6))
    plt.plot(iterations, heights, 'o-', label='最大高度', color='#9b59b6', linewidth=2)

    # 添加最小高度标记
    min_height_idx = heights.index(min(heights))
    plt.scatter([iterations[min_height_idx]], [heights[min_height_idx]],
                color='red', s=100, zorder=5, label='最小高度')

    plt.xlabel('迭代次数')
    plt.ylabel('高度 (厘米)')
    plt.title('参数调优过程中的高度变化')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.savefig(os.path.join(charts_dir, 'heights.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # 4. 绘制参数变化图
    plt.figure(figsize=(10, 6))
    plt.plot(iterations, h_priorities, 'o-', label='水平优先级', color='#3498db', linewidth=2)
    plt.plot(iterations, g_priorities, 'o-', label='空隙填充优先级', color='#2ecc71', linewidth=2)
    plt.plot(iterations, r_priorities, 'o-', label='旋转优先级', color='#e74c3c', linewidth=2)

    # 标记最佳参数组合
    best_idx = scores.index(max(scores))
    plt.scatter([iterations[best_idx]], [h_priorities[best_idx]], color='blue', s=100, zorder=5)
    plt.scatter([iterations[best_idx]], [g_priorities[best_idx]], color='green', s=100, zorder=5)
    plt.scatter([iterations[best_idx]], [r_priorities[best_idx]], color='red', s=100, zorder=5)

    plt.xlabel('迭代次数')
    plt.ylabel('参数值 (%)')
    plt.title('参数调优过程中的参数变化')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    plt.savefig(os.path.join(charts_dir, 'parameters.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # 5. 绘制参数与得分的关系图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))

    # 水平优先级与得分关系
    sc1 = axes[0].scatter(h_priorities, scores, c=scores, cmap='viridis', alpha=0.7)
    axes[0].set_xlabel('水平优先级')
    axes[0].set_ylabel('得分')
    axes[0].set_title('水平优先级与得分的关系')
    axes[0].grid(True, linestyle='--', alpha=0.7)
    fig.colorbar(sc1, ax=axes[0], label='得分')

    # 空隙填充优先级与得分关系
    sc2 = axes[1].scatter(g_priorities, scores, c=scores, cmap='viridis', alpha=0.7)
    axes[1].set_xlabel('空隙填充优先级')
    axes[1].set_ylabel('得分')
    axes[1].set_title('空隙填充优先级与得分的关系')
    axes[1].grid(True, linestyle='--', alpha=0.7)
    fig.colorbar(sc2, ax=axes[1], label='得分')

    # 旋转优先级与得分关系
    sc3 = axes[2].scatter(r_priorities, scores, c=scores, cmap='viridis', alpha=0.7)
    axes[2].set_xlabel('旋转优先级')
    axes[2].set_ylabel('得分')
    axes[2].set_title('旋转优先级与得分的关系')
    axes[2].grid(True, linestyle='--', alpha=0.7)
    fig.colorbar(sc3, ax=axes[2], label='得分')

    plt.tight_layout()
    plt.savefig(os.path.join(charts_dir, 'parameter_scores.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # 6. 绘制3D参数空间图
    from mpl_toolkits.mplot3d import Axes3D

    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # 绘制3D散点图
    sc = ax.scatter(h_priorities, g_priorities, r_priorities, c=scores, cmap='viridis', s=50, alpha=0.7)

    # 标记最佳点
    best_idx = scores.index(max(scores))
    ax.scatter([h_priorities[best_idx]], [g_priorities[best_idx]], [r_priorities[best_idx]],
              color='red', s=200, label='最佳参数')

    ax.set_xlabel('水平优先级')
    ax.set_ylabel('空隙填充优先级')
    ax.set_zlabel('旋转优先级')
    ax.set_title('参数空间中的得分分布')

    # 添加颜色条
    cbar = fig.colorbar(sc, ax=ax, shrink=0.5, aspect=5)
    cbar.set_label('得分')

    ax.legend()
    plt.savefig(os.path.join(charts_dir, 'parameter_space_3d.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # 7. 绘制成功率和处理时间图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10))

    # 成功率图
    ax1.plot(iterations, success_rates, 'o-', color='#f39c12', linewidth=2)
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('成功率 (%)')
    ax1.set_title('参数调优过程中的成功率变化')
    ax1.grid(True, linestyle='--', alpha=0.7)

    # 处理时间图
    ax2.plot(iterations, processing_times, 'o-', color='#16a085', linewidth=2)
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('处理时间 (秒)')
    ax2.set_title('参数调优过程中的处理时间变化')
    ax2.grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout()
    plt.savefig(os.path.join(charts_dir, 'performance_metrics.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # 生成HTML报告
    html_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>C类俄罗斯方块算法参数调优报告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }}
            h1, h2, h3 {{ color: #333; }}
            .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }}
            .chart {{ margin: 20px 0; text-align: center; }}
            .chart img {{ max-width: 100%; border: 1px solid #ddd; border-radius: 5px; box-shadow: 0 0 5px rgba(0,0,0,0.1); }}
            table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .best {{ background-color: #d4edda; }}
            .summary {{ display: flex; flex-wrap: wrap; margin: 20px 0; }}
            .summary-box {{ flex: 1; min-width: 200px; margin: 10px; padding: 15px; border-radius: 5px; box-shadow: 0 0 5px rgba(0,0,0,0.1); }}
            .summary-box h3 {{ margin-top: 0; }}
            .parameter-box {{ background-color: #e3f2fd; }}
            .score-box {{ background-color: #fff3cd; }}
            .utilization-box {{ background-color: #d1e7dd; }}
            .height-box {{ background-color: #f8d7da; }}
            .header {{ display: flex; justify-content: space-between; align-items: center; }}
            .header-info {{ text-align: right; font-size: 0.9em; color: #666; }}
            .chart-row {{ display: flex; flex-wrap: wrap; }}
            .chart-col {{ flex: 1; min-width: 300px; margin: 10px; }}
            .footer {{ margin-top: 30px; padding-top: 10px; border-top: 1px solid #ddd; font-size: 0.9em; color: #666; text-align: center; }}
            .nav {{ position: sticky; top: 0; background-color: white; padding: 10px 0; border-bottom: 1px solid #ddd; z-index: 100; }}
            .nav ul {{ display: flex; list-style: none; padding: 0; margin: 0; }}
            .nav li {{ margin-right: 20px; }}
            .nav a {{ text-decoration: none; color: #333; }}
            .nav a:hover {{ color: #007bff; }}
            .section {{ margin: 30px 0; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>C类俄罗斯方块算法参数调优报告</h1>
                <div class="header-info">
                    <p>生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                    <p>总迭代次数: {len(results)}</p>
                </div>
            </div>

            <div class="nav">
                <ul>
                    <li><a href="#summary">摘要</a></li>
                    <li><a href="#best-params">最佳参数</a></li>
                    <li><a href="#charts">图表分析</a></li>
                    <li><a href="#all-results">所有结果</a></li>
                    <li><a href="#recommendations">建议</a></li>
                </ul>
            </div>

            <div id="summary" class="section">
                <h2>调优摘要</h2>
                <div class="summary">
                    <div class="summary-box parameter-box">
                        <h3>最佳参数组合</h3>
                        <p>水平优先级: <strong>{results[0]['horizontal_priority']}%</strong></p>
                        <p>空隙填充优先级: <strong>{results[0]['gap_filling_priority']}%</strong></p>
                        <p>旋转优先级: <strong>{results[0]['rotation_priority']}%</strong></p>
                    </div>

                    <div class="summary-box score-box">
                        <h3>得分统计</h3>
                        <p>最高得分: <strong>{max(scores):.2f}</strong></p>
                        <p>平均得分: <strong>{np.mean(scores):.2f}</strong></p>
                        <p>标准差: <strong>{np.std(scores):.2f}</strong></p>
                    </div>

                    <div class="summary-box utilization-box">
                        <h3>利用率统计</h3>
                        <p>最高画布利用率: <strong>{max(utilizations):.2f}%</strong></p>
                        <p>平均画布利用率: <strong>{np.mean(utilizations):.2f}%</strong></p>
                        <p>最高行利用率: <strong>{max(row_utilizations):.2f}%</strong></p>
                    </div>

                    <div class="summary-box height-box">
                        <h3>高度统计</h3>
                        <p>最小高度: <strong>{min(heights):.2f}厘米</strong></p>
                        <p>平均高度: <strong>{np.mean(heights):.2f}厘米</strong></p>
                        <p>最大高度: <strong>{max(heights):.2f}厘米</strong></p>
                    </div>
                </div>
            </div>

            <div id="best-params" class="section">
                <h2>最佳参数组合详情</h2>
                <table>
                    <tr>
                        <th>参数</th>
                        <th>值</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>水平优先级</td>
                        <td>{results[0]['horizontal_priority']}%</td>
                        <td>影响水平空间利用率的优先级，值越高越优先考虑水平利用率</td>
                    </tr>
                    <tr>
                        <td>空隙填充优先级</td>
                        <td>{results[0]['gap_filling_priority']}%</td>
                        <td>影响空隙填充的优先级，值越高越优先填充现有行的空隙</td>
                    </tr>
                    <tr>
                        <td>旋转优先级</td>
                        <td>{results[0]['rotation_priority']}%</td>
                        <td>影响图片旋转决策的优先级，值越高越倾向于旋转图片</td>
                    </tr>
                    <tr>
                        <td>综合得分</td>
                        <td>{results[0]['score']:.2f}</td>
                        <td>综合评分，考虑了利用率、高度和成功率等因素</td>
                    </tr>
                    <tr>
                        <td>画布利用率</td>
                        <td>{results[0]['utilization']:.2f}%</td>
                        <td>整个画布的利用率</td>
                    </tr>
                    <tr>
                        <td>行利用率</td>
                        <td>{results[0]['row_utilization']:.2f}%</td>
                        <td>每一行的平均利用率</td>
                    </tr>
                    <tr>
                        <td>最大高度</td>
                        <td>{results[0]['max_height_cm']:.2f}厘米</td>
                        <td>布局的最大高度</td>
                    </tr>
                    <tr>
                        <td>成功率</td>
                        <td>{results[0]['success_rate']:.2f}%</td>
                        <td>成功放置图片的比例</td>
                    </tr>
                    <tr>
                        <td>处理时间</td>
                        <td>{results[0].get('processing_time', 0):.2f}秒</td>
                        <td>处理所有图片所需的时间</td>
                    </tr>
                </table>
            </div>

            <div id="charts" class="section">
                <h2>参数调优过程分析</h2>

                <div class="chart-row">
                    <div class="chart-col">
                        <div class="chart">
                            <h3>得分变化</h3>
                            <img src="charts/scores.png" alt="得分变化图">
                        </div>
                    </div>
                    <div class="chart-col">
                        <div class="chart">
                            <h3>利用率变化</h3>
                            <img src="charts/utilizations.png" alt="利用率变化图">
                        </div>
                    </div>
                </div>

                <div class="chart-row">
                    <div class="chart-col">
                        <div class="chart">
                            <h3>高度变化</h3>
                            <img src="charts/heights.png" alt="高度变化图">
                        </div>
                    </div>
                    <div class="chart-col">
                        <div class="chart">
                            <h3>参数变化</h3>
                            <img src="charts/parameters.png" alt="参数变化图">
                        </div>
                    </div>
                </div>

                <div class="chart">
                    <h3>参数与得分的关系</h3>
                    <img src="charts/parameter_scores.png" alt="参数与得分的关系图">
                </div>

                <div class="chart">
                    <h3>参数空间分布</h3>
                    <img src="charts/parameter_space_3d.png" alt="参数空间分布图">
                </div>

                <div class="chart">
                    <h3>性能指标</h3>
                    <img src="charts/performance_metrics.png" alt="性能指标图">
                </div>
            </div>

            <div id="recommendations" class="section">
                <h2>参数调优建议</h2>
                <p>根据调优结果，我们可以得出以下建议：</p>
                <ul>
                    <li>水平优先级设置为 <strong>{results[0]['horizontal_priority']}%</strong> 时，可以获得最佳的水平空间利用率。</li>
                    <li>空隙填充优先级设置为 <strong>{results[0]['gap_filling_priority']}%</strong> 时，可以有效填充现有行的空隙，减少垂直高度。</li>
                    <li>旋转优先级设置为 <strong>{results[0]['rotation_priority']}%</strong> 时，可以在保持图片原始方向的同时，适当旋转以提高利用率。</li>
                    <li>这组参数在测试数据集上的画布利用率为 <strong>{results[0]['utilization']:.2f}%</strong>，行利用率为 <strong>{results[0]['row_utilization']:.2f}%</strong>。</li>
                    <li>如果需要进一步优化，可以尝试在当前最佳参数附近进行更细致的搜索。</li>
                </ul>
            </div>

            <div id="all-results" class="section">
                <h2>所有迭代结果</h2>
                <p>下表列出了所有迭代的参数组合及其性能指标，按得分降序排列。</p>
                <table>
                    <tr>
                        <th>迭代</th>
                        <th>水平优先级</th>
                        <th>空隙填充优先级</th>
                        <th>旋转优先级</th>
                        <th>得分</th>
                        <th>画布利用率</th>
                        <th>行利用率</th>
                        <th>最大高度(厘米)</th>
                        <th>成功率</th>
                    </tr>
    """

    # 添加所有迭代结果
    for r in sorted(results, key=lambda x: -x['score']):
        is_best = r == results[0]
        html_report += f"""
            <tr class="{'best' if is_best else ''}">
                <td>{r['iteration']}</td>
                <td>{r['horizontal_priority']}%</td>
                <td>{r['gap_filling_priority']}%</td>
                <td>{r['rotation_priority']}%</td>
                <td>{r['score']:.2f}</td>
                <td>{r['utilization']:.2f}%</td>
                <td>{r['row_utilization']:.2f}%</td>
                <td>{r['max_height_cm']:.2f}</td>
                <td>{r['success_rate']:.2f}%</td>
            </tr>
        """

    html_report += """
                </table>
            </div>

            <div class="footer">
                <p>C类俄罗斯方块算法参数调优报告 | 由自动调优系统生成</p>
            </div>
        </div>
    </body>
    </html>
    """

    # 保存HTML报告
    with open(os.path.join(report_dir, 'report.html'), 'w', encoding='utf-8') as f:
        f.write(html_report)

    log.info(f"参数调优报告已生成: {os.path.join(report_dir, 'report.html')}")

    # 尝试自动打开报告
    try:
        import webbrowser
        report_path = os.path.join(report_dir, 'report.html')
        webbrowser.open('file://' + os.path.abspath(report_path))
        log.info("已自动打开报告，如果没有显示，请手动打开报告文件")
    except Exception as e:
        log.warning(f"无法自动打开报告: {e}")
        log.info(f"请手动打开报告文件: {os.path.join(report_dir, 'report.html')}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='C类俄罗斯方块算法测试')

    # 基本参数
    parser.add_argument('--width', type=float, default=160, help='画布宽度（厘米）')
    parser.add_argument('--max-height', type=float, default=5000, help='最大高度（厘米）')
    parser.add_argument('--spacing', type=float, default=0.1, help='图片间距（厘米）')
    parser.add_argument('--ppi', type=int, default=72, help='每英寸像素数')

    # 算法参数
    parser.add_argument('--horizontal', type=int, default=80, help='水平优先级（百分比）')
    parser.add_argument('--gap-filling', type=int, default=70, help='空隙填充优先级（百分比）')
    parser.add_argument('--rotation', type=int, default=60, help='旋转优先级（百分比）')

    # 输入输出参数
    parser.add_argument('--input', type=str, help='输入文件路径，每行一个尺寸')
    parser.add_argument('--output-dir', type=str, default='results', help='输出目录')
    parser.add_argument('--no-visualize', action='store_true', help='不生成可视化图像')

    # 自动调优参数
    parser.add_argument('--auto-tune', action='store_true', help='自动调优算法参数')
    parser.add_argument('--iterations', type=int, default=50, help='自动调优迭代次数')
    parser.add_argument('--scale-factor', type=float, default=0.1, help='缩放比例，用于创建等比例缩小模型')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 读取输入文件或使用命令行参数
    images = []
    if args.input:
        # 从文件读取尺寸
        with open(args.input, 'r', encoding='utf-8') as f:
            size_list = [line.strip() for line in f.readlines()]
        images = parse_size_list(size_list)
    else:
        # 使用示例尺寸
        size_list = [
            "40*48", "120✘45", "120✘55", "80*130", "115*50",
            "60*120", "90*140", "110*50", "70*120", "60*140"
        ]
        images = parse_size_list(size_list)

    # 如果没有图片，退出
    if not images:
        log.error("没有图片数据，退出")
        return

    # 输出图片信息
    log.info(f"共 {len(images)} 个图片:")
    for i, img in enumerate(images[:10]):  # 只显示前10个
        log.info(f"  {i+1}. {img['name']}: {img['width']}x{img['height']}厘米")
    if len(images) > 10:
        log.info(f"  ... 还有 {len(images)-10} 个图片")

    # 自动调优或使用指定参数
    if args.auto_tune:
        # 自动调优
        log.info(f"开始自动调优，迭代次数: {args.iterations}，缩放比例: {args.scale_factor}")
        best_result = auto_tune_parameters(
            images=images,
            canvas_width=args.width,
            max_height=args.max_height,
            image_spacing=args.spacing,
            output_dir=args.output_dir,
            visualize=not args.no_visualize,
            ppi=args.ppi,
            iterations=args.iterations,
            scale_factor=args.scale_factor
        )

        # 输出最佳参数
        log.info(f"\n最终最佳参数组合:")
        log.info(f"  - 水平优先级: {best_result['horizontal_priority']}%")
        log.info(f"  - 空隙填充优先级: {best_result['gap_filling_priority']}%")
        log.info(f"  - 旋转优先级: {best_result['rotation_priority']}%")
        log.info(f"  - 综合得分: {best_result['score']:.2f}")
        log.info(f"  - 画布利用率: {best_result['utilization']:.2f}%")
        log.info(f"  - 平均行利用率: {best_result['row_utilization']:.2f}%")
        log.info(f"  - 最大高度: {best_result['max_height_cm']:.2f}厘米")

        # 打开报告
        report_path = os.path.join(args.output_dir, 'report', 'report.html')
        log.info(f"\n参数调优报告已生成: {report_path}")
        log.info(f"请在浏览器中打开查看详细结果")
    else:
        # 使用指定参数
        log.info(f"使用指定参数: H={args.horizontal}, G={args.gap_filling}, R={args.rotation}")
        output_path = os.path.join(args.output_dir, 'tetris_result.png')
        result = test_tetris_algorithm(
            images=images,
            canvas_width=args.width,
            max_height=args.max_height,
            image_spacing=args.spacing,
            horizontal_priority=args.horizontal,
            gap_filling_priority=args.gap_filling,
            rotation_priority=args.rotation,
            output_path=output_path if not args.no_visualize else None,
            visualize=not args.no_visualize,
            ppi=args.ppi
        )

        # 输出结果
        log.info(f"\n测试结果:")
        log.info(f"  - 画布利用率: {result['utilization']:.2f}%")
        log.info(f"  - 平均行利用率: {result['row_utilization']:.2f}%")
        log.info(f"  - 最大高度: {result['max_height_cm']:.2f}厘米")
        log.info(f"  - 成功率: {result['success_rate']:.2f}%")

        if not args.no_visualize:
            log.info(f"\n布局可视化已保存到: {output_path}")


if __name__ == "__main__":
    main()
