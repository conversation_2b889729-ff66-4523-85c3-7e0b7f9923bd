#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tetris算法优化器模块

提供Tetris算法的并行优化功能：
1. 多线程碰撞检测
2. 并行参数优化
3. 空间分区优化
4. 性能监控和报告
"""

import os
import sys
import logging
import time
import concurrent.futures
import threading
import queue
from typing import List, Dict, Any, Tuple, Optional, Callable
import math
import random

# 导入自定义模块
from utils.parallel_manager import ParallelManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("TetrisOptimizer")

class TetrisOptimizer:
    """
    Tetris算法优化器，提供并行优化功能
    
    特性：
    1. 多线程碰撞检测
    2. 并行参数优化
    3. 空间分区优化
    4. 性能监控和报告
    """
    
    def __init__(self, tetris_packer=None):
        """
        初始化Tetris算法优化器
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        
        # 创建并行管理器
        self.parallel_manager = ParallelManager()
        
        # 创建锁
        self._lock = threading.Lock()
        
        # 创建线程池
        self.thread_pool = None
        
        # 创建进程池
        self.process_pool = None
        
        # 初始化网格优化参数
        self.grid_cell_size = 10  # 网格单元格大小
        self.grid_cells = {}  # 网格单元格，键为(grid_x, grid_y)，值为该单元格中的图片列表
        
        log.info("Tetris算法优化器初始化完成")
    
    def optimize_collision_detection(self, tetris_packer=None):
        """
        优化碰撞检测算法
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            
        Returns:
            bool: 是否成功优化
        """
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return False
        
        try:
            
            # 备份原始方法
            original_check_collision = packer._check_collision
            
            # 定义优化后的碰撞检测方法
            def optimized_check_collision(x, y, width, height):
                """优化后的碰撞检测方法"""
                # 检查边界条件
                if x < 0 or x + width > packer.container_width or y < 0:
                    return True
                
                # 如果是第一个图片，放在底部
                if not packer.placed_images:
                    return y > 0
                
                # 使用网格优化检查碰撞
                if packer.use_grid_optimization and len(packer.placed_images) > 10:
                    try:
                        # 计算当前矩形所在的网格单元格
                        grid_x_start = max(0, x // packer.grid_cell_size)
                        grid_x_end = min((x + width) // packer.grid_cell_size + 1, 
                                        packer.container_width // packer.grid_cell_size)
                        grid_y_start = max(0, y // packer.grid_cell_size)
                        grid_y_end = min((y + height) // packer.grid_cell_size + 1, 
                                        packer.max_height // packer.grid_cell_size if packer.max_height > 0 else sys.maxsize)
                        
                        # 只检查相关网格单元格中的图片
                        for grid_x in range(grid_x_start, grid_x_end):
                            for grid_y in range(grid_y_start, grid_y_end):
                                grid_key = (grid_x, grid_y)
                                if grid_key in packer.grid_cells:
                                    for img_index in packer.grid_cells[grid_key]:
                                        # 检查与该图片是否碰撞
                                        img = packer.placed_images[img_index]
                                        
                                        # 计算两个矩形的边界
                                        img_left = img['x']
                                        img_right = img['x'] + img['width']
                                        img_top = img['y']
                                        img_bottom = img['y'] + img['height']
                                        
                                        rect_left = x
                                        rect_right = x + width
                                        rect_top = y
                                        rect_bottom = y + height
                                        
                                        # 检查是否重叠
                                        if not (rect_right <= img_left or rect_left >= img_right or
                                                rect_bottom <= img_top or rect_top >= img_bottom):
                                            return True
                        
                        # 如果没有碰撞，返回False
                        return False
                    except Exception:
                        # 如果网格优化出错，回退到传统方法
                        pass
                
                # 使用传统方法检查是否与已放置的图片重叠
                for img in packer.placed_images:
                    # 计算两个矩形的边界
                    img_left = img['x']
                    img_right = img['x'] + img['width']
                    img_top = img['y']
                    img_bottom = img['y'] + img['height']
                    
                    rect_left = x
                    rect_right = x + width
                    rect_top = y
                    rect_bottom = y + height
                    
                    # 检查是否重叠
                    if not (rect_right <= img_left or rect_left >= img_right or
                            rect_bottom <= img_top or rect_top >= img_bottom):
                        return True
                
                return False
            
            # 替换碰撞检测方法
            packer._check_collision = optimized_check_collision
            
            # 优化网格参数
            packer.grid_cell_size = self.grid_cell_size
            packer.grid_cells = self.grid_cells
            
            log.info("碰撞检测算法优化完成")
            return True
            
        except Exception as e:
            log.error(f"优化碰撞检测算法失败: {str(e)}")
            return False
    
    def parallel_find_best_position(self, tetris_packer=None, width=0, height=0, image_data=None, max_workers=None):
        """
        并行查找最佳位置
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            width: 图片宽度
            height: 图片高度
            image_data: 图片数据
            max_workers: 最大工作线程数，如果为None则自动确定
            
        Returns:
            Tuple[int, int, bool, bool]: (x坐标, y坐标, 是否成功, 是否需要旋转)
        """
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return 0, 0, False, False
        
        try:
            
            # 确定最佳线程数
            if max_workers is None:
                max_workers = self.parallel_manager.get_optimal_workers(ParallelManager.TASK_TYPE_CPU)
            
            # 创建线程池
            if self.thread_pool is None:
                self.thread_pool = self.parallel_manager.create_thread_pool("tetris_position", ParallelManager.TASK_TYPE_CPU)
            
            # 定义评估位置的函数
            def evaluate_position(x, y, need_rotation):
                """评估指定位置的得分"""
                actual_width = width if not need_rotation else height
                actual_height = height if not need_rotation else width
                
                # 检查是否会发生碰撞
                if packer._check_collision(x, y, actual_width, actual_height):
                    return x, y, -float('inf'), need_rotation
                
                # 计算评分
                score = packer._calculate_position_score(x, y, actual_width, actual_height)
                
                return x, y, score, need_rotation
            
            # 生成候选位置
            candidates = []
            
            # 考虑不旋转的情况
            for x in range(0, packer.container_width - width + 1, max(1, packer.grid_cell_size // 2)):
                # 找到该位置的最低y坐标
                y = packer._find_lowest_y(x, width)
                candidates.append((x, y, False))
            
            # 考虑旋转的情况
            if width != height and packer.rotation_priority > 0:
                for x in range(0, packer.container_width - height + 1, max(1, packer.grid_cell_size // 2)):
                    # 找到该位置的最低y坐标
                    y = packer._find_lowest_y(x, height)
                    candidates.append((x, y, True))
            
            # 并行评估所有候选位置
            futures = []
            for x, y, need_rotation in candidates:
                futures.append(self.thread_pool.submit(evaluate_position, x, y, need_rotation))
            
            # 收集结果并找出最佳位置
            best_score = -float('inf')
            best_x = 0
            best_y = 0
            best_rotation = False
            
            for future in concurrent.futures.as_completed(futures):
                x, y, score, need_rotation = future.result()
                if score > best_score:
                    best_score = score
                    best_x = x
                    best_y = y
                    best_rotation = need_rotation
            
            # 判断是否找到有效位置
            success = best_score > -float('inf')
            
            return best_x, best_y, success, best_rotation
            
        except Exception as e:
            log.error(f"并行查找最佳位置失败: {str(e)}")
            return 0, 0, False, False
