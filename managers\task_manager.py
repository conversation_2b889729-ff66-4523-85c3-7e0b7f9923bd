#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务管理器
负责管理整个图片排版任务的生命周期，协调各个组件的工作

主要职责：
1. 管理任务队列和执行状态
2. 协调RectPack算法和Photoshop集成
3. 处理任务错误和恢复机制
4. 提供任务进度监控和反馈
5. 优化任务执行效率和资源使用
"""

import logging
import time
import os
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

# 导入管理器
from managers.rectpack_manager import RectPackManager
from managers.photoshop_integration_manager import PhotoshopIntegrationManager
from utils.memory_manager import MemoryManager

# 配置日志
log = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class Task:
    """任务类"""
    
    def __init__(self, task_id: str, material_name: str, sheet_name: str, 
                 pattern_items: List[Dict[str, Any]], canvas_config: Dict[str, Any],
                 priority: TaskPriority = TaskPriority.NORMAL):
        """初始化任务
        
        Args:
            task_id: 任务ID
            material_name: 材质名称
            sheet_name: 工作表名称
            pattern_items: 图片项目列表
            canvas_config: 画布配置
            priority: 任务优先级
        """
        self.task_id = task_id
        self.material_name = material_name
        self.sheet_name = sheet_name
        self.pattern_items = pattern_items
        self.canvas_config = canvas_config
        self.priority = priority
        
        self.status = TaskStatus.PENDING
        self.progress = 0.0
        self.start_time = None
        self.end_time = None
        self.error_message = ""
        self.retry_count = 0
        self.max_retries = 3
        
        # 结果数据
        self.arranged_images = []
        self.output_path = ""
        self.utilization_rate = 0.0
        self.processing_time = 0.0


class TaskManager(QObject):
    """任务管理器
    
    负责管理整个任务执行流程，包括：
    - 任务队列管理
    - 组件协调
    - 错误处理和恢复
    - 性能监控
    """
    
    # 信号定义
    task_started = pyqtSignal(str)                    # 任务开始信号
    task_progress = pyqtSignal(str, float, str)       # 任务进度信号 (task_id, progress, status)
    task_completed = pyqtSignal(str, dict)            # 任务完成信号 (task_id, result)
    task_failed = pyqtSignal(str, str)                # 任务失败信号 (task_id, error)
    queue_status_changed = pyqtSignal(int, int)       # 队列状态变化信号 (pending, running)
    
    def __init__(self, config_manager=None):
        """初始化任务管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        super().__init__()
        self.config_manager = config_manager
        
        # 任务队列
        self.task_queue = []
        self.running_tasks = {}
        self.completed_tasks = {}
        self.failed_tasks = {}
        
        # 组件管理器
        self.rectpack_manager = RectPackManager(config_manager)
        self.photoshop_manager = PhotoshopIntegrationManager(config_manager)
        self.memory_manager = MemoryManager()
        
        # 执行控制
        self.max_concurrent_tasks = 1  # 目前只支持单任务执行
        self.is_paused = False
        self.is_shutting_down = False
        
        # 性能监控
        self.total_tasks_processed = 0
        self.total_processing_time = 0.0
        self.average_utilization = 0.0
        
        # 连接信号
        self._connect_signals()
        
        # 设置任务执行定时器
        self.execution_timer = QTimer()
        self.execution_timer.timeout.connect(self._process_task_queue)
        self.execution_timer.start(1000)  # 每秒检查一次任务队列
        
        log.info("任务管理器初始化完成")
    
    def _connect_signals(self):
        """连接各组件的信号"""
        # RectPack管理器信号
        self.rectpack_manager.progress_updated.connect(self._handle_rectpack_progress)
        self.rectpack_manager.error_occurred.connect(self._handle_rectpack_error)
        self.rectpack_manager.utilization_updated.connect(self._handle_utilization_update)
        
        # Photoshop管理器信号
        self.photoshop_manager.connection_status_changed.connect(self._handle_ps_connection_status)
        self.photoshop_manager.operation_progress.connect(self._handle_ps_operation_progress)
        self.photoshop_manager.error_occurred.connect(self._handle_ps_error)
    
    def add_task(self, material_name: str, sheet_name: str, pattern_items: List[Dict[str, Any]],
                 canvas_config: Dict[str, Any], priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """添加任务到队列
        
        Args:
            material_name: 材质名称
            sheet_name: 工作表名称
            pattern_items: 图片项目列表
            canvas_config: 画布配置
            priority: 任务优先级
            
        Returns:
            str: 任务ID
        """
        task_id = f"{material_name}_{sheet_name}_{int(time.time())}"
        
        task = Task(
            task_id=task_id,
            material_name=material_name,
            sheet_name=sheet_name,
            pattern_items=pattern_items,
            canvas_config=canvas_config,
            priority=priority
        )
        
        # 按优先级插入队列
        self._insert_task_by_priority(task)
        
        log.info(f"任务已添加到队列: {task_id} (优先级: {priority.name})")
        self._emit_queue_status()
        
        return task_id
    
    def _insert_task_by_priority(self, task: Task):
        """按优先级插入任务到队列
        
        Args:
            task: 任务对象
        """
        # 找到合适的插入位置
        insert_index = len(self.task_queue)
        for i, existing_task in enumerate(self.task_queue):
            if task.priority.value > existing_task.priority.value:
                insert_index = i
                break
        
        self.task_queue.insert(insert_index, task)
    
    def _process_task_queue(self):
        """处理任务队列"""
        if self.is_paused or self.is_shutting_down:
            return
        
        # 检查是否有可执行的任务
        if len(self.running_tasks) >= self.max_concurrent_tasks:
            return
        
        if not self.task_queue:
            return
        
        # 获取下一个任务
        task = self.task_queue.pop(0)
        self._execute_task(task)
    
    def _execute_task(self, task: Task):
        """执行任务
        
        Args:
            task: 任务对象
        """
        try:
            task.status = TaskStatus.RUNNING
            task.start_time = time.time()
            self.running_tasks[task.task_id] = task
            
            log.info(f"开始执行任务: {task.task_id}")
            self.task_started.emit(task.task_id)
            self._emit_queue_status()
            
            # 检查内存使用情况
            self.memory_manager.check_memory_usage()
            
            # 第一阶段：初始化RectPack算法
            self._update_task_progress(task, 10, "初始化RectPack算法...")
            
            canvas_config = task.canvas_config
            rectpack_init_success = self.rectpack_manager.initialize_algorithm(
                canvas_width_m=canvas_config.get('canvas_width_m', 2.0),
                max_height_cm=canvas_config.get('max_height_cm', 5000),
                image_spacing_cm=canvas_config.get('image_spacing_cm', 0.1),
                ppi=canvas_config.get('ppi', 72)
            )
            
            if not rectpack_init_success:
                raise Exception("RectPack算法初始化失败")
            
            # 第二阶段：初始化Photoshop连接
            self._update_task_progress(task, 20, "初始化Photoshop连接...")
            
            test_mode = self.config_manager.get_test_mode_settings().get('is_test_mode', False) if self.config_manager else False
            ps_init_success = self.photoshop_manager.initialize_connection(test_mode)
            
            if not ps_init_success and not test_mode:
                raise Exception("Photoshop连接初始化失败")
            
            # 第三阶段：使用RectPack算法排列图片
            self._update_task_progress(task, 30, "使用RectPack算法排列图片...")
            
            arranged_images, arrange_success = self.rectpack_manager.process_images(task.pattern_items)
            
            if not arrange_success:
                raise Exception("图片排列失败")
            
            task.arranged_images = arranged_images
            
            # 第四阶段：创建Photoshop画布
            self._update_task_progress(task, 60, "创建Photoshop画布...")
            
            canvas_name = f"{task.material_name}_{task.sheet_name}"
            canvas_width_px = canvas_config.get('canvas_width_px', 2000)
            canvas_height_px = canvas_config.get('canvas_height_px', 3000)
            ppi = canvas_config.get('ppi', 72)
            
            if not test_mode:
                canvas_success = self.photoshop_manager.create_canvas(
                    width=canvas_width_px,
                    height=canvas_height_px,
                    name=canvas_name,
                    ppi=ppi
                )
                
                if not canvas_success:
                    raise Exception("画布创建失败")
            
            # 第五阶段：放置图片
            self._update_task_progress(task, 70, "放置图片到画布...")
            
            if not test_mode:
                successful_count, failed_count = self.photoshop_manager.place_images_batch(arranged_images)
                
                if failed_count > 0:
                    log.warning(f"部分图片放置失败: 成功 {successful_count}, 失败 {failed_count}")
            
            # 第六阶段：保存画布
            self._update_task_progress(task, 90, "保存画布...")
            
            output_path = self._generate_output_path(task)
            task.output_path = output_path
            
            if not test_mode:
                save_success = self.photoshop_manager.save_and_close_canvas(output_path)
                
                if not save_success:
                    raise Exception("画布保存失败")
            
            # 任务完成
            task.status = TaskStatus.COMPLETED
            task.end_time = time.time()
            task.processing_time = task.end_time - task.start_time
            task.utilization_rate = self.rectpack_manager.last_utilization
            
            self._update_task_progress(task, 100, "任务完成")
            
            # 移动到完成队列
            self.running_tasks.pop(task.task_id)
            self.completed_tasks[task.task_id] = task
            
            # 更新统计信息
            self._update_performance_stats(task)
            
            # 发送完成信号
            result = {
                'output_path': task.output_path,
                'utilization_rate': task.utilization_rate,
                'processing_time': task.processing_time,
                'arranged_images_count': len(task.arranged_images)
            }
            self.task_completed.emit(task.task_id, result)
            
            log.info(f"任务执行完成: {task.task_id}, 耗时: {task.processing_time:.2f}秒, 利用率: {task.utilization_rate:.2f}%")
            
        except Exception as e:
            self._handle_task_error(task, str(e))
        
        finally:
            self._emit_queue_status()
    
    def _handle_task_error(self, task: Task, error_message: str):
        """处理任务错误
        
        Args:
            task: 任务对象
            error_message: 错误消息
        """
        task.error_message = error_message
        task.retry_count += 1
        
        log.error(f"任务执行失败: {task.task_id}, 错误: {error_message}, 重试次数: {task.retry_count}")
        
        # 检查是否可以重试
        if task.retry_count <= task.max_retries:
            log.info(f"准备重试任务: {task.task_id} (第{task.retry_count}次重试)")
            task.status = TaskStatus.PENDING
            self._insert_task_by_priority(task)
        else:
            # 重试次数用完，标记为失败
            task.status = TaskStatus.FAILED
            task.end_time = time.time()
            
            # 移动到失败队列
            if task.task_id in self.running_tasks:
                self.running_tasks.pop(task.task_id)
            self.failed_tasks[task.task_id] = task
            
            # 发送失败信号
            self.task_failed.emit(task.task_id, error_message)
    
    def _update_task_progress(self, task: Task, progress: float, status: str):
        """更新任务进度
        
        Args:
            task: 任务对象
            progress: 进度百分比
            status: 状态描述
        """
        task.progress = progress
        self.task_progress.emit(task.task_id, progress, status)
    
    def _generate_output_path(self, task: Task) -> str:
        """生成输出路径
        
        Args:
            task: 任务对象
            
        Returns:
            str: 输出路径
        """
        # 这里应该根据实际需求生成输出路径
        output_dir = task.canvas_config.get('output_dir', 'output')
        filename = f"{task.material_name}_{task.sheet_name}.tiff"
        return os.path.join(output_dir, filename)
    
    def _update_performance_stats(self, task: Task):
        """更新性能统计信息
        
        Args:
            task: 任务对象
        """
        self.total_tasks_processed += 1
        self.total_processing_time += task.processing_time
        
        # 计算平均利用率
        total_utilization = self.average_utilization * (self.total_tasks_processed - 1) + task.utilization_rate
        self.average_utilization = total_utilization / self.total_tasks_processed
    
    def _emit_queue_status(self):
        """发送队列状态信号"""
        pending_count = len(self.task_queue)
        running_count = len(self.running_tasks)
        self.queue_status_changed.emit(pending_count, running_count)
    
    def _handle_rectpack_progress(self, progress: int):
        """处理RectPack进度更新"""
        # 这里可以根据需要更新当前运行任务的进度
        pass
    
    def _handle_rectpack_error(self, error_message: str):
        """处理RectPack错误"""
        log.error(f"RectPack错误: {error_message}")
    
    def _handle_utilization_update(self, utilization: float):
        """处理利用率更新"""
        # 这里可以根据需要更新UI显示
        pass
    
    def _handle_ps_connection_status(self, is_connected: bool, message: str):
        """处理Photoshop连接状态变化"""
        if not is_connected:
            log.warning(f"Photoshop连接状态变化: {message}")
    
    def _handle_ps_operation_progress(self, progress: int, status: str):
        """处理Photoshop操作进度"""
        # 这里可以根据需要更新当前任务的进度
        pass
    
    def _handle_ps_error(self, error_type: str, error_message: str):
        """处理Photoshop错误"""
        log.error(f"Photoshop错误 [{error_type}]: {error_message}")
    
    def pause_execution(self):
        """暂停任务执行"""
        self.is_paused = True
        log.info("任务执行已暂停")
    
    def resume_execution(self):
        """恢复任务执行"""
        self.is_paused = False
        log.info("任务执行已恢复")
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否取消成功
        """
        # 从队列中移除
        for i, task in enumerate(self.task_queue):
            if task.task_id == task_id:
                task.status = TaskStatus.CANCELLED
                self.task_queue.pop(i)
                log.info(f"任务已从队列中取消: {task_id}")
                self._emit_queue_status()
                return True
        
        # 如果是正在运行的任务，标记为取消（实际停止可能需要更复杂的逻辑）
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.status = TaskStatus.CANCELLED
            log.info(f"正在运行的任务已标记为取消: {task_id}")
            return True
        
        return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        # 查找任务
        task = None
        for task_list in [self.task_queue, self.running_tasks.values(), 
                         self.completed_tasks.values(), self.failed_tasks.values()]:
            for t in task_list:
                if t.task_id == task_id:
                    task = t
                    break
            if task:
                break
        
        if not task:
            return None
        
        return {
            'task_id': task.task_id,
            'material_name': task.material_name,
            'sheet_name': task.sheet_name,
            'status': task.status.value,
            'progress': task.progress,
            'start_time': task.start_time,
            'end_time': task.end_time,
            'processing_time': task.processing_time,
            'utilization_rate': task.utilization_rate,
            'error_message': task.error_message,
            'retry_count': task.retry_count,
            'output_path': task.output_path
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要
        
        Returns:
            Dict[str, Any]: 性能摘要数据
        """
        return {
            'total_tasks_processed': self.total_tasks_processed,
            'total_processing_time': self.total_processing_time,
            'average_processing_time': self.total_processing_time / self.total_tasks_processed if self.total_tasks_processed > 0 else 0,
            'average_utilization': self.average_utilization,
            'pending_tasks': len(self.task_queue),
            'running_tasks': len(self.running_tasks),
            'completed_tasks': len(self.completed_tasks),
            'failed_tasks': len(self.failed_tasks),
            'rectpack_stats': self.rectpack_manager.get_performance_stats(),
            'photoshop_stats': self.photoshop_manager.get_performance_stats()
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            self.is_shutting_down = True
            
            # 停止执行定时器
            if self.execution_timer:
                self.execution_timer.stop()
            
            # 清理组件管理器
            self.rectpack_manager.cleanup()
            self.photoshop_manager.cleanup()
            self.memory_manager.cleanup()
            
            log.info("任务管理器资源清理完成")
            
        except Exception as e:
            log.error(f"清理任务管理器资源失败: {str(e)}")
