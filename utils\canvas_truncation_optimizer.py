#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
画布截断优化器模块

提供画布截断优化功能：
1. 在截断前尽可能填充底部行
2. 评估是否应将底部行图片移至下一画布
3. 优化画布高度，提高整体利用率
"""

import logging
import copy
from typing import List, Dict, Any, Tuple, Optional

# 导入底部行优化器
from utils.bottom_row_optimizer import BottomRowOptimizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("CanvasTruncationOptimizer")

class CanvasTruncationOptimizer:
    """
    画布截断优化器，提供画布截断优化功能

    特性：
    1. 在截断前尽可能填充底部行
    2. 评估是否应将底部行图片移至下一画布
    3. 优化画布高度，提高整体利用率
    """

    def __init__(self, tetris_packer=None):
        """
        初始化画布截断优化器

        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        self.bottom_row_optimizer = BottomRowOptimizer(tetris_packer)
        log.info("画布截断优化器初始化完成")

    def optimize_before_truncation(self, tetris_packer=None, remaining_patterns: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        在画布截断前进行优化

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            Dict[str, Any]: 优化结果
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            log.error("未提供有效的Tetris算法实例或画布为空")
            return {'success': False, 'message': "未提供有效的Tetris算法实例或画布为空"}

        try:
            # 记录原始状态
            original_placed_images = copy.deepcopy(packer.placed_images)
            original_max_height = packer.get_max_height()
            original_utilization = packer.get_utilization()

            log.info(f"开始画布截断前优化，原始高度: {original_max_height}, 原始利用率: {original_utilization:.4f}")

            # 如果没有提供剩余图片，无法进行优化
            if not remaining_patterns:
                log.info("未提供剩余图片，无法进行优化")
                return {'success': False, 'message': "未提供剩余图片，无法进行优化"}

            # 尝试优化底部行
            bottom_row_optimized = self.bottom_row_optimizer.optimize_bottom_row(packer, remaining_patterns)

            # 如果底部行优化失败，尝试其他优化策略
            if not bottom_row_optimized:
                log.info("底部行优化失败，尝试其他优化策略")

                # 尝试移除底部行图片，看是否能提高利用率
                bottom_row_info = self.bottom_row_optimizer._get_bottom_row_info(packer)
                if bottom_row_info and bottom_row_info['width_used'] / packer.container_width < 0.5:
                    log.info("底部行利用率较低，尝试移除底部行图片")

                    # 保存当前状态
                    current_placed_images = copy.deepcopy(packer.placed_images)

                    # 移除底部行图片
                    removed_images = []
                    for img in bottom_row_info['images']:
                        if img in packer.placed_images:
                            packer.placed_images.remove(img)
                            pattern = img.get('pattern', {})
                            if pattern:
                                removed_images.append(pattern)

                    # 更新当前最大高度
                    packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0

                    # 计算移除后的利用率
                    new_utilization = packer.get_utilization()

                    # 如果移除后利用率降低，恢复原状态
                    if new_utilization < original_utilization:
                        log.info("移除底部行图片后利用率降低，恢复原状态")
                        packer.placed_images = current_placed_images
                        packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
                    else:
                        log.info(f"成功移除底部行图片，利用率从 {original_utilization:.4f} 提高到 {new_utilization:.4f}")
                        # 将移除的图片添加到剩余图片列表的开头，优先放置
                        for img in removed_images:
                            remaining_patterns.insert(0, img)
                        bottom_row_optimized = True

            # 计算优化后的状态
            new_max_height = packer.get_max_height()
            new_utilization = packer.get_utilization()

            # 计算改进百分比
            height_reduction = (original_max_height - new_max_height) / original_max_height * 100 if original_max_height > 0 else 0
            utilization_improvement = (new_utilization - original_utilization) / original_utilization * 100 if original_utilization > 0 else 0

            log.info(f"画布截断前优化完成，高度减少: {height_reduction:.2f}%, 利用率提升: {utilization_improvement:.2f}%")

            # 返回优化结果
            return {
                'success': bottom_row_optimized,
                'original_height': original_max_height,
                'new_height': new_max_height,
                'original_utilization': original_utilization,
                'new_utilization': new_utilization,
                'height_reduction': height_reduction,
                'utilization_improvement': utilization_improvement,
                'message': "画布截断前优化完成"
            }

        except Exception as e:
            log.error(f"画布截断前优化失败: {str(e)}")
            # 恢复原始状态
            if 'original_placed_images' in locals():
                packer.placed_images = original_placed_images
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
            return {'success': False, 'message': f"画布截断前优化失败: {str(e)}"}

    def should_move_bottom_row(self, tetris_packer=None) -> bool:
        """
        评估是否应将底部行图片移至下一画布

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例

        Returns:
            bool: 是否应移动底部行
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            return False

        try:
            # 获取底部行信息
            bottom_row_info = self.bottom_row_optimizer._get_bottom_row_info(packer)
            if not bottom_row_info:
                return False

            # 计算底部行利用率
            bottom_row_utilization = bottom_row_info['width_used'] / packer.container_width

            # 如果底部行利用率较低，考虑移动
            if bottom_row_utilization < 0.3:
                log.info(f"底部行利用率较低 ({bottom_row_utilization:.2f})，建议移至下一画布")
                return True

            # 如果底部行只有一个图片，且宽度小于容器宽度的30%，考虑移动
            if len(bottom_row_info['images']) == 1 and bottom_row_info['images'][0]['width'] < packer.container_width * 0.3:
                log.info("底部行只有一个小图片，建议移至下一画布")
                return True

            # 检查底部行是否包含不完整的B类图片组
            b_class_images = [img for img in bottom_row_info['images'] if img.get('category') == 'B' or img.get('image_class') == 'B']
            if b_class_images:
                # 获取B类图片的宽度
                b_width = b_class_images[0]['width']

                # 计算一行可以放置的B类图片数量
                max_count = packer.container_width // b_width

                # 如果B类图片数量不是完整的一组，考虑移动
                if 0 < len(b_class_images) < max_count and len(b_class_images) < max_count * 0.7:
                    log.info(f"底部行包含不完整的B类图片组 ({len(b_class_images)}/{max_count})，建议移至下一画布")
                    return True

            return False

        except Exception as e:
            log.error(f"评估底部行移动失败: {str(e)}")
            return False

    def optimize_canvas_height(self, tetris_packer=None) -> Dict[str, Any]:
        """
        优化画布高度

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例

        Returns:
            Dict[str, Any]: 优化结果
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            log.error("未提供有效的Tetris算法实例或画布为空")
            return {'success': False, 'message': "未提供有效的Tetris算法实例或画布为空"}

        try:
            # 记录原始高度
            original_max_height = packer.get_max_height()

            # 获取底部行信息
            bottom_row_info = self.bottom_row_optimizer._get_bottom_row_info(packer)
            if not bottom_row_info:
                return {'success': False, 'message': "未找到有效的底部行"}

            # 计算底部行利用率
            bottom_row_utilization = bottom_row_info['width_used'] / packer.container_width

            # 如果底部行利用率较高，不需要优化
            if bottom_row_utilization > 0.7:
                return {'success': False, 'message': "底部行利用率较高，不需要优化"}

            # 计算可能的新高度 - 移除底部行
            new_height = bottom_row_info['y']

            # 如果新高度与原始高度相差不大，不需要优化
            if original_max_height - new_height < 50:  # 最小高度差阈值
                return {'success': False, 'message': "高度差较小，不需要优化"}

            # 返回优化建议
            return {
                'success': True,
                'original_height': original_max_height,
                'suggested_height': new_height,
                'height_reduction': (original_max_height - new_height) / original_max_height * 100,
                'bottom_row_utilization': bottom_row_utilization,
                'message': "建议移除底部行，优化画布高度"
            }

        except Exception as e:
            log.error(f"优化画布高度失败: {str(e)}")
            return {'success': False, 'message': f"优化画布高度失败: {str(e)}"}
