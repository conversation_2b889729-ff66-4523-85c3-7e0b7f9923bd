#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
俄罗斯方块算法优化测试脚本
在等比缩小的画布上测试俄罗斯方块算法，通过各种图片组合来优化水平利用率和代码效率
"""

import os
import sys
import time
import random
import argparse
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.font_manager import FontProperties
from typing import List, Dict, Tuple, Any, Optional

# 配置中文字体支持
try:
    # 尝试使用系统中文字体
    font_paths = [
        'C:/Windows/Fonts/simhei.ttf',  # Windows 黑体
        'C:/Windows/Fonts/msyh.ttc',    # Windows 微软雅黑
        'C:/Windows/Fonts/simsun.ttc',  # Windows 宋体
        '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'  # Linux 文泉驿微米黑
    ]

    font_found = False
    for font_path in font_paths:
        if os.path.exists(font_path):
            plt.rcParams['font.family'] = ['sans-serif']
            if 'msyh' in font_path:
                plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] + plt.rcParams['font.sans-serif']
            elif 'simhei' in font_path:
                plt.rcParams['font.sans-serif'] = ['SimHei'] + plt.rcParams['font.sans-serif']
            elif 'simsun' in font_path:
                plt.rcParams['font.sans-serif'] = ['SimSun'] + plt.rcParams['font.sans-serif']
            elif 'wqy-microhei' in font_path:
                plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei'] + plt.rcParams['font.sans-serif']
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            font_found = True
            break

    if not font_found:
        # 如果没有找到中文字体，使用英文显示
        print("Warning: No Chinese font found. Using English instead.")
except Exception as e:
    print(f"Warning: Failed to set Chinese font: {e}")
    # 出错时使用英文显示

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入俄罗斯方块算法
from core.tetris_packer import TetrisPacker
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("TetrisOptimizer")

class TetrisOptimizer:
    """俄罗斯方块算法优化器"""

    def __init__(self,
                 canvas_width: int = 1600,
                 scale_factor: float = 0.1,
                 image_spacing: int = 5,
                 seed: Optional[int] = None):
        """
        初始化优化器

        Args:
            canvas_width: 原始画布宽度（像素）
            scale_factor: 缩放因子，用于等比缩小画布和图片
            image_spacing: 图片间距（像素）
            seed: 随机种子，用于生成可重复的测试数据
        """
        self.original_width = canvas_width
        self.scale_factor = scale_factor
        self.canvas_width = int(canvas_width * scale_factor)
        self.image_spacing = max(1, int(image_spacing * scale_factor))

        # 设置随机种子
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)

        # 记录测试结果
        self.test_results = []

        log.info(f"初始化优化器:")
        log.info(f"  - 原始画布宽度: {self.original_width}像素")
        log.info(f"  - 缩放因子: {self.scale_factor}")
        log.info(f"  - 缩放后画布宽度: {self.canvas_width}像素")
        log.info(f"  - 图片间距: {self.image_spacing}像素")

    def generate_test_images(self,
                            count: int,
                            min_size: int = 50,
                            max_size: int = 300,
                            distribution: str = 'random',
                            aspect_ratios: List[float] = None) -> List[Dict[str, Any]]:
        """
        生成测试图片

        Args:
            count: 图片数量
            min_size: 最小尺寸（像素）
            max_size: 最大尺寸（像素）
            distribution: 尺寸分布类型，可选值：'random'（随机）, 'uniform'（均匀）, 'normal'（正态）, 'bimodal'（双峰）
            aspect_ratios: 宽高比列表，如果提供，则从中随机选择

        Returns:
            List[Dict[str, Any]]: 图片列表，每个图片包含宽度、高度等信息
        """
        # 缩放尺寸范围
        min_size = max(10, int(min_size * self.scale_factor))
        max_size = max(20, int(max_size * self.scale_factor))

        # 默认宽高比范围
        if aspect_ratios is None:
            aspect_ratios = [0.5, 0.75, 1.0, 1.33, 1.5, 2.0]

        images = []

        for i in range(count):
            # 根据分布类型生成尺寸
            if distribution == 'uniform':
                # 均匀分布
                size = random.randint(min_size, max_size)
            elif distribution == 'normal':
                # 正态分布
                mean = (min_size + max_size) / 2
                std = (max_size - min_size) / 6  # 99.7%的值在6个标准差内
                size = int(np.clip(np.random.normal(mean, std), min_size, max_size))
            elif distribution == 'bimodal':
                # 双峰分布（模拟小图和大图）
                if random.random() < 0.5:
                    size = random.randint(min_size, min_size + (max_size - min_size) // 3)
                else:
                    size = random.randint(max_size - (max_size - min_size) // 3, max_size)
            else:
                # 默认随机分布
                size = random.randint(min_size, max_size)

            # 随机选择宽高比
            aspect_ratio = random.choice(aspect_ratios)

            # 计算宽度和高度
            if random.random() < 0.5:
                # 宽图
                width = size
                height = int(width / aspect_ratio)
            else:
                # 高图
                height = size
                width = int(height * aspect_ratio)

            # 确保尺寸在合理范围内
            width = max(10, min(width, self.canvas_width))
            height = max(10, min(height, self.canvas_width))

            # 创建图片数据
            image = {
                'id': f"image_{i+1}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height
            }

            images.append(image)

        log.info(f"生成了 {len(images)} 个测试图片 (分布类型: {distribution})")
        log.info(f"  - 尺寸范围: {min_size}-{max_size}像素")
        log.info(f"  - 宽高比范围: {min(aspect_ratios)}-{max(aspect_ratios)}")

        return images

    def test_parameters(self,
                       images: List[Dict[str, Any]],
                       horizontal_priority: int = 80,
                       gap_filling_priority: int = 70,
                       rotation_priority: int = 60,
                       max_height: int = 0) -> Dict[str, Any]:
        """
        测试特定参数组合的效果

        Args:
            images: 图片列表
            horizontal_priority: 水平优先级（百分比）
            gap_filling_priority: 空隙填充优先级（百分比）
            rotation_priority: 旋转优先级（百分比）
            max_height: 最大高度限制（0表示无限制）

        Returns:
            Dict[str, Any]: 测试结果
        """
        # 创建俄罗斯方块算法实例
        packer = TetrisPacker(
            container_width=self.canvas_width,
            image_spacing=self.image_spacing,
            max_height=max_height
        )

        # 设置算法参数
        packer.horizontal_priority = horizontal_priority
        packer.gap_filling_priority = gap_filling_priority
        packer.rotation_priority = rotation_priority

        # 记录开始时间
        start_time = time.time()

        # 放置图片
        placed_count = 0
        for image in images:
            # 准备图片数据
            image_data = {
                'id': image['id'],
                'c_horizontal_priority': horizontal_priority,
                'c_gap_filling_priority': gap_filling_priority,
                'c_rotation_priority': rotation_priority
            }

            # 尝试放置图片
            result = packer.place_image(image['width'], image['height'], image_data)

            if result[2]:  # 如果成功放置
                placed_count += 1

        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time

        # 获取布局结果
        max_height = packer.get_max_height()
        placed_images = packer.get_placed_images()
        utilization = packer.get_utilization() * 100  # 转换为百分比

        # 计算水平利用率（每一行的平均利用率）
        row_utilizations = []
        row_heights = {}

        for img in placed_images:
            y = img['y']
            if y not in row_heights:
                row_heights[y] = []
            row_heights[y].append((img['x'], img['x'] + img['width']))

        for y, ranges in row_heights.items():
            # 合并重叠的范围
            ranges.sort()
            merged = []
            for start, end in ranges:
                if not merged or start > merged[-1][1]:
                    merged.append((start, end))
                else:
                    merged[-1] = (merged[-1][0], max(merged[-1][1], end))

            # 计算行利用率
            covered_width = sum(end - start for start, end in merged)
            row_util = covered_width / self.canvas_width
            row_utilizations.append(row_util)

        # 计算平均行利用率
        avg_row_utilization = np.mean(row_utilizations) * 100 if row_utilizations else 0

        # 计算处理速度
        processing_speed = placed_count / processing_time if processing_time > 0 else 0

        # 记录测试结果
        result = {
            'parameters': {
                'horizontal_priority': horizontal_priority,
                'gap_filling_priority': gap_filling_priority,
                'rotation_priority': rotation_priority,
                'max_height': max_height
            },
            'results': {
                'placed_count': placed_count,
                'total_count': len(images),
                'success_rate': placed_count / len(images) * 100,
                'max_height': max_height,
                'canvas_utilization': utilization,
                'avg_row_utilization': avg_row_utilization,
                'processing_time': processing_time,
                'processing_speed': processing_speed
            },
            'placed_images': placed_images
        }

        # 记录日志
        log.info(f"测试参数组合:")
        log.info(f"  - 水平优先级: {horizontal_priority}%")
        log.info(f"  - 空隙填充优先级: {gap_filling_priority}%")
        log.info(f"  - 旋转优先级: {rotation_priority}%")
        log.info(f"测试结果:")
        log.info(f"  - 成功放置: {placed_count}/{len(images)} 个图片 ({result['results']['success_rate']:.2f}%)")
        log.info(f"  - 画布利用率: {utilization:.2f}%")
        log.info(f"  - 平均行利用率: {avg_row_utilization:.2f}%")
        log.info(f"  - 最大高度: {max_height}像素")
        log.info(f"  - 处理时间: {processing_time:.2f}秒")
        log.info(f"  - 处理速度: {processing_speed:.2f}图片/秒")

        # 添加到测试结果列表
        self.test_results.append(result)

        return result

    def grid_search(self,
                   images: List[Dict[str, Any]],
                   horizontal_priorities: List[int] = None,
                   gap_filling_priorities: List[int] = None,
                   rotation_priorities: List[int] = None) -> Dict[str, Any]:
        """
        网格搜索最优参数组合

        Args:
            images: 图片列表
            horizontal_priorities: 水平优先级列表
            gap_filling_priorities: 空隙填充优先级列表
            rotation_priorities: 旋转优先级列表

        Returns:
            Dict[str, Any]: 最优参数组合及其结果
        """
        # 默认参数范围
        if horizontal_priorities is None:
            horizontal_priorities = [60, 70, 80, 90]
        if gap_filling_priorities is None:
            gap_filling_priorities = [50, 60, 70, 80]
        if rotation_priorities is None:
            rotation_priorities = [40, 50, 60, 70]

        log.info(f"开始网格搜索最优参数组合...")
        log.info(f"  - 水平优先级范围: {horizontal_priorities}")
        log.info(f"  - 空隙填充优先级范围: {gap_filling_priorities}")
        log.info(f"  - 旋转优先级范围: {rotation_priorities}")

        best_result = None
        best_score = -float('inf')

        # 清空之前的测试结果
        self.test_results = []

        # 测试所有参数组合
        total_combinations = len(horizontal_priorities) * len(gap_filling_priorities) * len(rotation_priorities)
        current_combination = 0

        for hp in horizontal_priorities:
            for gp in gap_filling_priorities:
                for rp in rotation_priorities:
                    current_combination += 1
                    log.info(f"测试参数组合 {current_combination}/{total_combinations}...")

                    # 测试当前参数组合
                    result = self.test_parameters(
                        images=images,
                        horizontal_priority=hp,
                        gap_filling_priority=gp,
                        rotation_priority=rp
                    )

                    # 计算综合得分
                    # 权重可以根据需要调整
                    canvas_util_weight = 0.5  # 画布利用率权重
                    row_util_weight = 0.3     # 行利用率权重
                    speed_weight = 0.2        # 处理速度权重

                    canvas_util = result['results']['canvas_utilization']
                    row_util = result['results']['avg_row_utilization']
                    speed = min(1000, result['results']['processing_speed']) / 1000 * 100  # 归一化到0-100

                    score = (canvas_util_weight * canvas_util +
                             row_util_weight * row_util +
                             speed_weight * speed)

                    result['score'] = score

                    # 更新最优结果
                    if score > best_score:
                        best_score = score
                        best_result = result

                    log.info(f"  - 综合得分: {score:.2f}")

        log.info(f"网格搜索完成!")
        log.info(f"最优参数组合:")
        log.info(f"  - 水平优先级: {best_result['parameters']['horizontal_priority']}%")
        log.info(f"  - 空隙填充优先级: {best_result['parameters']['gap_filling_priority']}%")
        log.info(f"  - 旋转优先级: {best_result['parameters']['rotation_priority']}%")
        log.info(f"最优结果:")
        log.info(f"  - 画布利用率: {best_result['results']['canvas_utilization']:.2f}%")
        log.info(f"  - 平均行利用率: {best_result['results']['avg_row_utilization']:.2f}%")
        log.info(f"  - 处理速度: {best_result['results']['processing_speed']:.2f}图片/秒")
        log.info(f"  - 综合得分: {best_result['score']:.2f}")

        return best_result

    def visualize_result(self,
                        result: Dict[str, Any],
                        output_path: str = None,
                        show_grid: bool = True,
                        show_stats: bool = True) -> None:
        """
        可视化测试结果

        Args:
            result: 测试结果
            output_path: 输出文件路径，如果为None则显示图像
            show_grid: 是否显示网格
            show_stats: 是否显示统计信息
        """
        placed_images = result['placed_images']

        if not placed_images:
            log.warning("没有放置任何图片，无法可视化")
            return

        # 计算画布高度
        max_height = max(img['y'] + img['height'] for img in placed_images)

        # 创建图像
        fig, ax = plt.subplots(figsize=(12, max_height / 100 + 2))

        # 绘制画布边界
        ax.add_patch(Rectangle((0, 0), self.canvas_width, max_height,
                              fill=False, edgecolor='black', linewidth=2))

        # 绘制网格
        if show_grid:
            grid_size = 50 * self.scale_factor
            for x in range(0, self.canvas_width, int(grid_size)):
                ax.axvline(x, color='lightgray', linestyle='-', linewidth=0.5)
            for y in range(0, max_height, int(grid_size)):
                ax.axhline(y, color='lightgray', linestyle='-', linewidth=0.5)

        # 绘制已放置的图片
        colors = plt.cm.tab20(np.linspace(0, 1, 20))

        for i, img in enumerate(placed_images):
            color = colors[i % len(colors)]

            # 绘制图片矩形
            rect = Rectangle((img['x'], img['y']), img['width'], img['height'],
                           fill=True, edgecolor='black', facecolor=color, linewidth=1, alpha=0.7)
            ax.add_patch(rect)

            # 添加图片ID
            if 'data' in img and 'id' in img['data']:
                img_id = img['data']['id']
                ax.text(img['x'] + img['width'] / 2, img['y'] + img['height'] / 2,
                       img_id, ha='center', va='center', fontsize=8)

        # 添加统计信息
        if show_stats:
            stats_text = [
                f"参数: 水平优先级={result['parameters']['horizontal_priority']}%, "
                f"空隙填充优先级={result['parameters']['gap_filling_priority']}%, "
                f"旋转优先级={result['parameters']['rotation_priority']}%",
                f"画布: {self.canvas_width}x{max_height} 像素",
                f"图片: {result['results']['placed_count']}/{result['results']['total_count']} 个",
                f"画布利用率: {result['results']['canvas_utilization']:.2f}%",
                f"平均行利用率: {result['results']['avg_row_utilization']:.2f}%",
                f"处理时间: {result['results']['processing_time']:.2f}秒",
                f"处理速度: {result['results']['processing_speed']:.2f}图片/秒"
            ]

            if 'score' in result:
                stats_text.append(f"综合得分: {result['score']:.2f}")

            plt.figtext(0.02, 0.02, '\n'.join(stats_text), fontsize=10,
                      bbox=dict(facecolor='white', alpha=0.8, boxstyle='round'))

        # 设置坐标轴
        ax.set_xlim(0, self.canvas_width)
        ax.set_ylim(0, max_height)
        ax.set_aspect('equal')

        # 设置标题
        plt.title('俄罗斯方块算法布局结果')

        # 保存或显示图像
        if output_path:
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"布局可视化已保存到: {output_path}")
        else:
            plt.show()

        plt.close()

    def compare_results(self,
                       top_n: int = 3,
                       output_path: str = None) -> None:
        """
        比较多个测试结果

        Args:
            top_n: 显示前N个最佳结果
            output_path: 输出文件路径，如果为None则显示图像
        """
        if not self.test_results:
            log.warning("没有测试结果可比较")
            return

        # 计算每个结果的综合得分（如果尚未计算）
        for result in self.test_results:
            if 'score' not in result:
                canvas_util_weight = 0.5
                row_util_weight = 0.3
                speed_weight = 0.2

                canvas_util = result['results']['canvas_utilization']
                row_util = result['results']['avg_row_utilization']
                speed = min(1000, result['results']['processing_speed']) / 1000 * 100

                result['score'] = (canvas_util_weight * canvas_util +
                                 row_util_weight * row_util +
                                 speed_weight * speed)

        # 按得分排序
        sorted_results = sorted(self.test_results, key=lambda x: x['score'], reverse=True)

        # 取前N个结果
        top_results = sorted_results[:top_n]

        # 创建比较图
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))

        # 比较画布利用率
        canvas_utils = [r['results']['canvas_utilization'] for r in top_results]
        row_utils = [r['results']['avg_row_utilization'] for r in top_results]
        speeds = [r['results']['processing_speed'] for r in top_results]

        labels = [f"H{r['parameters']['horizontal_priority']}-"
                 f"G{r['parameters']['gap_filling_priority']}-"
                 f"R{r['parameters']['rotation_priority']}" for r in top_results]

        # 画布利用率
        axes[0].bar(labels, canvas_utils, color='skyblue')
        axes[0].set_title('画布利用率 (%)')
        axes[0].set_ylim(0, 100)
        for i, v in enumerate(canvas_utils):
            axes[0].text(i, v + 1, f"{v:.1f}%", ha='center')

        # 行利用率
        axes[1].bar(labels, row_utils, color='lightgreen')
        axes[1].set_title('平均行利用率 (%)')
        axes[1].set_ylim(0, 100)
        for i, v in enumerate(row_utils):
            axes[1].text(i, v + 1, f"{v:.1f}%", ha='center')

        # 处理速度
        axes[2].bar(labels, speeds, color='salmon')
        axes[2].set_title('处理速度 (图片/秒)')
        for i, v in enumerate(speeds):
            axes[2].text(i, v + 1, f"{v:.1f}", ha='center')

        plt.tight_layout()

        # 保存或显示图像
        if output_path:
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"比较结果已保存到: {output_path}")
        else:
            plt.show()

        plt.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='俄罗斯方块算法优化测试')

    # 基本参数
    parser.add_argument('--width', type=int, default=1600, help='原始画布宽度（像素）')
    parser.add_argument('--scale', type=float, default=0.1, help='缩放因子')
    parser.add_argument('--spacing', type=int, default=5, help='图片间距（像素）')
    parser.add_argument('--seed', type=int, default=None, help='随机种子')

    # 图片生成参数
    parser.add_argument('--count', type=int, default=50, help='图片数量')
    parser.add_argument('--min-size', type=int, default=50, help='最小图片尺寸（像素）')
    parser.add_argument('--max-size', type=int, default=300, help='最大图片尺寸（像素）')
    parser.add_argument('--distribution', type=str, default='random',
                      choices=['random', 'uniform', 'normal', 'bimodal'],
                      help='图片尺寸分布类型')

    # 测试模式
    parser.add_argument('--mode', type=str, default='grid_search',
                      choices=['single_test', 'grid_search'],
                      help='测试模式')

    # 单次测试参数
    parser.add_argument('--horizontal', type=int, default=80, help='水平优先级（百分比）')
    parser.add_argument('--gap-filling', type=int, default=70, help='空隙填充优先级（百分比）')
    parser.add_argument('--rotation', type=int, default=60, help='旋转优先级（百分比）')

    # 网格搜索参数
    parser.add_argument('--horizontal-range', type=str, default='70,80,90',
                      help='水平优先级范围，逗号分隔')
    parser.add_argument('--gap-filling-range', type=str, default='60,70,80',
                      help='空隙填充优先级范围，逗号分隔')
    parser.add_argument('--rotation-range', type=str, default='50,60,70',
                      help='旋转优先级范围，逗号分隔')

    # 输出参数
    parser.add_argument('--output', type=str, default=None, help='输出文件路径')
    parser.add_argument('--compare', action='store_true', help='比较多个测试结果')

    args = parser.parse_args()

    # 创建优化器
    optimizer = TetrisOptimizer(
        canvas_width=args.width,
        scale_factor=args.scale,
        image_spacing=args.spacing,
        seed=args.seed
    )

    # 生成测试图片
    images = optimizer.generate_test_images(
        count=args.count,
        min_size=args.min_size,
        max_size=args.max_size,
        distribution=args.distribution
    )

    # 根据测试模式执行测试
    if args.mode == 'single_test':
        # 单次测试
        result = optimizer.test_parameters(
            images=images,
            horizontal_priority=args.horizontal,
            gap_filling_priority=args.gap_filling,
            rotation_priority=args.rotation
        )

        # 可视化结果
        if args.output:
            optimizer.visualize_result(result, args.output)
        else:
            optimizer.visualize_result(result)

    elif args.mode == 'grid_search':
        # 解析参数范围
        horizontal_priorities = [int(x) for x in args.horizontal_range.split(',')]
        gap_filling_priorities = [int(x) for x in args.gap_filling_range.split(',')]
        rotation_priorities = [int(x) for x in args.rotation_range.split(',')]

        # 网格搜索
        best_result = optimizer.grid_search(
            images=images,
            horizontal_priorities=horizontal_priorities,
            gap_filling_priorities=gap_filling_priorities,
            rotation_priorities=rotation_priorities
        )

        # 可视化最佳结果
        if args.output:
            optimizer.visualize_result(best_result, args.output)
        else:
            optimizer.visualize_result(best_result)

        # 比较结果
        if args.compare:
            compare_output = args.output.replace('.png', '_compare.png') if args.output else None
            optimizer.compare_results(top_n=3, output_path=compare_output)


if __name__ == "__main__":
    main()
