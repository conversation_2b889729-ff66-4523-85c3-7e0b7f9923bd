#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack改进验证脚本
用于验证改进后的RectPack算法和相关组件是否正常工作

验证内容：
1. 管理器类的基本功能
2. 模块导入和初始化
3. 基本的算法流程
4. 错误处理机制
"""

import os
import sys
import logging
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)


def verify_manager_imports():
    """验证管理器类的导入"""
    log.info("验证管理器类导入...")
    
    try:
        from managers.rectpack_manager import RectPackManager
        log.info("✓ RectPackManager 导入成功")
    except Exception as e:
        log.error(f"✗ RectPackManager 导入失败: {str(e)}")
        return False
    
    try:
        from managers.photoshop_integration_manager import PhotoshopIntegrationManager
        log.info("✓ PhotoshopIntegrationManager 导入成功")
    except Exception as e:
        log.error(f"✗ PhotoshopIntegrationManager 导入失败: {str(e)}")
        return False
    
    try:
        from managers.task_manager import TaskManager, TaskPriority
        log.info("✓ TaskManager 导入成功")
    except Exception as e:
        log.error(f"✗ TaskManager 导入失败: {str(e)}")
        return False
    
    return True


def verify_manager_initialization():
    """验证管理器类的初始化"""
    log.info("验证管理器类初始化...")
    
    try:
        # 模拟配置管理器
        class MockConfigManager:
            def get_rectpack_settings(self):
                return {
                    'use_rectpack_algorithm': True,
                    'pack_algo': 0,
                    'sort_algo': 0,
                    'rotation_enabled': True,
                    'optimization_level': 2,
                    'max_iterations': 5
                }
            
            def get_test_mode_settings(self):
                return {
                    'is_test_mode': True,
                    'miniature_ratio': 0.1
                }
        
        config_manager = MockConfigManager()
        
        # 测试RectPackManager初始化
        from managers.rectpack_manager import RectPackManager
        rectpack_manager = RectPackManager(config_manager)
        log.info("✓ RectPackManager 初始化成功")
        
        # 测试PhotoshopIntegrationManager初始化
        from managers.photoshop_integration_manager import PhotoshopIntegrationManager
        ps_manager = PhotoshopIntegrationManager(config_manager)
        log.info("✓ PhotoshopIntegrationManager 初始化成功")
        
        # 测试TaskManager初始化
        from managers.task_manager import TaskManager
        task_manager = TaskManager(config_manager)
        log.info("✓ TaskManager 初始化成功")
        
        return True
        
    except Exception as e:
        log.error(f"✗ 管理器初始化失败: {str(e)}")
        return False


def verify_rectpack_algorithm():
    """验证RectPack算法基本功能"""
    log.info("验证RectPack算法基本功能...")
    
    try:
        # 模拟配置管理器
        class MockConfigManager:
            def get_rectpack_settings(self):
                return {
                    'use_rectpack_algorithm': True,
                    'pack_algo': 0,
                    'sort_algo': 0,
                    'rotation_enabled': True,
                    'optimization_level': 2,
                    'max_iterations': 5
                }
        
        config_manager = MockConfigManager()
        
        from managers.rectpack_manager import RectPackManager
        rectpack_manager = RectPackManager(config_manager)
        
        # 测试算法初始化
        success = rectpack_manager.initialize_algorithm(
            canvas_width_m=2.0,
            max_height_cm=5000,
            image_spacing_cm=0.1,
            ppi=72
        )
        
        if success:
            log.info("✓ RectPack算法初始化成功")
        else:
            log.error("✗ RectPack算法初始化失败")
            return False
        
        # 测试参数优化
        optimized_params = rectpack_manager.optimize_algorithm_parameters()
        if optimized_params:
            log.info("✓ 算法参数优化成功")
        else:
            log.warning("⚠ 算法参数优化返回空结果")
        
        # 测试性能统计
        stats = rectpack_manager.get_performance_stats()
        log.info(f"✓ 性能统计获取成功: {stats}")
        
        return True
        
    except Exception as e:
        log.error(f"✗ RectPack算法验证失败: {str(e)}")
        return False


def verify_photoshop_integration():
    """验证Photoshop集成基本功能"""
    log.info("验证Photoshop集成基本功能...")
    
    try:
        # 模拟配置管理器
        class MockConfigManager:
            def get_test_mode_settings(self):
                return {
                    'is_test_mode': True,
                    'miniature_ratio': 0.1
                }
        
        config_manager = MockConfigManager()
        
        from managers.photoshop_integration_manager import PhotoshopIntegrationManager
        ps_manager = PhotoshopIntegrationManager(config_manager)
        
        # 测试连接初始化（测试模式）
        success = ps_manager.initialize_connection(test_mode=True)
        
        if success:
            log.info("✓ Photoshop连接初始化成功（测试模式）")
        else:
            log.error("✗ Photoshop连接初始化失败")
            return False
        
        # 测试健康检查
        health_status = ps_manager.perform_health_check()
        log.info(f"✓ 健康检查完成: {health_status}")
        
        # 测试性能统计
        stats = ps_manager.get_performance_stats()
        log.info(f"✓ 性能统计获取成功: {stats}")
        
        return True
        
    except Exception as e:
        log.error(f"✗ Photoshop集成验证失败: {str(e)}")
        return False


def verify_task_manager():
    """验证任务管理器基本功能"""
    log.info("验证任务管理器基本功能...")
    
    try:
        # 模拟配置管理器
        class MockConfigManager:
            def get_rectpack_settings(self):
                return {'use_rectpack_algorithm': True}
            
            def get_test_mode_settings(self):
                return {'is_test_mode': True}
        
        config_manager = MockConfigManager()
        
        from managers.task_manager import TaskManager, TaskPriority
        task_manager = TaskManager(config_manager)
        
        # 创建测试图片数据
        test_images = [
            {
                'name': 'test_image_1',
                'width': 200,
                'height': 150,
                'path': 'test1.jpg'
            },
            {
                'name': 'test_image_2', 
                'width': 300,
                'height': 200,
                'path': 'test2.jpg'
            }
        ]
        
        # 测试任务添加
        canvas_config = {
            'canvas_width_m': 2.0,
            'max_height_cm': 5000,
            'image_spacing_cm': 0.1,
            'ppi': 72,
            'output_dir': 'test_output'
        }
        
        task_id = task_manager.add_task(
            material_name="test_material",
            sheet_name="test_sheet",
            pattern_items=test_images,
            canvas_config=canvas_config,
            priority=TaskPriority.NORMAL
        )
        
        if task_id:
            log.info(f"✓ 任务添加成功: {task_id}")
        else:
            log.error("✗ 任务添加失败")
            return False
        
        # 测试任务状态查询
        status = task_manager.get_task_status(task_id)
        if status:
            log.info(f"✓ 任务状态查询成功: {status['status']}")
        else:
            log.error("✗ 任务状态查询失败")
            return False
        
        # 测试性能摘要
        summary = task_manager.get_performance_summary()
        log.info(f"✓ 性能摘要获取成功: {summary}")
        
        return True
        
    except Exception as e:
        log.error(f"✗ 任务管理器验证失败: {str(e)}")
        return False


def verify_error_handling():
    """验证错误处理机制"""
    log.info("验证错误处理机制...")
    
    try:
        from managers.rectpack_manager import RectPackManager
        
        # 模拟配置管理器
        class MockConfigManager:
            def get_rectpack_settings(self):
                return {}
        
        config_manager = MockConfigManager()
        rectpack_manager = RectPackManager(config_manager)
        
        # 测试无效参数处理
        success = rectpack_manager.initialize_algorithm(
            canvas_width_m=-1,  # 无效值
            max_height_cm=5000,
            image_spacing_cm=0.1,
            ppi=72
        )
        
        if not success:
            log.info("✓ 无效参数错误处理正常")
        else:
            log.warning("⚠ 无效参数未被正确处理")
        
        # 测试空图片列表处理
        arranged_images, process_success = rectpack_manager.process_images([])
        
        if not process_success:
            log.info("✓ 空图片列表错误处理正常")
        else:
            log.warning("⚠ 空图片列表未被正确处理")
        
        return True
        
    except Exception as e:
        log.error(f"✗ 错误处理验证失败: {str(e)}")
        return False


def main():
    """主验证函数"""
    log.info("开始验证RectPack改进...")
    
    verification_results = []
    
    # 执行各项验证
    tests = [
        ("管理器导入", verify_manager_imports),
        ("管理器初始化", verify_manager_initialization),
        ("RectPack算法", verify_rectpack_algorithm),
        ("Photoshop集成", verify_photoshop_integration),
        ("任务管理器", verify_task_manager),
        ("错误处理", verify_error_handling)
    ]
    
    for test_name, test_func in tests:
        log.info(f"\n{'='*50}")
        log.info(f"验证: {test_name}")
        log.info(f"{'='*50}")
        
        try:
            result = test_func()
            verification_results.append((test_name, result))
            
            if result:
                log.info(f"✓ {test_name} 验证通过")
            else:
                log.error(f"✗ {test_name} 验证失败")
                
        except Exception as e:
            log.error(f"✗ {test_name} 验证异常: {str(e)}")
            verification_results.append((test_name, False))
    
    # 输出验证结果摘要
    log.info(f"\n{'='*60}")
    log.info("验证结果摘要")
    log.info(f"{'='*60}")
    
    passed_count = 0
    total_count = len(verification_results)
    
    for test_name, result in verification_results:
        status = "✓ 通过" if result else "✗ 失败"
        log.info(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    log.info(f"\n总计: {passed_count}/{total_count} 项验证通过")
    
    if passed_count == total_count:
        log.info("🎉 所有验证项目都通过了！RectPack改进验证成功。")
        return True
    else:
        log.warning(f"⚠ 有 {total_count - passed_count} 项验证失败，请检查相关问题。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
