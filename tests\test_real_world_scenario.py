#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
真实场景测试脚本
测试俄罗斯方块算法在真实场景下的效果
"""

import os
import sys
import time
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.patches import Rectangle
from typing import List, Dict, Tuple, Any, Optional

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 检查字体是否可用
try:
    # 尝试使用系统中文字体
    font_paths = [
        'C:/Windows/Fonts/simhei.ttf',  # Windows 黑体
        'C:/Windows/Fonts/msyh.ttc',    # Windows 微软雅黑
        'C:/Windows/Fonts/simsun.ttc',  # Windows 宋体
        '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'  # Linux 文泉驿微米黑
    ]

    font_found = False
    for font_path in font_paths:
        if os.path.exists(font_path):
            font_found = True
            print(f"找到中文字体: {font_path}")
            break

    if not font_found:
        print("警告: 未找到中文字体文件，但仍将尝试使用系统字体")
except Exception as e:
    print(f"警告: 设置中文字体时出错: {e}")
    print("将尝试使用系统默认字体")

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入俄罗斯方块算法
from core.tetris_packer import TetrisPacker
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("RealWorldTest")

class RealWorldTester:
    """真实场景测试器"""

    def __init__(self,
                 canvas_width: int = 1600,
                 image_spacing: int = 5,
                 max_height: int = 0,
                 seed: Optional[int] = None):
        """
        初始化测试器

        Args:
            canvas_width: 画布宽度（像素）
            image_spacing: 图片间距（像素）
            max_height: 最大高度限制（0表示无限制）
            seed: 随机种子，用于生成可重复的测试数据
        """
        self.canvas_width = canvas_width
        self.image_spacing = image_spacing
        self.max_height = max_height

        # 创建俄罗斯方块算法实例
        self.packer = TetrisPacker(
            container_width=canvas_width,
            image_spacing=image_spacing,
            max_height=max_height
        )

        # 记录测试结果
        self.test_results = {}

        log.info(f"初始化测试器:")
        log.info(f"  - 画布宽度: {self.canvas_width}像素")
        log.info(f"  - 图片间距: {self.image_spacing}像素")
        log.info(f"  - 最大高度限制: {self.max_height if self.max_height > 0 else '无限制'}像素")

    def load_images(self, input_path: str) -> List[Dict[str, Any]]:
        """
        从JSON文件加载图片数据

        Args:
            input_path: 输入文件路径

        Returns:
            List[Dict[str, Any]]: 图片列表
        """
        with open(input_path, 'r', encoding='utf-8') as f:
            images = json.load(f)

        log.info(f"从 {input_path} 加载了 {len(images)} 个图片")

        return images

    def test_algorithm(self,
                      images: List[Dict[str, Any]],
                      horizontal_priority: int = 80,
                      gap_filling_priority: int = 80,
                      rotation_priority: int = 60) -> Dict[str, Any]:
        """
        测试算法效果

        Args:
            images: 图片列表
            horizontal_priority: 水平优先级（百分比）
            gap_filling_priority: 空隙填充优先级（百分比）
            rotation_priority: 旋转优先级（百分比）

        Returns:
            Dict[str, Any]: 测试结果
        """
        # 重置俄罗斯方块算法实例
        self.packer = TetrisPacker(
            container_width=self.canvas_width,
            image_spacing=self.image_spacing,
            max_height=self.max_height
        )

        # 设置算法参数
        self.packer.horizontal_priority = horizontal_priority
        self.packer.gap_filling_priority = gap_filling_priority
        self.packer.rotation_priority = rotation_priority

        # 记录开始时间
        start_time = time.time()

        # 放置图片
        placed_count = 0
        for image in images:
            # 准备图片数据
            image_data = {
                'id': image['id'],
                'c_horizontal_priority': horizontal_priority,
                'c_gap_filling_priority': gap_filling_priority,
                'c_rotation_priority': rotation_priority
            }

            # 尝试放置图片
            result = self.packer.place_image(image['width'], image['height'], image_data)

            if result[2]:  # 如果成功放置
                placed_count += 1

        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time

        # 获取布局结果
        max_height = self.packer.get_max_height()
        placed_images = self.packer.get_placed_images()
        utilization = self.packer.get_utilization() * 100  # 转换为百分比

        # 计算水平利用率（每一行的平均利用率）
        row_utilizations = []
        row_heights = {}

        for img in placed_images:
            y = img['y']
            if y not in row_heights:
                row_heights[y] = []
            row_heights[y].append((img['x'], img['x'] + img['width']))

        for y, ranges in row_heights.items():
            # 合并重叠的范围
            ranges.sort()
            merged = []
            for start, end in ranges:
                if not merged or start > merged[-1][1]:
                    merged.append((start, end))
                else:
                    merged[-1] = (merged[-1][0], max(merged[-1][1], end))

            # 计算行利用率
            covered_width = sum(end - start for start, end in merged)
            row_util = covered_width / self.canvas_width
            row_utilizations.append(row_util)

        # 计算平均行利用率
        avg_row_utilization = np.mean(row_utilizations) * 100 if row_utilizations else 0

        # 计算处理速度
        processing_speed = placed_count / processing_time if processing_time > 0 else 0

        # 记录测试结果
        result = {
            'parameters': {
                'horizontal_priority': horizontal_priority,
                'gap_filling_priority': gap_filling_priority,
                'rotation_priority': rotation_priority,
                'max_height': max_height
            },
            'results': {
                'placed_count': placed_count,
                'total_count': len(images),
                'success_rate': placed_count / len(images) * 100,
                'max_height': max_height,
                'canvas_utilization': utilization,
                'avg_row_utilization': avg_row_utilization,
                'processing_time': processing_time,
                'processing_speed': processing_speed
            },
            'placed_images': placed_images
        }

        # 记录日志
        log.info(f"测试参数组合:")
        log.info(f"  - 水平优先级: {horizontal_priority}%")
        log.info(f"  - 空隙填充优先级: {gap_filling_priority}%")
        log.info(f"  - 旋转优先级: {rotation_priority}%")
        log.info(f"测试结果:")
        log.info(f"  - 成功放置: {placed_count}/{len(images)} 个图片 ({result['results']['success_rate']:.2f}%)")
        log.info(f"  - 画布利用率: {utilization:.2f}%")
        log.info(f"  - 平均行利用率: {avg_row_utilization:.2f}%")
        log.info(f"  - 最大高度: {max_height}像素")
        log.info(f"  - 处理时间: {processing_time:.2f}秒")
        log.info(f"  - 处理速度: {processing_speed:.2f}图片/秒")

        # 添加到测试结果字典
        test_key = f"h{horizontal_priority}_g{gap_filling_priority}_r{rotation_priority}"
        self.test_results[test_key] = result

        return result

    def visualize_result(self,
                        result: Dict[str, Any],
                        output_path: str = None,
                        show_grid: bool = True,
                        show_stats: bool = True,
                        show_process: bool = False) -> None:
        """
        可视化测试结果

        Args:
            result: 测试结果
            output_path: 输出文件路径，如果为None则显示图像
            show_grid: 是否显示网格
            show_stats: 是否显示统计信息
            show_process: 是否显示放置过程（动画）
        """
        placed_images = result['placed_images']

        if not placed_images:
            log.warning("没有放置任何图片，无法可视化")
            return

        # 计算画布高度
        max_height = max(img['y'] + img['height'] for img in placed_images)

        # 创建图像
        fig, ax = plt.subplots(figsize=(12, max_height / 100 + 2))

        # 绘制画布边界
        ax.add_patch(Rectangle((0, 0), self.canvas_width, max_height,
                              fill=False, edgecolor='black', linewidth=2))

        # 绘制网格
        if show_grid:
            grid_size = 50
            for x in range(0, self.canvas_width, grid_size):
                ax.axvline(x, color='lightgray', linestyle='-', linewidth=0.5)
            for y in range(0, max_height, grid_size):
                ax.axhline(y, color='lightgray', linestyle='-', linewidth=0.5)

        # 绘制已放置的图片
        colors = plt.cm.tab20(np.linspace(0, 1, 20))

        if show_process:
            # 动态显示放置过程
            for i, img in enumerate(placed_images):
                color = colors[i % len(colors)]

                # 绘制图片矩形
                rect = Rectangle((img['x'], img['y']), img['width'], img['height'],
                               fill=True, edgecolor='black', facecolor=color, linewidth=1, alpha=0.7)
                ax.add_patch(rect)

                # 添加图片ID
                if 'data' in img and 'id' in img['data']:
                    img_id = img['data']['id']
                    # 如果data中有name字段，优先使用name
                    if 'name' in img['data']:
                        img_id = img['data']['name']
                    # 使用支持中文的字体
                    ax.text(img['x'] + img['width'] / 2, img['y'] + img['height'] / 2,
                           img_id, ha='center', va='center', fontsize=8, family='SimHei')

                # 更新图像
                plt.draw()
                plt.pause(0.1)
        else:
            # 一次性绘制所有图片
            for i, img in enumerate(placed_images):
                color = colors[i % len(colors)]

                # 绘制图片矩形
                rect = Rectangle((img['x'], img['y']), img['width'], img['height'],
                               fill=True, edgecolor='black', facecolor=color, linewidth=1, alpha=0.7)
                ax.add_patch(rect)

                # 添加图片ID
                if 'data' in img and 'id' in img['data']:
                    img_id = img['data']['id']
                    # 如果data中有name字段，优先使用name
                    if 'name' in img['data']:
                        img_id = img['data']['name']
                    # 使用支持中文的字体
                    ax.text(img['x'] + img['width'] / 2, img['y'] + img['height'] / 2,
                           img_id, ha='center', va='center', fontsize=8, family='SimHei')

        # 添加统计信息
        if show_stats:
            stats_text = [
                f"参数: 水平优先级={result['parameters']['horizontal_priority']}%, "
                f"空隙填充优先级={result['parameters']['gap_filling_priority']}%, "
                f"旋转优先级={result['parameters']['rotation_priority']}%",
                f"画布: {self.canvas_width}x{max_height} 像素",
                f"图片: {result['results']['placed_count']}/{result['results']['total_count']} 个",
                f"画布利用率: {result['results']['canvas_utilization']:.2f}%",
                f"平均行利用率: {result['results']['avg_row_utilization']:.2f}%",
                f"处理时间: {result['results']['processing_time']:.2f}秒",
                f"处理速度: {result['results']['processing_speed']:.2f}图片/秒"
            ]

            plt.figtext(0.02, 0.02, '\n'.join(stats_text), fontsize=10,
                      bbox=dict(facecolor='white', alpha=0.8, boxstyle='round'),
                      family='SimHei')

        # 设置坐标轴
        ax.set_xlim(0, self.canvas_width)
        ax.set_ylim(0, max_height)
        ax.set_aspect('equal')

        # 设置标题（使用中文字体）
        plt.title('俄罗斯方块算法布局结果', fontproperties='SimHei')

        # 保存或显示图像
        if output_path:
            # 确保目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            # 保存图像，使用高质量设置
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"布局可视化已保存到: {output_path}")
        else:
            plt.show()

        plt.close()

    def compare_results(self, output_path: str = None) -> None:
        """
        比较多个测试结果

        Args:
            output_path: 输出文件路径，如果为None则显示图像
        """
        if not self.test_results:
            log.warning("没有测试结果可比较")
            return

        # 创建比较图
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))

        # 准备数据
        labels = []
        canvas_utils = []
        row_utils = []
        speeds = []

        for key, result in self.test_results.items():
            labels.append(key)
            canvas_utils.append(result['results']['canvas_utilization'])
            row_utils.append(result['results']['avg_row_utilization'])
            speeds.append(result['results']['processing_speed'])

        # 画布利用率
        axes[0].bar(labels, canvas_utils, color='skyblue')
        axes[0].set_title('画布利用率 (%)', fontproperties='SimHei')
        axes[0].set_ylim(0, 100)
        for i, v in enumerate(canvas_utils):
            axes[0].text(i, v + 1, f"{v:.1f}%", ha='center', fontproperties='SimHei')

        # 行利用率
        axes[1].bar(labels, row_utils, color='lightgreen')
        axes[1].set_title('平均行利用率 (%)', fontproperties='SimHei')
        axes[1].set_ylim(0, 100)
        for i, v in enumerate(row_utils):
            axes[1].text(i, v + 1, f"{v:.1f}%", ha='center', fontproperties='SimHei')

        # 处理速度
        axes[2].bar(labels, speeds, color='salmon')
        axes[2].set_title('处理速度 (图片/秒)', fontproperties='SimHei')
        for i, v in enumerate(speeds):
            axes[2].text(i, v + 1, f"{v:.1f}", ha='center', fontproperties='SimHei')

        plt.tight_layout()

        # 保存或显示图像
        if output_path:
            # 确保目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            # 保存图像，使用高质量设置
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"比较结果已保存到: {output_path}")
        else:
            plt.show()

        plt.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='真实场景测试')

    # 基本参数
    parser.add_argument('--width', type=int, default=1600, help='画布宽度（像素）')
    parser.add_argument('--spacing', type=int, default=5, help='图片间距（像素）')
    parser.add_argument('--max-height', type=int, default=0, help='最大高度限制（0表示无限制）')

    # 输入输出参数
    parser.add_argument('--input', type=str, required=True, help='输入图片数据文件路径')
    parser.add_argument('--output', type=str, default=None, help='输出文件路径')

    # 算法参数
    parser.add_argument('--horizontal', type=int, default=80, help='水平优先级（百分比）')
    parser.add_argument('--gap-filling', type=int, default=80, help='空隙填充优先级（百分比）')
    parser.add_argument('--rotation', type=int, default=60, help='旋转优先级（百分比）')

    # 可视化参数
    parser.add_argument('--show-grid', action='store_true', help='显示网格')
    parser.add_argument('--show-process', action='store_true', help='显示放置过程（动画）')

    # 比较模式
    parser.add_argument('--compare', action='store_true', help='比较多个参数组合')
    parser.add_argument('--horizontal-range', type=str, default='70,80,90', help='水平优先级范围，逗号分隔')
    parser.add_argument('--gap-filling-range', type=str, default='70,80,90', help='空隙填充优先级范围，逗号分隔')
    parser.add_argument('--rotation-range', type=str, default='50,60,70', help='旋转优先级范围，逗号分隔')

    args = parser.parse_args()

    # 创建测试器
    tester = RealWorldTester(
        canvas_width=args.width,
        image_spacing=args.spacing,
        max_height=args.max_height
    )

    # 加载图片数据
    images = tester.load_images(args.input)

    if args.compare:
        # 解析参数范围
        horizontal_priorities = [int(x) for x in args.horizontal_range.split(',')]
        gap_filling_priorities = [int(x) for x in args.gap_filling_range.split(',')]
        rotation_priorities = [int(x) for x in args.rotation_range.split(',')]

        # 测试多个参数组合
        for hp in horizontal_priorities:
            for gp in gap_filling_priorities:
                for rp in rotation_priorities:
                    # 只测试部分组合，减少测试时间
                    if hp == 80 and gp == 80 and rp == 60:
                        # 测试当前参数组合
                        result = tester.test_algorithm(
                            images=images,
                            horizontal_priority=hp,
                            gap_filling_priority=gp,
                            rotation_priority=rp
                        )

                        # 可视化结果
                        if args.output:
                            output_path = args.output.replace('.png', f'_h{hp}_g{gp}_r{rp}.png')
                            tester.visualize_result(
                                result=result,
                                output_path=output_path,
                                show_grid=args.show_grid,
                                show_process=args.show_process
                            )

        # 比较结果
        if args.output:
            compare_output = args.output.replace('.png', '_compare.png')
            tester.compare_results(output_path=compare_output)
        else:
            tester.compare_results()
    else:
        # 测试单个参数组合
        result = tester.test_algorithm(
            images=images,
            horizontal_priority=args.horizontal,
            gap_filling_priority=args.gap_filling,
            rotation_priority=args.rotation
        )

        # 可视化结果
        if args.output:
            tester.visualize_result(
                result=result,
                output_path=args.output,
                show_grid=args.show_grid,
                show_process=args.show_process
            )
        else:
            tester.visualize_result(
                result=result,
                show_grid=args.show_grid,
                show_process=args.show_process
            )


if __name__ == "__main__":
    main()
