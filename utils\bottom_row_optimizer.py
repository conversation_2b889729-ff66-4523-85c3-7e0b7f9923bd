#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
画布底部行优化器模块

提供画布底部行优化功能：
1. 优化底部行填充，尽可能填充更多图片
2. 评估底部行图片是否应移至下一画布
3. 提高整体画布利用率
"""

import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("BottomRowOptimizer")

class BottomRowOptimizer:
    """
    画布底部行优化器，提供底部行优化功能

    特性：
    1. 优化底部行填充，尽可能填充更多图片
    2. 评估底部行图片是否应移至下一画布
    3. 提高整体画布利用率
    """

    def __init__(self, tetris_packer=None):
        """
        初始化画布底部行优化器

        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        log.info("画布底部行优化器初始化完成")

    def optimize_bottom_row(self, tetris_packer=None, remaining_patterns: List[Dict[str, Any]] = None) -> bool:
        """
        优化画布底部行，尽可能填充更多图片

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            bool: 是否成功优化
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            log.error("未提供有效的Tetris算法实例或画布为空")
            return False

        try:
            # 获取画布底部行信息
            bottom_row_info = self._get_bottom_row_info(packer)
            if not bottom_row_info:
                log.info("未找到有效的底部行")
                return False

            # 计算底部行利用率
            bottom_row_utilization = bottom_row_info['width_used'] / packer.container_width
            log.info(f"底部行利用率: {bottom_row_utilization:.4f}, 行高: {bottom_row_info['height']}, 已用宽度: {bottom_row_info['width_used']}, 剩余宽度: {bottom_row_info['width_remaining']}")

            # 如果底部行利用率已经很高，不需要优化
            if bottom_row_utilization > 0.9:
                log.info("底部行利用率已经很高，不需要优化")
                return False

            # 如果没有提供剩余图片，无法进行优化
            if not remaining_patterns:
                log.info("未提供剩余图片，无法进行优化")
                return False

            # 尝试在底部行填充更多图片
            success = self._fill_bottom_row(packer, bottom_row_info, remaining_patterns)

            # 如果无法在底部行填充更多图片，评估是否应将底部行图片移至下一画布
            if not success and bottom_row_utilization < 0.5:
                log.info("底部行利用率较低，评估是否应将底部行图片移至下一画布")
                success = self._evaluate_move_to_next_canvas(packer, bottom_row_info, remaining_patterns, recursion_depth=0)

            return success

        except Exception as e:
            log.error(f"优化底部行失败: {str(e)}")
            return False

    def _get_bottom_row_info(self, packer) -> Dict[str, Any]:
        """
        获取画布底部行信息
        增强版：更精确地识别底部行，处理特殊情况

        Args:
            packer: Tetris算法实例

        Returns:
            Dict[str, Any]: 底部行信息
        """
        if not packer.placed_images:
            log.info("画布为空，无法获取底部行信息")
            return None

        # 获取当前最大高度
        max_height = packer.get_max_height()
        if max_height <= 0:
            log.info("画布高度为0，无法获取底部行信息")
            return None

        # 设置底部行识别参数
        bottom_row_tolerance = 10  # 允许的误差范围（像素）
        bottom_row_range = max_height * 0.1  # 底部行范围（最大高度的10%）
        bottom_row_min_range = 20  # 最小底部行范围（像素）

        # 取较大值作为底部行范围
        bottom_row_range = max(bottom_row_range, bottom_row_min_range)

        # 找出底部行的图片
        bottom_row_images = []
        bottom_row_y_values = []

        for img in packer.placed_images:
            img_bottom = img['y'] + img['height']

            # 如果图片底部在最大高度的误差范围内，认为是底部图片
            if max_height - img_bottom <= bottom_row_tolerance:
                bottom_row_images.append(img)
                bottom_row_y_values.append(img['y'])

        if not bottom_row_images:
            log.info("未找到底部行图片")
            return None

        # 如果底部行图片数量过多，可能是因为误差范围太大
        # 尝试缩小误差范围，只保留最底部的图片
        if len(bottom_row_images) > 5:  # 如果底部行图片超过5个
            # 计算图片底部的平均高度
            avg_bottom = sum(img['y'] + img['height'] for img in bottom_row_images) / len(bottom_row_images)

            # 只保留底部高度接近平均值的图片
            filtered_images = []
            for img in bottom_row_images:
                img_bottom = img['y'] + img['height']
                if abs(img_bottom - avg_bottom) <= bottom_row_tolerance / 2:  # 使用更小的误差范围
                    filtered_images.append(img)

            # 如果过滤后还有图片，使用过滤后的结果
            if filtered_images:
                bottom_row_images = filtered_images
                bottom_row_y_values = [img['y'] for img in filtered_images]

        # 确定底部行的y坐标（使用最高的y值，即最底部的起始位置）
        bottom_row_y = max(bottom_row_y_values) if bottom_row_y_values else None

        if bottom_row_y is None:
            log.info("无法确定底部行的y坐标")
            return None

        # 计算底部行的宽度和高度
        width_used = 0
        max_row_height = 0
        x_ranges = []  # 存储每个图片的x范围

        for img in bottom_row_images:
            # 只考虑y坐标接近底部行y坐标的图片
            if abs(img['y'] - bottom_row_y) <= bottom_row_range:
                width = img['width']
                height = img['height']
                x_start = img['x']
                x_end = x_start + width

                # 添加x范围
                x_ranges.append((x_start, x_end))

                # 更新最大高度
                max_row_height = max(max_row_height, height)

        # 合并重叠的x范围
        if x_ranges:
            x_ranges.sort()  # 按起始位置排序
            merged_ranges = []
            current_start, current_end = x_ranges[0]

            for start, end in x_ranges[1:]:
                if start <= current_end:  # 有重叠
                    current_end = max(current_end, end)  # 扩展当前范围
                else:  # 无重叠，添加当前范围并开始新范围
                    merged_ranges.append((current_start, current_end))
                    current_start, current_end = start, end

            # 添加最后一个范围
            merged_ranges.append((current_start, current_end))

            # 计算总宽度（所有不重叠范围的宽度之和）
            width_used = sum(end - start for start, end in merged_ranges)

        # 获取图片间距
        image_spacing = 0
        if hasattr(packer, 'image_spacing'):
            image_spacing = packer.image_spacing

        # 考虑图片间距对宽度的影响
        if len(merged_ranges) > 1 and image_spacing > 0:
            # 减去可能重复计算的间距
            width_used -= image_spacing * (len(merged_ranges) - 1)

        # 计算底部行的剩余宽度
        width_remaining = max(0, packer.container_width - width_used)

        # 创建底部行信息
        bottom_row_info = {
            'y': bottom_row_y,
            'height': max_row_height,
            'width_used': width_used,
            'width_remaining': width_remaining,
            'images': bottom_row_images,
            'x_ranges': merged_ranges  # 添加x范围信息，便于后续处理
        }

        log.info(f"底部行信息: y={bottom_row_y}, 高度={max_row_height}, 已用宽度={width_used}, 剩余宽度={width_remaining}, 图片数量={len(bottom_row_images)}")

        return bottom_row_info

    def _fill_bottom_row(self, packer, bottom_row_info: Dict[str, Any], remaining_patterns: List[Dict[str, Any]]) -> bool:
        """
        尝试在底部行填充更多图片

        Args:
            packer: Tetris算法实例
            bottom_row_info: 底部行信息
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            bool: 是否成功填充
        """
        # 如果剩余宽度太小，无法填充更多图片
        if bottom_row_info['width_remaining'] < 20:  # 最小宽度阈值
            log.info("底部行剩余宽度太小，无法填充更多图片")
            return False

        # 找出可以放入底部行的图片
        suitable_patterns = []

        for pattern in remaining_patterns:
            # 获取图片尺寸
            width = pattern['width_px'] if 'width_px' in pattern else pattern.get('width', 0)
            height = pattern['height_px'] if 'height_px' in pattern else pattern.get('height', 0)

            # 检查是否可以放入底部行
            if width <= bottom_row_info['width_remaining'] and height <= bottom_row_info['height']:
                suitable_patterns.append({
                    'pattern': pattern,
                    'width': width,
                    'height': height,
                    'area': width * height,
                    'fit_score': width / bottom_row_info['width_remaining']  # 宽度比作为适应度分数
                })

            # 检查旋转后是否可以放入底部行
            elif height <= bottom_row_info['width_remaining'] and width <= bottom_row_info['height']:
                suitable_patterns.append({
                    'pattern': pattern,
                    'width': height,  # 旋转后的宽度
                    'height': width,  # 旋转后的高度
                    'area': width * height,
                    'fit_score': height / bottom_row_info['width_remaining'],  # 宽度比作为适应度分数
                    'need_rotation': True
                })

        # 如果没有合适的图片，无法填充
        if not suitable_patterns:
            log.info("未找到合适的图片填充底部行")
            return False

        # 按适应度分数降序排序
        suitable_patterns.sort(key=lambda p: -p['fit_score'])

        # 尝试放置图片
        placed_count = 0
        current_x = packer.container_width - bottom_row_info['width_remaining']

        for pattern_info in suitable_patterns:
            pattern = pattern_info['pattern']
            width = pattern_info['width']
            height = pattern_info['height']
            need_rotation = pattern_info.get('need_rotation', False)

            # 检查是否还有足够空间
            if width > bottom_row_info['width_remaining']:
                continue

            # 放置图片
            packer._update_after_placement(
                current_x, bottom_row_info['y'],
                width, height,
                width, height,
                pattern, need_rotation
            )

            # 更新剩余宽度和当前x坐标
            bottom_row_info['width_remaining'] -= width
            current_x += width
            placed_count += 1

            # 从剩余图片列表中移除已放置的图片
            remaining_patterns.remove(pattern)

            # 如果剩余宽度太小，结束填充
            if bottom_row_info['width_remaining'] < 20:
                break

        log.info(f"成功在底部行填充 {placed_count} 个图片")
        return placed_count > 0

    def _evaluate_move_to_next_canvas(self, packer, bottom_row_info: Dict[str, Any], remaining_patterns: List[Dict[str, Any]], recursion_depth: int = 0) -> bool:
        """
        评估是否应将底部行图片移至下一画布
        增强版：更全面地评估底部行图片，处理更多特殊情况

        Args:
            packer: Tetris算法实例
            bottom_row_info: 底部行信息
            remaining_patterns: 剩余未放置的图片列表
            recursion_depth: 递归深度计数器，用于防止无限递归

        Returns:
            bool: 是否成功移动
        """
        # 防止递归死循环 - 限制最大递归深度为1（完全禁止递归）
        if recursion_depth >= 1:
            log.warning(f"递归深度达到上限 ({recursion_depth})，停止移动底部图片以防止死循环")
            return False

        # 如果底部行利用率较高，不需要移动
        bottom_row_utilization = bottom_row_info['width_used'] / packer.container_width
        if bottom_row_utilization > 0.7:  # 提高阈值，只有利用率非常高时才保留
            log.info(f"底部行利用率非常高 ({bottom_row_utilization:.4f})，不需要移动")
            return False

        # 计算底部行图片占用的总面积
        bottom_row_area = sum(img['width'] * img['height'] for img in bottom_row_info['images'])

        # 计算底部行图片占整个画布的面积比例
        total_area = sum(img['width'] * img['height'] for img in packer.placed_images)
        area_ratio = bottom_row_area / total_area if total_area > 0 else 0

        # 如果底部行图片占比很小，考虑移动
        if area_ratio < 0.05:  # 底部行图片占总面积不到5%
            log.info(f"底部行图片占总面积比例很小 ({area_ratio:.4f})，考虑移动")

            # 从已放置图片列表中移除底部行图片
            removed_images = []
            for img in bottom_row_info['images']:
                if img in packer.placed_images:
                    packer.placed_images.remove(img)
                    pattern = img.get('pattern', {})
                    if pattern:
                        removed_images.append(pattern)

            # 更新当前最大高度
            packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0

            # 将底部行图片添加到剩余图片列表的开头，优先放置
            for pattern in removed_images:
                remaining_patterns.insert(0, pattern)

            log.info(f"已将底部行 {len(removed_images)} 个图片移至下一画布")
            return True

        # 如果底部行只有一个图片，进行智能判断
        if len(bottom_row_info['images']) == 1:
            # 获取底部图片信息
            bottom_image = bottom_row_info['images'][0]
            bottom_image_width = bottom_image['width']
            bottom_image_class = self._get_image_class(bottom_image)

            # 计算移除后的高度减少
            current_height = packer.get_max_height()
            new_height = bottom_row_info['y']
            height_reduction = (current_height - new_height) / current_height if current_height > 0 else 0

            # 检查是否是B类或C类图片
            if bottom_image_class in ['B', 'C']:
                # 检查待处理图片中是否有能与当前底部图片形成完整B类图片组的图片
                if self._can_form_complete_b_group(bottom_image, remaining_patterns, packer.container_width):
                    log.info(f"底部单图片是{bottom_image_class}类，可以与待处理图片形成完整B类图片组，不移动")
                    return False

                # 检查剩余图片数量 - 如果是最后几张图片，优先完成排列
                if len(remaining_patterns) <= 3:
                    log.info(f"剩余图片数量很少 ({len(remaining_patterns)}张)，底部单图片是{bottom_image_class}类，优先完成排列")
                    return False

                # 检查底部图片宽度 - 如果宽度超过画布宽度的50%，考虑保留
                if bottom_image_width > packer.container_width * 0.5:
                    log.info(f"底部单图片宽度较大 ({bottom_image_width}px，占画布宽度的{bottom_image_width/packer.container_width:.2%})，考虑保留")

                    # 如果高度减少不显著，保留图片
                    if height_reduction <= 0.05:  # 高度减少不超过5%
                        log.info(f"底部单图片移除后高度减少不显著 ({height_reduction:.2%})，决定保留")
                        return False

            # 如果移除后高度减少显著，考虑移动
            if height_reduction > 0.03:  # 高度减少超过3%
                log.info(f"底部行只有一个图片，移除后高度减少 {height_reduction:.4f}，考虑移动")

                # 尝试在剩余空间中找到合适的图片填充
                if self._try_find_fitting_images(bottom_row_info, remaining_patterns, packer):
                    log.info("成功找到合适的图片填充底部空间，不需要移动底部图片")
                    return True

                # 从已放置图片列表中移除底部行图片
                packer.placed_images.remove(bottom_image)

                # 将底部行图片添加到剩余图片列表的开头，优先放置
                pattern = bottom_image.get('pattern', {})
                if pattern:
                    remaining_patterns.insert(0, pattern)

                # 更新当前最大高度
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0

                log.info("已将底部行图片移至下一画布")
                return True

        # 如果底部行利用率较低，考虑移动
        if bottom_row_utilization < 0.5:
            # 检查底部行图片数量
            if len(bottom_row_info['images']) <= 3:  # 底部行图片数量不多
                # 检查是否有B类图片组
                b_class_images = [img for img in bottom_row_info['images'] if self._get_image_class(img) == 'B']
                if b_class_images and len(b_class_images) > 1:
                    log.info(f"底部行包含多个B类图片 ({len(b_class_images)}个)，可能是B类图片组，考虑保留")
                    return False

                log.info(f"底部行利用率较低 ({bottom_row_utilization:.4f})，且图片数量不多 ({len(bottom_row_info['images'])})，考虑移动")

                # 尝试在剩余空间中找到合适的图片填充
                if self._try_find_fitting_images(bottom_row_info, remaining_patterns, packer):
                    log.info("成功找到合适的图片填充底部空间，不需要移动底部图片")
                    return True

                # 从已放置图片列表中移除底部行图片
                removed_images = []
                for img in bottom_row_info['images']:
                    if img in packer.placed_images:
                        packer.placed_images.remove(img)
                        pattern = img.get('pattern', {})
                        if pattern:
                            removed_images.append(pattern)

                # 更新当前最大高度
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0

                # 将底部行图片添加到剩余图片列表的开头，优先放置
                for pattern in removed_images:
                    remaining_patterns.insert(0, pattern)

                log.info(f"已将底部行 {len(removed_images)} 个图片移至下一画布")
                return True

        # 检查是否是最后一批图片
        if len(remaining_patterns) < 5:  # 剩余图片很少，可能是最后一批
            # 检查剩余图片是否能填充底部空间
            if self._try_find_fitting_images(bottom_row_info, remaining_patterns, packer):
                log.info("成功找到合适的图片填充底部空间，不需要移动底部图片")
                return True

            log.info(f"剩余图片很少 ({len(remaining_patterns)})，考虑移动底部行图片")

            # 从已放置图片列表中移除底部行图片
            removed_images = []
            for img in bottom_row_info['images']:
                if img in packer.placed_images:
                    packer.placed_images.remove(img)
                    pattern = img.get('pattern', {})
                    if pattern:
                        removed_images.append(pattern)

            # 更新当前最大高度
            packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0

            # 将底部行图片添加到剩余图片列表的开头，优先放置
            for pattern in removed_images:
                remaining_patterns.insert(0, pattern)

            log.info(f"已将底部行 {len(removed_images)} 个图片移至下一画布")
            return True

        log.info("底部行图片不适合移至下一画布")
        return False

    def _get_image_class(self, image: Dict[str, Any]) -> str:
        """
        获取图片的类别，统一处理图片类别信息获取
        增强版：更全面地检查图片类别信息，支持更多数据结构

        Args:
            image: 图片信息

        Returns:
            str: 图片类别，如'A', 'B', 'C'等，默认为'C'
        """
        try:
            # 如果输入为None或不是字典，直接返回默认值
            if not image or not isinstance(image, dict):
                return 'C'

            # 按优先级顺序尝试从不同位置获取图片类别
            # 1. 首先尝试从data字典中获取（最高优先级）
            if 'data' in image and isinstance(image['data'], dict):
                data = image['data']
                if 'image_class' in data and data['image_class']:
                    return data['image_class']
                elif 'category' in data and data['category']:
                    return data['category']
                elif 'original_pattern' in data and isinstance(data['original_pattern'], dict):
                    # 检查原始图案中的类别信息
                    original_pattern = data['original_pattern']
                    if 'image_class' in original_pattern and original_pattern['image_class']:
                        return original_pattern['image_class']
                    elif 'category' in original_pattern and original_pattern['category']:
                        return original_pattern['category']

            # 2. 尝试直接从图片对象获取
            if 'image_class' in image and image['image_class']:
                return image['image_class']
            elif 'category' in image and image['category']:
                return image['category']

            # 3. 尝试从pattern中获取
            if 'pattern' in image and isinstance(image['pattern'], dict):
                pattern = image['pattern']
                if 'image_class' in pattern and pattern['image_class']:
                    return pattern['image_class']
                elif 'category' in pattern and pattern['category']:
                    return pattern['category']
                elif 'data' in pattern and isinstance(pattern['data'], dict):
                    # 检查pattern的data字段
                    pattern_data = pattern['data']
                    if 'image_class' in pattern_data and pattern_data['image_class']:
                        return pattern_data['image_class']
                    elif 'category' in pattern_data and pattern_data['category']:
                        return pattern_data['category']

            # 4. 尝试从original_pattern中获取
            if 'original_pattern' in image and isinstance(image['original_pattern'], dict):
                original_pattern = image['original_pattern']
                if 'image_class' in original_pattern and original_pattern['image_class']:
                    return original_pattern['image_class']
                elif 'category' in original_pattern and original_pattern['category']:
                    return original_pattern['category']

            # 默认返回C类
            return 'C'
        except Exception as e:
            log.warning(f"获取图片类别失败: {str(e)}，默认返回'C'")
            return 'C'

    def _can_form_complete_b_group(self, bottom_image: Dict[str, Any], remaining_patterns: List[Dict[str, Any]], container_width: int) -> bool:
        """
        检查是否可以形成完整的B类图片组
        增强版2.0：更智能地判断B类图片组的完整性，考虑更多因素

        Args:
            bottom_image: 底部图片信息
            remaining_patterns: 剩余未放置的图片列表
            container_width: 容器宽度

        Returns:
            bool: 是否可以形成完整的B类图片组
        """
        try:
            # 获取底部图片类别
            bottom_image_class = self._get_image_class(bottom_image)

            # 如果不是B类图片，直接返回False
            if bottom_image_class != 'B':
                return False

            # 获取底部图片宽度
            bottom_image_width = bottom_image.get('width', 0)
            if bottom_image_width <= 0:
                log.warning("底部B类图片宽度无效")
                return False

            # 如果底部图片宽度超过容器宽度的80%，认为已经是完整的
            if bottom_image_width >= container_width * 0.8:
                log.info(f"底部B类图片宽度 ({bottom_image_width}px) 已接近容器宽度 ({container_width}px)，认为是完整的")
                return True

            # 计算一行可以放置的B类图片数量（考虑图片间距）
            image_spacing = 0  # 默认间距
            if 'spacing' in bottom_image:
                image_spacing = bottom_image['spacing']

            # 计算包含间距的总宽度
            width_with_spacing = bottom_image_width + image_spacing
            max_count = container_width // width_with_spacing if width_with_spacing > 0 else 1

            # 如果只能放置一个，则已经是完整的
            if max_count <= 1:
                return True

            # 在剩余图片中查找B类图片
            b_class_patterns = []

            # 获取底部图片的其他特征，用于匹配
            bottom_image_height = bottom_image.get('height', 0)
            bottom_image_name = ""
            if 'name' in bottom_image:
                bottom_image_name = bottom_image['name']
            elif 'pattern_name' in bottom_image:
                bottom_image_name = bottom_image['pattern_name']
            elif 'data' in bottom_image and isinstance(bottom_image['data'], dict):
                if 'name' in bottom_image['data']:
                    bottom_image_name = bottom_image['data']['name']
                elif 'pattern_name' in bottom_image['data']:
                    bottom_image_name = bottom_image['data']['pattern_name']

            # 使用统一的方法获取图片类别
            for pattern in remaining_patterns:
                if not isinstance(pattern, dict):
                    continue

                # 获取图片类别
                pattern_class = self._get_image_class(pattern)

                # 如果是B类图片，添加到列表
                if pattern_class == 'B':
                    # 获取图片宽度，使用更健壮的方式
                    pattern_width = 0
                    if 'width_px' in pattern:
                        pattern_width = pattern['width_px']
                    elif 'width' in pattern:
                        pattern_width = pattern['width']

                    # 如果宽度为0，尝试从其他属性获取
                    if pattern_width == 0 and 'data' in pattern and isinstance(pattern['data'], dict):
                        if 'width_px' in pattern['data']:
                            pattern_width = pattern['data']['width_px']
                        elif 'width' in pattern['data']:
                            pattern_width = pattern['data']['width']

                    # 如果宽度仍为0，跳过此图片
                    if pattern_width <= 0:
                        continue

                    # 获取图片高度，用于更精确的匹配
                    pattern_height = 0
                    if 'height_px' in pattern:
                        pattern_height = pattern['height_px']
                    elif 'height' in pattern:
                        pattern_height = pattern['height']

                    # 如果高度为0，尝试从其他属性获取
                    if pattern_height == 0 and 'data' in pattern and isinstance(pattern['data'], dict):
                        if 'height_px' in pattern['data']:
                            pattern_height = pattern['data']['height_px']
                        elif 'height' in pattern['data']:
                            pattern_height = pattern['data']['height']

                    # 获取图片名称，用于更精确的匹配
                    pattern_name = ""
                    if 'name' in pattern:
                        pattern_name = pattern['name']
                    elif 'pattern_name' in pattern:
                        pattern_name = pattern['pattern_name']
                    elif 'data' in pattern and isinstance(pattern['data'], dict):
                        if 'name' in pattern['data']:
                            pattern_name = pattern['data']['name']
                        elif 'pattern_name' in pattern['data']:
                            pattern_name = pattern['data']['pattern_name']

                    # 计算匹配分数
                    match_score = 0

                    # 1. 宽度匹配（最重要）
                    width_match = 1.0 - min(abs(pattern_width - bottom_image_width) / max(pattern_width, bottom_image_width), 1.0)
                    match_score += width_match * 0.6  # 60%权重

                    # 2. 高度匹配（次要）
                    if bottom_image_height > 0 and pattern_height > 0:
                        height_match = 1.0 - min(abs(pattern_height - bottom_image_height) / max(pattern_height, bottom_image_height), 1.0)
                        match_score += height_match * 0.3  # 30%权重

                    # 3. 名称匹配（辅助）
                    if bottom_image_name and pattern_name and bottom_image_name == pattern_name:
                        match_score += 0.1  # 10%权重

                    # 如果匹配分数超过阈值，认为是同一组的B类图片
                    if match_score >= 0.7:  # 70%匹配度
                        b_class_patterns.append({
                            'pattern': pattern,
                            'width': pattern_width,
                            'height': pattern_height,
                            'match_score': match_score
                        })

            # 检查是否有足够的B类图片形成完整的一组
            if len(b_class_patterns) >= max_count - 1:
                # 按匹配分数降序排序
                b_class_patterns.sort(key=lambda p: -p.get('match_score', 0))

                # 计算底部图片和待处理B类图片的总宽度
                total_width = bottom_image_width
                total_patterns = 1  # 包括底部图片

                # 考虑图片间距
                if image_spacing > 0:
                    total_width += image_spacing * (max_count - 1)  # 每个图片之间的间距

                # 添加最匹配的图片
                for i in range(min(max_count - 1, len(b_class_patterns))):
                    pattern_info = b_class_patterns[i]
                    pattern_width = pattern_info.get('width', 0)

                    # 如果宽度有效，添加到总宽度
                    if pattern_width > 0:
                        total_width += pattern_width
                        total_patterns += 1

                # 计算平均宽度
                avg_width = total_width / total_patterns if total_patterns > 0 else 0

                # 计算预计总宽度（如果所有图片都有相同宽度）
                estimated_total_width = avg_width * max_count
                if image_spacing > 0:
                    estimated_total_width += image_spacing * (max_count - 1)

                # 如果总宽度接近容器宽度，认为可以形成完整的B类图片组
                # 使用自适应阈值，根据图片数量调整
                threshold = 0.85  # 基础阈值
                if max_count > 3:
                    # 图片数量越多，阈值越低，增加容错性
                    threshold = max(0.80, 0.85 - (max_count - 3) * 0.01)

                if total_width >= container_width * threshold:
                    log.info(f"底部B类图片与待处理B类图片总宽度 ({total_width}px) 接近容器宽度 ({container_width}px)，可以形成完整的B类图片组")
                    return True

                # 如果预计总宽度接近容器宽度，也认为可以形成完整的B类图片组
                if estimated_total_width >= container_width * threshold:
                    log.info(f"底部B类图片与待处理B类图片预计总宽度 ({estimated_total_width}px) 接近容器宽度 ({container_width}px)，可以形成完整的B类图片组")
                    return True

                # 如果图片数量足够多，即使宽度不够，也可能形成完整的B类图片组
                if total_patterns >= max_count * 0.9:  # 至少达到所需数量的90%
                    log.info(f"底部B类图片与待处理B类图片数量 ({total_patterns}个) 接近所需数量 ({max_count}个)，可能形成完整的B类图片组")
                    return True

            return False
        except Exception as e:
            log.warning(f"检查是否可以形成完整B类图片组失败: {str(e)}")
            return False

    def _try_find_fitting_images(self, bottom_row_info: Dict[str, Any], remaining_patterns: List[Dict[str, Any]], packer) -> bool:
        """
        尝试在底部空间中找到合适的图片填充
        增强版2.0：更智能地查找合适的图片，考虑更多因素，提高填充效率

        Args:
            bottom_row_info: 底部行信息
            remaining_patterns: 剩余未放置的图片列表
            packer: Tetris算法实例

        Returns:
            bool: 是否成功找到并填充
        """
        try:
            # 如果剩余宽度太小，无法填充
            min_width_threshold = 20  # 最小宽度阈值
            if bottom_row_info['width_remaining'] < min_width_threshold:
                log.info(f"底部空间剩余宽度 ({bottom_row_info['width_remaining']}px) 小于最小阈值 ({min_width_threshold}px)，无法填充")
                return False

            # 找出可以放入底部空间的图片
            suitable_patterns = []

            # 计算底部空间的宽高比和面积
            bottom_space_width = bottom_row_info['width_remaining']
            bottom_space_height = bottom_row_info['height']
            bottom_space_ratio = bottom_space_width / bottom_space_height if bottom_space_height > 0 else 0
            bottom_space_area = bottom_space_width * bottom_space_height

            # 获取底部行中已有图片的类别分布
            bottom_class_distribution = {}
            for img in bottom_row_info.get('images', []):
                img_class = self._get_image_class(img)
                if img_class not in bottom_class_distribution:
                    bottom_class_distribution[img_class] = 0
                bottom_class_distribution[img_class] += 1

            # 确定主要类别（如果有）
            main_class = None
            max_count = 0
            for img_class, count in bottom_class_distribution.items():
                if count > max_count:
                    max_count = count
                    main_class = img_class

            # 遍历剩余图片，寻找合适的图片
            for pattern in remaining_patterns:
                if not isinstance(pattern, dict):
                    continue

                # 获取图片类别
                pattern_class = self._get_image_class(pattern)

                # 获取图片尺寸，使用更健壮的方式
                width = 0
                height = 0

                # 尝试从不同位置获取宽度和高度
                if 'width_px' in pattern:
                    width = pattern['width_px']
                elif 'width' in pattern:
                    width = pattern['width']

                if 'height_px' in pattern:
                    height = pattern['height_px']
                elif 'height' in pattern:
                    height = pattern['height']

                # 如果宽度或高度为0，尝试从data中获取
                if (width == 0 or height == 0) and 'data' in pattern and isinstance(pattern['data'], dict):
                    if 'width_px' in pattern['data']:
                        width = pattern['data']['width_px']
                    elif 'width' in pattern['data']:
                        width = pattern['data']['width']

                    if 'height_px' in pattern['data']:
                        height = pattern['data']['height_px']
                    elif 'height' in pattern['data']:
                        height = pattern['data']['height']

                # 如果宽度或高度仍为0，跳过此图片
                if width <= 0 or height <= 0:
                    continue

                # 计算图片的宽高比和面积
                pattern_ratio = width / height if height > 0 else 0
                pattern_area = width * height

                # 检查是否可以放入底部空间（不旋转）
                if width <= bottom_space_width and height <= bottom_space_height:
                    # 计算适应度分数 - 考虑多种因素

                    # 1. 宽度利用率（最重要）- 越接近剩余宽度越好
                    width_ratio = width / bottom_space_width

                    # 2. 形状匹配度 - 宽高比越接近底部空间越好
                    shape_match = 1.0 - min(abs(pattern_ratio - bottom_space_ratio) / max(pattern_ratio, bottom_space_ratio), 1.0)

                    # 3. 面积利用率 - 越接近底部空间面积越好
                    area_ratio = pattern_area / bottom_space_area
                    area_match = 1.0 - min(abs(1.0 - area_ratio), 1.0)

                    # 4. 类别匹配度 - 与底部行主要类别相同的图片优先
                    class_match = 0.0
                    if main_class and pattern_class == main_class:
                        class_match = 1.0

                    # 综合评分 - 权重可调整
                    fit_score = (
                        width_ratio * 0.5 +      # 50% 宽度利用率
                        shape_match * 0.2 +      # 20% 形状匹配度
                        area_match * 0.2 +       # 20% 面积利用率
                        class_match * 0.1        # 10% 类别匹配度
                    )

                    suitable_patterns.append({
                        'pattern': pattern,
                        'width': width,
                        'height': height,
                        'area': pattern_area,
                        'fit_score': fit_score,
                        'class': pattern_class,
                        'need_rotation': False
                    })

                # 检查旋转后是否可以放入底部空间
                elif height <= bottom_space_width and width <= bottom_space_height:
                    # 计算旋转后的宽高比
                    rotated_ratio = height / width if width > 0 else 0

                    # 计算适应度分数 - 考虑多种因素

                    # 1. 宽度利用率（最重要）- 越接近剩余宽度越好
                    width_ratio = height / bottom_space_width

                    # 2. 形状匹配度 - 宽高比越接近底部空间越好
                    shape_match = 1.0 - min(abs(rotated_ratio - bottom_space_ratio) / max(rotated_ratio, bottom_space_ratio), 1.0)

                    # 3. 面积利用率 - 越接近底部空间面积越好
                    area_ratio = pattern_area / bottom_space_area
                    area_match = 1.0 - min(abs(1.0 - area_ratio), 1.0)

                    # 4. 类别匹配度 - 与底部行主要类别相同的图片优先
                    class_match = 0.0
                    if main_class and pattern_class == main_class:
                        class_match = 1.0

                    # 5. 旋转惩罚 - 对旋转施加轻微惩罚，优先选择不需要旋转的图片
                    rotation_penalty = 0.05

                    # 综合评分 - 权重可调整
                    fit_score = (
                        width_ratio * 0.5 +      # 50% 宽度利用率
                        shape_match * 0.2 +      # 20% 形状匹配度
                        area_match * 0.2 +       # 20% 面积利用率
                        class_match * 0.1 -      # 10% 类别匹配度
                        rotation_penalty         # 5% 旋转惩罚
                    )

                    suitable_patterns.append({
                        'pattern': pattern,
                        'width': height,  # 旋转后的宽度
                        'height': width,  # 旋转后的高度
                        'area': pattern_area,
                        'fit_score': fit_score,
                        'class': pattern_class,
                        'need_rotation': True
                    })

            # 如果没有合适的图片，返回False
            if not suitable_patterns:
                log.info("未找到合适的图片填充底部空间")
                return False

            # 按适应度分数和面积降序排序
            suitable_patterns.sort(key=lambda p: (-p['fit_score'], -p['area']))

            # 记录最佳候选图片
            if suitable_patterns:
                best_pattern = suitable_patterns[0]
                log.info(f"最佳候选图片: 类别={best_pattern.get('class', 'C')}, "
                         f"尺寸={best_pattern['width']}x{best_pattern['height']}, "
                         f"适应度分数={best_pattern['fit_score']:.2f}, "
                         f"旋转={best_pattern.get('need_rotation', False)}")

            # 尝试放置图片
            placed_count = 0
            current_x = packer.container_width - bottom_row_info['width_remaining']

            # 获取图片间距
            image_spacing = 0
            if hasattr(packer, 'image_spacing'):
                image_spacing = packer.image_spacing

            # 尝试组合填充策略
            # 1. 先尝试找到一个最佳匹配的图片
            # 2. 如果找到，尝试在剩余空间中找到更多图片
            # 3. 如果找不到最佳匹配，尝试组合多个小图片填充

            # 策略1: 尝试放置最佳匹配的图片
            best_patterns = [p for p in suitable_patterns if p['fit_score'] >= 0.8]  # 适应度分数高的图片
            if best_patterns:
                pattern_info = best_patterns[0]
                pattern = pattern_info['pattern']
                width = pattern_info['width']
                height = pattern_info['height']
                need_rotation = pattern_info.get('need_rotation', False)
                image_class = pattern_info.get('class', self._get_image_class(pattern))

                try:
                    # 放置图片
                    packer._update_after_placement(
                        current_x, bottom_row_info['y'],
                        width, height,
                        width, height,
                        pattern, need_rotation
                    )

                    # 更新剩余宽度和当前x坐标
                    bottom_row_info['width_remaining'] -= width
                    if image_spacing > 0:
                        bottom_row_info['width_remaining'] -= image_spacing
                    current_x += width + image_spacing
                    placed_count += 1

                    # 从剩余图片列表和候选列表中移除已放置的图片
                    try:
                        remaining_patterns.remove(pattern)
                        suitable_patterns = [p for p in suitable_patterns if p['pattern'] != pattern]
                    except ValueError:
                        log.warning(f"无法从剩余图片列表中移除已放置的图片，可能已被移除")

                    log.info(f"成功在底部空间放置最佳匹配图片: 宽度={width}, 高度={height}, 类别={image_class}, "
                             f"旋转={need_rotation}, 适应度分数={pattern_info['fit_score']:.2f}")
                except Exception as e:
                    log.warning(f"放置最佳匹配图片时出错: {str(e)}")

            # 策略2: 尝试填充剩余空间
            # 如果还有剩余空间，继续尝试放置更多图片
            if bottom_row_info['width_remaining'] >= min_width_threshold:
                # 重新计算底部空间的宽高比
                bottom_space_width = bottom_row_info['width_remaining']
                bottom_space_ratio = bottom_space_width / bottom_space_height if bottom_space_height > 0 else 0

                # 重新评估剩余图片
                for pattern_info in suitable_patterns:
                    pattern = pattern_info['pattern']
                    width = pattern_info['width']
                    height = pattern_info['height']
                    need_rotation = pattern_info.get('need_rotation', False)

                    # 检查是否还有足够空间
                    if width > bottom_row_info['width_remaining']:
                        continue

                    # 获取图片类别
                    image_class = pattern_info.get('class', self._get_image_class(pattern))

                    try:
                        # 放置图片
                        packer._update_after_placement(
                            current_x, bottom_row_info['y'],
                            width, height,
                            width, height,
                            pattern, need_rotation
                        )

                        # 更新剩余宽度和当前x坐标
                        bottom_row_info['width_remaining'] -= width
                        if image_spacing > 0 and bottom_row_info['width_remaining'] > 0:
                            bottom_row_info['width_remaining'] -= image_spacing
                        current_x += width + image_spacing
                        placed_count += 1

                        # 从剩余图片列表中移除已放置的图片
                        try:
                            remaining_patterns.remove(pattern)
                        except ValueError:
                            log.warning(f"无法从剩余图片列表中移除已放置的图片，可能已被移除")

                        log.info(f"成功在底部空间放置额外图片: 宽度={width}, 高度={height}, 类别={image_class}, "
                                 f"旋转={need_rotation}, 适应度分数={pattern_info['fit_score']:.2f}")

                        # 如果剩余宽度太小，结束填充
                        if bottom_row_info['width_remaining'] < min_width_threshold:
                            break
                    except Exception as e:
                        log.warning(f"放置额外图片时出错: {str(e)}，尝试下一个图片")
                        continue

            # 记录填充结果
            if placed_count > 0:
                log.info(f"底部空间填充完成，共放置 {placed_count} 个图片，剩余宽度: {bottom_row_info['width_remaining']}px")
                return True
            else:
                log.info("未能在底部空间放置任何图片")
                return False
        except Exception as e:
            log.warning(f"尝试在底部空间中找到合适的图片填充失败: {str(e)}")
            return False
