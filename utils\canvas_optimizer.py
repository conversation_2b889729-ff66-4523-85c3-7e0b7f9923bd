#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
画布参数优化器模块

提供画布参数优化功能：
1. 多进程并行评估多组参数
2. 贝叶斯优化算法，减少参数搜索空间
3. 增量计算机制，避免重复计算
4. 性能监控和报告
"""

import os
import sys
import logging
import time
import concurrent.futures
import threading
import queue
import json
import random
import math
from typing import List, Dict, Any, Tuple, Optional, Callable, Union
import numpy as np
from datetime import datetime

# 导入自定义模块
from utils.parallel_manager import ParallelManager

# 尝试导入贝叶斯优化库
try:
    from skopt import Optimizer
    from skopt.space import Real, Integer
    from skopt.utils import use_named_args
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("CanvasOptimizer")

class CanvasOptimizer:
    """
    画布参数优化器，提供并行优化功能

    特性：
    1. 多进程并行评估多组参数
    2. 贝叶斯优化算法，减少参数搜索空间
    3. 增量计算机制，避免重复计算
    4. 性能监控和报告
    """

    def __init__(self, tetris_packer=None, image_classifier=None):
        """
        初始化画布参数优化器

        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
            image_classifier: 图像分类器实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        self.image_classifier = image_classifier

        # 创建并行管理器
        self.parallel_manager = ParallelManager()

        # 创建锁
        self._lock = threading.Lock()

        # 创建进程池
        self.process_pool = None

        # 参数评估结果缓存
        self.evaluation_cache = {}

        # 最佳参数组合
        self.best_params = None
        self.best_score = -float('inf')

        # 参数搜索历史
        self.search_history = []

        # 贝叶斯优化器
        self.bayesian_optimizer = None

        log.info("画布参数优化器初始化完成")

    def optimize_parameters(self, pattern_items, canvas_settings, iteration_params=None):
        """
        优化算法参数

        Args:
            pattern_items: 图案项目列表
            canvas_settings: 画布设置
            iteration_params: 迭代参数，包含canvas_iteration_count和canvas_iteration_time

        Returns:
            Dict: 最佳参数组合
        """

        # 获取迭代参数
        if iteration_params is None:
            iteration_params = {
                'canvas_iteration_count': 5,  # 每次迭代的参数组合数
                'canvas_iteration_time': 5    # 总迭代次数
            }

        canvas_iteration_count = iteration_params.get('canvas_iteration_count', 5)
        canvas_iteration_time = iteration_params.get('canvas_iteration_time', 5)

        log.info(f"开始参数优化，每次迭代 {canvas_iteration_count} 组参数，共 {canvas_iteration_time} 次迭代")

        # 定义参数空间
        param_space = {
            'horizontal_priority': (60, 95),
            'gap_filling_priority': (50, 90),
            'rotation_priority': (30, 80)
        }

        # 使用贝叶斯优化
        if SKOPT_AVAILABLE:
            log.info("使用贝叶斯优化算法")
            return self._bayesian_optimize(pattern_items, canvas_settings, param_space,
                                          canvas_iteration_count * canvas_iteration_time)
        else:
            log.info("贝叶斯优化库不可用，使用网格搜索")
            return self._grid_search(pattern_items, canvas_settings, param_space,
                                    canvas_iteration_count, canvas_iteration_time)

    def _bayesian_optimize(self, pattern_items, canvas_settings, param_space, n_calls):
        """
        使用贝叶斯优化算法优化参数

        Args:
            pattern_items: 图案项目列表
            canvas_settings: 画布设置
            param_space: 参数空间
            n_calls: 调用评估函数的次数

        Returns:
            Dict: 最佳参数组合
        """
        # 创建进程池
        if self.process_pool is None:
            self.process_pool = self.parallel_manager.create_process_pool(
                "canvas_optimizer", ParallelManager.TASK_TYPE_CPU)

        # 定义贝叶斯优化的参数空间
        dimensions = [
            Integer(param_space['horizontal_priority'][0], param_space['horizontal_priority'][1], name='horizontal_priority'),
            Integer(param_space['gap_filling_priority'][0], param_space['gap_filling_priority'][1], name='gap_filling_priority'),
            Integer(param_space['rotation_priority'][0], param_space['rotation_priority'][1], name='rotation_priority')
        ]

        # 创建贝叶斯优化器
        self.bayesian_optimizer = Optimizer(dimensions,
                                           base_estimator="GP",
                                           acq_func="EI",
                                           acq_optimizer="auto",
                                           random_state=42)

        # 定义目标函数
        @use_named_args(dimensions)
        def objective_function(horizontal_priority, gap_filling_priority, rotation_priority):
            params = {
                'horizontal_priority': horizontal_priority,
                'gap_filling_priority': gap_filling_priority,
                'rotation_priority': rotation_priority
            }

            # 检查缓存
            params_key = f"{horizontal_priority}_{gap_filling_priority}_{rotation_priority}"
            if params_key in self.evaluation_cache:
                log.info(f"使用缓存的评估结果: {params}")
                return -self.evaluation_cache[params_key]  # 贝叶斯优化是最小化问题，所以取负值

            # 评估参数
            score, result = self._evaluate_parameters(pattern_items, canvas_settings, params)

            # 更新缓存
            self.evaluation_cache[params_key] = score

            # 更新搜索历史
            self.search_history.append({
                'params': params,
                'score': score,
                'result': result,
                'timestamp': datetime.now().isoformat()
            })

            # 更新最佳参数
            if score > self.best_score:
                self.best_score = score
                self.best_params = params
                log.info(f"发现新的最佳参数: {params}, 得分: {score:.4f}")

            return -score  # 贝叶斯优化是最小化问题，所以取负值

        # 运行贝叶斯优化
        log.info(f"开始贝叶斯优化，将评估 {n_calls} 组参数")

        # 首先测试几个预设的参数组合
        preset_params = [
            {'horizontal_priority': 80, 'gap_filling_priority': 70, 'rotation_priority': 60},  # 默认参数
            {'horizontal_priority': 90, 'gap_filling_priority': 80, 'rotation_priority': 50},  # 高水平优先级
            {'horizontal_priority': 85, 'gap_filling_priority': 85, 'rotation_priority': 40}   # 高空隙填充优先级
        ]

        log.info("首先测试预设参数组合...")
        for params in preset_params:
            score, result = self._evaluate_parameters(pattern_items, canvas_settings, params)
            log.info(f"预设参数 {params} 得分: {score:.4f}")

            # 更新缓存
            params_key = f"{params['horizontal_priority']}_{params['gap_filling_priority']}_{params['rotation_priority']}"
            self.evaluation_cache[params_key] = score

            # 更新搜索历史
            self.search_history.append({
                'params': params,
                'score': score,
                'result': result,
                'timestamp': datetime.now().isoformat()
            })

            # 更新最佳参数
            if score > self.best_score:
                self.best_score = score
                self.best_params = params

        # 如果n_calls小于等于3，我们已经测试了预设参数，可以直接返回结果
        if n_calls <= 3:
            log.info("已完成预设参数测试，跳过贝叶斯优化")
            return self.best_params

        # 继续运行贝叶斯优化
        log.info(f"继续运行贝叶斯优化，剩余 {n_calls - 3} 次评估...")

        # 使用多进程并行评估
        remaining_calls = n_calls - 3
        batch_size = min(remaining_calls, self.parallel_manager.get_optimal_workers(ParallelManager.TASK_TYPE_CPU))

        for i in range(0, remaining_calls, batch_size):
            current_batch_size = min(batch_size, remaining_calls - i)
            log.info(f"批量评估 {current_batch_size} 组参数 ({i+1}-{i+current_batch_size}/{n_calls})...")

            # 获取下一批参数
            next_points = self.bayesian_optimizer.ask(n_points=current_batch_size)

            # 并行评估
            futures = []
            for point in next_points:
                hp, gp, rp = point
                params = {
                    'horizontal_priority': hp,
                    'gap_filling_priority': gp,
                    'rotation_priority': rp
                }
                futures.append(self.process_pool.submit(
                    self._evaluate_parameters, pattern_items, canvas_settings, params))

            # 收集结果
            results = []
            for future, point in zip(futures, next_points):
                score, result = future.result()
                results.append(-score)  # 贝叶斯优化是最小化问题，所以取负值

                # 更新缓存和历史
                hp, gp, rp = point
                params = {
                    'horizontal_priority': hp,
                    'gap_filling_priority': gp,
                    'rotation_priority': rp
                }
                params_key = f"{hp}_{gp}_{rp}"
                self.evaluation_cache[params_key] = score

                self.search_history.append({
                    'params': params,
                    'score': score,
                    'result': result,
                    'timestamp': datetime.now().isoformat()
                })

                # 更新最佳参数
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params
                    log.info(f"发现新的最佳参数: {params}, 得分: {score:.4f}")

            # 告诉优化器结果
            self.bayesian_optimizer.tell(next_points, results)

        log.info(f"贝叶斯优化完成，最佳参数: {self.best_params}, 得分: {self.best_score:.4f}")
        return self.best_params

    def _grid_search(self, pattern_items, canvas_settings, param_space, iteration_count, iteration_time):
        """
        使用网格搜索优化参数

        Args:
            pattern_items: 图案项目列表
            canvas_settings: 画布设置
            param_space: 参数空间
            iteration_count: 每次迭代的参数组合数
            iteration_time: 总迭代次数

        Returns:
            Dict: 最佳参数组合
        """
        # 创建进程池
        if self.process_pool is None:
            self.process_pool = self.parallel_manager.create_process_pool(
                "canvas_optimizer", ParallelManager.TASK_TYPE_CPU)

        # 计算总评估次数
        total_evaluations = iteration_count * iteration_time

        # 生成参数组合
        param_combinations = []
        for _ in range(total_evaluations):
            params = {
                'horizontal_priority': random.randint(param_space['horizontal_priority'][0],
                                                    param_space['horizontal_priority'][1]),
                'gap_filling_priority': random.randint(param_space['gap_filling_priority'][0],
                                                     param_space['gap_filling_priority'][1]),
                'rotation_priority': random.randint(param_space['rotation_priority'][0],
                                                  param_space['rotation_priority'][1])
            }
            param_combinations.append(params)

        # 添加一些预设的参数组合
        preset_params = [
            {'horizontal_priority': 80, 'gap_filling_priority': 70, 'rotation_priority': 60},  # 默认参数
            {'horizontal_priority': 90, 'gap_filling_priority': 80, 'rotation_priority': 50},  # 高水平优先级
            {'horizontal_priority': 85, 'gap_filling_priority': 85, 'rotation_priority': 40}   # 高空隙填充优先级
        ]

        for params in preset_params:
            if params not in param_combinations:
                param_combinations.append(params)

        # 分批评估参数
        log.info(f"开始网格搜索，将评估 {len(param_combinations)} 组参数")

        # 确定批次大小
        batch_size = min(iteration_count, self.parallel_manager.get_optimal_workers(ParallelManager.TASK_TYPE_CPU))

        for i in range(0, len(param_combinations), batch_size):
            current_batch = param_combinations[i:i+batch_size]
            log.info(f"批量评估 {len(current_batch)} 组参数 ({i+1}-{i+len(current_batch)}/{len(param_combinations)})...")

            # 并行评估
            futures = []
            for params in current_batch:
                # 检查缓存
                params_key = f"{params['horizontal_priority']}_{params['gap_filling_priority']}_{params['rotation_priority']}"
                if params_key in self.evaluation_cache:
                    log.info(f"使用缓存的评估结果: {params}")
                    continue

                futures.append(self.process_pool.submit(
                    self._evaluate_parameters, pattern_items, canvas_settings, params))

            # 收集结果
            for future, params in zip(futures, current_batch):
                score, result = future.result()

                # 更新缓存
                params_key = f"{params['horizontal_priority']}_{params['gap_filling_priority']}_{params['rotation_priority']}"
                self.evaluation_cache[params_key] = score

                # 更新搜索历史
                self.search_history.append({
                    'params': params,
                    'score': score,
                    'result': result,
                    'timestamp': datetime.now().isoformat()
                })

                # 更新最佳参数
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params
                    log.info(f"发现新的最佳参数: {params}, 得分: {score:.4f}")

        log.info(f"网格搜索完成，最佳参数: {self.best_params}, 得分: {self.best_score:.4f}")
        return self.best_params

    def _evaluate_parameters(self, pattern_items, canvas_settings, params):
        """
        评估参数组合

        Args:
            pattern_items: 图案项目列表
            canvas_settings: 画布设置
            params: 参数组合

        Returns:
            float: 评估得分
        """

        try:
            # 创建Tetris算法实例
            from core.tetris_packer import TetrisPacker

            # 获取画布设置
            canvas_width_px = canvas_settings.get('canvas_width_px', 1600)
            max_height_px = canvas_settings.get('max_height_px', 10000)
            image_spacing_px = canvas_settings.get('image_spacing_px', 10)

            # 创建Tetris算法实例
            tetris_packer = TetrisPacker(
                container_width=canvas_width_px,
                image_spacing=image_spacing_px,
                max_height=max_height_px
            )

            # 设置算法参数
            tetris_packer.horizontal_priority = params['horizontal_priority']
            tetris_packer.gap_filling_priority = params['gap_filling_priority']
            tetris_packer.rotation_priority = params['rotation_priority']

            # 放置图片
            placed_count = 0
            total_count = sum(item.get('quantity', 1) for item in pattern_items)
            placed_images = []

            for pattern in pattern_items:
                # 获取图片信息
                width_px = pattern.get('width_px', 100)
                height_px = pattern.get('height_px', 100)
                quantity = pattern.get('quantity', 1)

                # 放置图片
                for _ in range(quantity):
                    # 添加图片
                    x, y, success = tetris_packer.add_image(width_px, height_px, pattern)

                    if success:
                        placed_count += 1
                        # 记录已放置的图片信息，用于生成迭代卡片
                        placed_images.append({
                            'x': x,
                            'y': y,
                            'width': width_px,
                            'height': height_px,
                            'name': pattern.get('name', ''),
                            'image_class': pattern.get('image_class', 'C')
                        })

            # 计算评分
            # 1. 画布利用率 (50%)
            canvas_utilization = tetris_packer.get_utilization()

            # 2. 成功率 (30%)
            success_rate = placed_count / total_count if total_count > 0 else 0

            # 3. 画布高度 (20%) - 越低越好
            max_height = tetris_packer.get_max_height()
            height_score = 1.0 - (max_height / max_height_px) if max_height_px > 0 else 0

            # 计算综合得分
            score = (
                0.5 * canvas_utilization * 100 +  # 画布利用率 (0-100)
                0.3 * success_rate * 100 +        # 成功率 (0-100)
                0.2 * height_score * 100          # 高度得分 (0-100)
            )

            # 创建结果对象，包含评估信息和已放置的图片
            result = {
                'params': params,
                'score': score,
                'canvas_utilization': canvas_utilization * 100,  # 转换为百分比
                'success_rate': success_rate * 100,  # 转换为百分比
                'max_height': max_height,
                'placed_count': placed_count,
                'total_count': total_count,
                'placed_images': placed_images,
                'canvas_width': canvas_width_px,
                'timestamp': datetime.now().isoformat()
            }

            log.info(f"参数 {params} 评估完成，得分: {score:.2f}, "
                    f"利用率: {canvas_utilization*100:.2f}%, "
                    f"成功率: {success_rate*100:.2f}%, "
                    f"高度: {max_height}px")

            return score, result

        except Exception as e:
            log.error(f"评估参数失败: {str(e)}")
            return 0.0, None

    def generate_iteration_cards(self, pattern_items, canvas_settings, iteration_params=None):
        """
        生成迭代卡片

        Args:
            pattern_items: 图案项目列表
            canvas_settings: 画布设置
            iteration_params: 迭代参数，包含canvas_iteration_count和canvas_iteration_time

        Returns:
            List[Dict]: 迭代卡片列表，按利用率排序
        """

        # 获取迭代参数
        if iteration_params is None:
            iteration_params = {
                'canvas_iteration_count': 5,  # 每次迭代的参数组合数
                'canvas_iteration_time': 1    # 总迭代次数
            }

        canvas_iteration_count = iteration_params.get('canvas_iteration_count', 5)

        log.info(f"开始生成迭代卡片，每次迭代 {canvas_iteration_count} 组参数")

        # 定义参数空间
        param_space = {
            'horizontal_priority': (60, 95),
            'gap_filling_priority': (50, 90),
            'rotation_priority': (30, 80)
        }

        # 生成参数组合
        param_combinations = []
        for _ in range(canvas_iteration_count):
            params = {
                'horizontal_priority': random.randint(param_space['horizontal_priority'][0],
                                                    param_space['horizontal_priority'][1]),
                'gap_filling_priority': random.randint(param_space['gap_filling_priority'][0],
                                                     param_space['gap_filling_priority'][1]),
                'rotation_priority': random.randint(param_space['rotation_priority'][0],
                                                  param_space['rotation_priority'][1])
            }
            param_combinations.append(params)

        # 添加默认参数组合
        default_params = {'horizontal_priority': 80, 'gap_filling_priority': 70, 'rotation_priority': 60}
        if default_params not in param_combinations:
            param_combinations.append(default_params)

        # 创建进程池
        if self.process_pool is None:
            self.process_pool = self.parallel_manager.create_process_pool(
                "canvas_optimizer", ParallelManager.TASK_TYPE_CPU)

        # 并行评估参数
        log.info(f"并行评估 {len(param_combinations)} 组参数...")

        futures = []
        for params in param_combinations:
            futures.append(self.process_pool.submit(
                self._evaluate_parameters, pattern_items, canvas_settings, params))

        # 收集结果
        iteration_cards = []
        for future in concurrent.futures.as_completed(futures):
            _, result = future.result()
            if result:
                iteration_cards.append(result)

        # 按利用率排序
        iteration_cards.sort(key=lambda x: x['canvas_utilization'], reverse=True)

        log.info(f"生成了 {len(iteration_cards)} 张迭代卡片")
        return iteration_cards

    def visualize_iteration_card(self, card, output_path=None, show_grid=True):
        """
        可视化迭代卡片

        Args:
            card: 迭代卡片
            output_path: 输出文件路径，如果为None则显示图像
            show_grid: 是否显示网格

        Returns:
            PIL.Image: 生成的图像
        """
        try:
            # 导入PIL库
            from PIL import Image, ImageDraw, ImageFont

            # 获取卡片信息
            placed_images = card['placed_images']
            canvas_width = card['canvas_width']
            max_height = card['max_height']
            params = card['params']

            # 添加边距
            margin = 20
            canvas_width_with_margin = canvas_width + 2 * margin
            max_height_with_margin = max_height + 2 * margin + 100  # 额外空间用于显示参数信息

            # 创建图像
            image = Image.new('RGB', (canvas_width_with_margin, max_height_with_margin), color='white')
            draw = ImageDraw.Draw(image)

            # 绘制画布边界
            draw.rectangle([margin, margin, margin + canvas_width, margin + max_height],
                          outline='black', width=2)

            # 绘制网格
            if show_grid:
                grid_size = 50
                for x in range(margin, margin + canvas_width + 1, grid_size):
                    draw.line([(x, margin), (x, margin + max_height)], fill='lightgray', width=1)
                for y in range(margin, margin + max_height + 1, grid_size):
                    draw.line([(margin, y), (margin + canvas_width, y)], fill='lightgray', width=1)

            # 绘制已放置的图片
            for img in placed_images:
                x = img['x'] + margin
                y = img['y'] + margin
                width = img['width']
                height = img['height']
                image_class = img.get('image_class', 'C')

                # 根据图片类别选择颜色
                if image_class == 'A':
                    color = (255, 200, 200)  # 浅红色
                    outline = (255, 0, 0)    # 红色
                elif image_class == 'B':
                    color = (200, 200, 255)  # 浅蓝色
                    outline = (0, 0, 255)    # 蓝色
                else:  # C类
                    color = (200, 255, 200)  # 浅绿色
                    outline = (0, 128, 0)    # 绿色

                # 绘制矩形
                draw.rectangle([x, y, x + width, y + height],
                              fill=color, outline=outline, width=2)

                # 绘制图片名称
                name = img.get('name', '')
                if name:
                    # 截断过长的名称
                    if len(name) > 15:
                        name = name[:12] + '...'
                    draw.text((x + 5, y + 5), name, fill='black')

            # 绘制参数信息
            info_y = margin + max_height + 10
            draw.text((margin, info_y),
                     f"水平优先级: {params['horizontal_priority']}%, 空隙填充: {params['gap_filling_priority']}%, 旋转: {params['rotation_priority']}%",
                     fill='black')

            info_y += 20
            draw.text((margin, info_y),
                     f"利用率: {card['canvas_utilization']:.4f}%, 成功率: {card['success_rate']:.2f}%, 高度: {max_height}px",
                     fill='black')

            info_y += 20
            draw.text((margin, info_y),
                     f"放置: {card['placed_count']}/{card['total_count']} 图片, 得分: {card['score']:.2f}",
                     fill='black')

            # 保存或显示图像
            if output_path:
                image.save(output_path)
                log.info(f"迭代卡片已保存到: {output_path}")

            return image

        except Exception as e:
            log.error(f"可视化迭代卡片失败: {str(e)}")
            return None
