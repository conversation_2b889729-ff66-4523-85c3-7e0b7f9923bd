#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法改进测试脚本
用于验证RectPack算法的改进效果和生产环境稳定性

测试内容：
1. RectPack算法性能测试
2. Photoshop集成稳定性测试
3. 内存使用优化测试
4. 错误处理和恢复测试
5. 画布利用率测试
"""

import os
import sys
import time
import logging
import random
from typing import Dict, List, Any
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入测试模块
from managers.rectpack_manager import RectPackManager
from managers.photoshop_integration_manager import PhotoshopIntegrationManager
from managers.task_manager import TaskManager, TaskPriority
from utils.memory_manager import MemoryManager
from core.config_manager_duckdb import ConfigManagerDuckDB

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_rectpack_improvements.log'),
        logging.StreamHandler()
    ]
)
log = logging.getLogger(__name__)


class RectPackTestSuite:
    """RectPack算法测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        self.config_manager = ConfigManagerDuckDB()
        self.memory_manager = MemoryManager()
        self.rectpack_manager = RectPackManager(self.config_manager)
        self.photoshop_manager = PhotoshopIntegrationManager(self.config_manager)
        self.task_manager = TaskManager(self.config_manager)
        
        # 测试结果
        self.test_results = {}
        
        log.info("RectPack测试套件初始化完成")
    
    def generate_test_images(self, count: int = 50) -> List[Dict[str, Any]]:
        """生成测试图片数据
        
        Args:
            count: 图片数量
            
        Returns:
            List[Dict[str, Any]]: 测试图片列表
        """
        test_images = []
        
        for i in range(count):
            # 生成随机尺寸的图片
            width = random.randint(100, 800)
            height = random.randint(100, 600)
            
            image_info = {
                'name': f'test_image_{i+1:03d}',
                'width': width,
                'height': height,
                'path': f'test_images/test_image_{i+1:03d}.jpg',
                'size_bytes': width * height * 3,  # 假设RGB图片
                'format': 'JPEG'
            }
            
            test_images.append(image_info)
        
        log.info(f"生成了 {count} 个测试图片")
        return test_images
    
    def test_rectpack_algorithm_performance(self) -> Dict[str, Any]:
        """测试RectPack算法性能"""
        log.info("开始RectPack算法性能测试...")
        
        test_cases = [
            {'image_count': 10, 'canvas_width': 2.0},
            {'image_count': 50, 'canvas_width': 2.0},
            {'image_count': 100, 'canvas_width': 3.0},
            {'image_count': 200, 'canvas_width': 4.0}
        ]
        
        results = []
        
        for case in test_cases:
            log.info(f"测试用例: {case['image_count']} 张图片, 画布宽度 {case['canvas_width']}m")
            
            # 生成测试图片
            test_images = self.generate_test_images(case['image_count'])
            
            # 初始化算法
            start_time = time.time()
            
            success = self.rectpack_manager.initialize_algorithm(
                canvas_width_m=case['canvas_width'],
                max_height_cm=5000,
                image_spacing_cm=0.1,
                ppi=72
            )
            
            if not success:
                log.error(f"算法初始化失败: {case}")
                continue
            
            # 处理图片
            arranged_images, process_success = self.rectpack_manager.process_images(test_images)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 获取性能统计
            stats = self.rectpack_manager.get_performance_stats()
            
            result = {
                'image_count': case['image_count'],
                'canvas_width': case['canvas_width'],
                'processing_time': processing_time,
                'success': process_success,
                'arranged_count': len(arranged_images),
                'utilization': stats.get('utilization', 0),
                'images_per_second': stats.get('images_per_second', 0)
            }
            
            results.append(result)
            log.info(f"测试结果: {result}")
        
        self.test_results['rectpack_performance'] = results
        log.info("RectPack算法性能测试完成")
        return results
    
    def test_photoshop_integration_stability(self) -> Dict[str, Any]:
        """测试Photoshop集成稳定性"""
        log.info("开始Photoshop集成稳定性测试...")
        
        # 测试连接初始化
        connection_tests = []
        for i in range(5):
            start_time = time.time()
            success = self.photoshop_manager.initialize_connection(test_mode=True)
            end_time = time.time()
            
            connection_tests.append({
                'attempt': i + 1,
                'success': success,
                'time': end_time - start_time
            })
        
        # 测试画布创建
        canvas_tests = []
        if any(test['success'] for test in connection_tests):
            for i in range(3):
                start_time = time.time()
                success = self.photoshop_manager.create_canvas(
                    width=2000,
                    height=3000,
                    name=f"test_canvas_{i+1}",
                    ppi=72
                )
                end_time = time.time()
                
                canvas_tests.append({
                    'canvas': i + 1,
                    'success': success,
                    'time': end_time - start_time
                })
        
        # 测试批量图片放置
        batch_tests = []
        test_images = self.generate_test_images(20)
        
        start_time = time.time()
        successful_count, failed_count = self.photoshop_manager.place_images_batch(test_images)
        end_time = time.time()
        
        batch_tests.append({
            'total_images': len(test_images),
            'successful': successful_count,
            'failed': failed_count,
            'time': end_time - start_time
        })
        
        result = {
            'connection_tests': connection_tests,
            'canvas_tests': canvas_tests,
            'batch_tests': batch_tests,
            'performance_stats': self.photoshop_manager.get_performance_stats()
        }
        
        self.test_results['photoshop_stability'] = result
        log.info("Photoshop集成稳定性测试完成")
        return result
    
    def test_memory_optimization(self) -> Dict[str, Any]:
        """测试内存使用优化"""
        log.info("开始内存优化测试...")
        
        # 记录初始内存状态
        initial_memory = self.memory_manager.get_memory_info()
        
        # 生成大量测试数据
        large_test_images = self.generate_test_images(500)
        
        # 记录处理前内存状态
        before_processing = self.memory_manager.get_memory_info()
        
        # 处理大量图片
        start_time = time.time()
        
        self.rectpack_manager.initialize_algorithm(
            canvas_width_m=3.0,
            max_height_cm=5000,
            image_spacing_cm=0.1,
            ppi=72
        )
        
        arranged_images, success = self.rectpack_manager.process_images(large_test_images)
        
        end_time = time.time()
        
        # 记录处理后内存状态
        after_processing = self.memory_manager.get_memory_info()
        
        # 执行内存清理
        self.memory_manager.cleanup_memory()
        
        # 记录清理后内存状态
        after_cleanup = self.memory_manager.get_memory_info()
        
        result = {
            'initial_memory': initial_memory,
            'before_processing': before_processing,
            'after_processing': after_processing,
            'after_cleanup': after_cleanup,
            'processing_time': end_time - start_time,
            'images_processed': len(arranged_images),
            'memory_increase': after_processing['process_rss'] - before_processing['process_rss'],
            'memory_recovered': after_processing['process_rss'] - after_cleanup['process_rss']
        }
        
        self.test_results['memory_optimization'] = result
        log.info("内存优化测试完成")
        return result
    
    def test_task_manager_workflow(self) -> Dict[str, Any]:
        """测试任务管理器工作流程"""
        log.info("开始任务管理器工作流程测试...")
        
        # 创建多个测试任务
        task_ids = []
        
        for i in range(3):
            test_images = self.generate_test_images(30 + i * 10)
            canvas_config = {
                'canvas_width_m': 2.0 + i * 0.5,
                'max_height_cm': 5000,
                'image_spacing_cm': 0.1,
                'ppi': 72,
                'output_dir': 'test_output'
            }
            
            task_id = self.task_manager.add_task(
                material_name=f"test_material_{i+1}",
                sheet_name=f"test_sheet_{i+1}",
                pattern_items=test_images,
                canvas_config=canvas_config,
                priority=TaskPriority.NORMAL if i < 2 else TaskPriority.HIGH
            )
            
            task_ids.append(task_id)
        
        # 等待任务完成
        start_time = time.time()
        max_wait_time = 300  # 最多等待5分钟
        
        while time.time() - start_time < max_wait_time:
            # 检查所有任务状态
            all_completed = True
            for task_id in task_ids:
                status = self.task_manager.get_task_status(task_id)
                if status and status['status'] not in ['completed', 'failed']:
                    all_completed = False
                    break
            
            if all_completed:
                break
            
            time.sleep(1)
        
        # 收集任务结果
        task_results = []
        for task_id in task_ids:
            status = self.task_manager.get_task_status(task_id)
            if status:
                task_results.append(status)
        
        # 获取性能摘要
        performance_summary = self.task_manager.get_performance_summary()
        
        result = {
            'task_results': task_results,
            'performance_summary': performance_summary,
            'total_time': time.time() - start_time
        }
        
        self.test_results['task_manager_workflow'] = result
        log.info("任务管理器工作流程测试完成")
        return result
    
    def test_error_handling_and_recovery(self) -> Dict[str, Any]:
        """测试错误处理和恢复机制"""
        log.info("开始错误处理和恢复测试...")
        
        error_tests = []
        
        # 测试1: 无效画布尺寸
        try:
            success = self.rectpack_manager.initialize_algorithm(
                canvas_width_m=-1,  # 无效值
                max_height_cm=5000,
                image_spacing_cm=0.1,
                ppi=72
            )
            error_tests.append({
                'test': 'invalid_canvas_size',
                'success': success,
                'expected_failure': True,
                'result': 'pass' if not success else 'fail'
            })
        except Exception as e:
            error_tests.append({
                'test': 'invalid_canvas_size',
                'success': False,
                'expected_failure': True,
                'result': 'pass',
                'error': str(e)
            })
        
        # 测试2: 空图片列表
        try:
            arranged_images, success = self.rectpack_manager.process_images([])
            error_tests.append({
                'test': 'empty_image_list',
                'success': success,
                'expected_failure': True,
                'result': 'pass' if not success else 'fail'
            })
        except Exception as e:
            error_tests.append({
                'test': 'empty_image_list',
                'success': False,
                'expected_failure': True,
                'result': 'pass',
                'error': str(e)
            })
        
        # 测试3: 内存不足模拟
        try:
            # 生成超大图片列表
            huge_test_images = self.generate_test_images(10000)
            
            self.rectpack_manager.initialize_algorithm(
                canvas_width_m=2.0,
                max_height_cm=5000,
                image_spacing_cm=0.1,
                ppi=72
            )
            
            arranged_images, success = self.rectpack_manager.process_images(huge_test_images)
            error_tests.append({
                'test': 'memory_stress',
                'success': success,
                'expected_failure': False,
                'result': 'pass' if success else 'fail',
                'processed_count': len(arranged_images)
            })
        except Exception as e:
            error_tests.append({
                'test': 'memory_stress',
                'success': False,
                'expected_failure': False,
                'result': 'fail',
                'error': str(e)
            })
        
        result = {
            'error_tests': error_tests,
            'total_tests': len(error_tests),
            'passed_tests': len([t for t in error_tests if t['result'] == 'pass'])
        }
        
        self.test_results['error_handling'] = result
        log.info("错误处理和恢复测试完成")
        return result
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        log.info("开始运行完整测试套件...")
        
        start_time = time.time()
        
        # 运行各项测试
        self.test_rectpack_algorithm_performance()
        self.test_photoshop_integration_stability()
        self.test_memory_optimization()
        self.test_task_manager_workflow()
        self.test_error_handling_and_recovery()
        
        end_time = time.time()
        
        # 生成测试报告
        test_summary = {
            'total_time': end_time - start_time,
            'test_results': self.test_results,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        log.info(f"完整测试套件运行完成，耗时 {end_time - start_time:.2f} 秒")
        return test_summary
    
    def cleanup(self):
        """清理测试资源"""
        try:
            self.rectpack_manager.cleanup()
            self.photoshop_manager.cleanup()
            self.task_manager.cleanup()
            self.memory_manager.cleanup_memory()
            log.info("测试资源清理完成")
        except Exception as e:
            log.error(f"清理测试资源失败: {str(e)}")


def main():
    """主函数"""
    # 创建QApplication实例（某些组件需要）
    app = QApplication(sys.argv)
    
    # 创建测试套件
    test_suite = RectPackTestSuite()
    
    try:
        # 运行测试
        results = test_suite.run_all_tests()
        
        # 输出测试结果摘要
        print("\n" + "="*60)
        print("RectPack算法改进测试结果摘要")
        print("="*60)
        print(f"测试时间: {results['timestamp']}")
        print(f"总耗时: {results['total_time']:.2f} 秒")
        print()
        
        for test_name, test_result in results['test_results'].items():
            print(f"{test_name}: 完成")
        
        print("\n详细结果已保存到 test_rectpack_improvements.log")
        
    except Exception as e:
        log.error(f"测试运行失败: {str(e)}")
        
    finally:
        # 清理资源
        test_suite.cleanup()
        app.quit()


if __name__ == "__main__":
    main()
