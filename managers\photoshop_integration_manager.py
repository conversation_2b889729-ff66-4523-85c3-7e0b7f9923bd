#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Photoshop集成管理器
负责管理与Adobe Photoshop的集成，确保在生产环境下的稳定性和可靠性

主要职责：
1. 管理Photoshop连接状态和健康检查
2. 优化图片放置的精确性和效率
3. 处理Photoshop操作中的错误和异常
4. 提供批量操作和事务管理
5. 监控内存使用和性能优化
"""

import logging
import time
import os
from typing import Dict, List, Any, Optional, Tuple
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

# 导入Photoshop相关模块
from utils.photoshop_helper import PhotoshopHelper
from utils.image_processor import PhotoshopImageProcessor
from utils.memory_manager import MemoryManager

# 配置日志
log = logging.getLogger(__name__)


class PhotoshopIntegrationManager(QObject):
    """Photoshop集成管理器
    
    负责管理与Photoshop的所有交互，包括：
    - 连接管理和健康检查
    - 图片放置和画布操作
    - 错误处理和恢复机制
    - 性能监控和优化
    """
    
    # 信号定义
    connection_status_changed = pyqtSignal(bool, str)  # 连接状态变化信号
    operation_progress = pyqtSignal(int, str)          # 操作进度信号
    error_occurred = pyqtSignal(str, str)              # 错误发生信号 (错误类型, 错误消息)
    performance_updated = pyqtSignal(dict)             # 性能数据更新信号
    
    def __init__(self, config_manager=None):
        """初始化Photoshop集成管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        super().__init__()
        self.config_manager = config_manager
        self.memory_manager = MemoryManager()
        
        # Photoshop相关
        self.ps_helper = PhotoshopHelper
        self.image_processor = None
        self.is_connected = False
        self.last_health_check = 0
        self.health_check_interval = 30  # 30秒检查一次
        
        # 性能监控
        self.operation_start_time = None
        self.total_operations = 0
        self.successful_operations = 0
        self.failed_operations = 0
        self.average_operation_time = 0.0
        
        # 错误处理
        self.error_count = 0
        self.max_errors = 5
        self.retry_attempts = {}
        self.max_retries = 3
        
        # 批量操作管理
        self.batch_size = 10  # 每批处理的图片数量
        self.current_batch = []
        self.pending_operations = []
        
        # 设置健康检查定时器
        self.health_check_timer = QTimer()
        self.health_check_timer.timeout.connect(self.perform_health_check)
        self.health_check_timer.start(self.health_check_interval * 1000)
        
        log.info("Photoshop集成管理器初始化完成")
    
    def initialize_connection(self, test_mode: bool = False) -> bool:
        """初始化Photoshop连接
        
        Args:
            test_mode: 是否为测试模式
            
        Returns:
            bool: 是否连接成功
        """
        try:
            if test_mode:
                log.info("测试模式：跳过Photoshop连接检查")
                self.is_connected = True
                self.connection_status_changed.emit(True, "测试模式：模拟连接成功")
                return True
            
            # 检查Photoshop是否运行
            is_running, message = self.ps_helper.check_photoshop()
            
            if not is_running:
                # 尝试启动Photoshop
                log.info("Photoshop未运行，尝试启动...")
                start_success, start_message = self.ps_helper.start_photoshop()
                
                if not start_success:
                    error_msg = f"无法启动Photoshop: {start_message}"
                    log.error(error_msg)
                    self.error_occurred.emit("CONNECTION_ERROR", error_msg)
                    self.is_connected = False
                    self.connection_status_changed.emit(False, error_msg)
                    return False
                
                # 等待Photoshop启动
                time.sleep(5)
                is_running, message = self.ps_helper.check_photoshop()
            
            if is_running:
                # 初始化图片处理器
                self.image_processor = PhotoshopImageProcessor()
                init_success = self.image_processor.initialize({})
                
                if init_success:
                    self.is_connected = True
                    self.last_health_check = time.time()
                    log.info("Photoshop连接成功")
                    self.connection_status_changed.emit(True, "Photoshop连接成功")
                    return True
                else:
                    error_msg = "Photoshop图片处理器初始化失败"
                    log.error(error_msg)
                    self.error_occurred.emit("PROCESSOR_ERROR", error_msg)
                    self.is_connected = False
                    self.connection_status_changed.emit(False, error_msg)
                    return False
            else:
                error_msg = f"Photoshop连接失败: {message}"
                log.error(error_msg)
                self.error_occurred.emit("CONNECTION_ERROR", error_msg)
                self.is_connected = False
                self.connection_status_changed.emit(False, error_msg)
                return False
                
        except Exception as e:
            error_msg = f"初始化Photoshop连接时发生异常: {str(e)}"
            log.error(error_msg)
            self.error_occurred.emit("EXCEPTION", error_msg)
            self.is_connected = False
            self.connection_status_changed.emit(False, error_msg)
            return False
    
    def perform_health_check(self) -> bool:
        """执行健康检查
        
        Returns:
            bool: 健康状态
        """
        try:
            current_time = time.time()
            
            # 检查是否需要进行健康检查
            if current_time - self.last_health_check < self.health_check_interval:
                return self.is_connected
            
            # 执行健康检查
            if self.is_connected:
                is_running, message = self.ps_helper.check_photoshop()
                
                if not is_running:
                    log.warning(f"Photoshop健康检查失败: {message}")
                    self.is_connected = False
                    self.connection_status_changed.emit(False, f"连接丢失: {message}")
                    
                    # 尝试重新连接
                    self._attempt_reconnection()
                else:
                    log.debug("Photoshop健康检查通过")
            
            self.last_health_check = current_time
            return self.is_connected
            
        except Exception as e:
            log.error(f"健康检查时发生异常: {str(e)}")
            return False
    
    def _attempt_reconnection(self) -> bool:
        """尝试重新连接Photoshop
        
        Returns:
            bool: 是否重连成功
        """
        try:
            log.info("尝试重新连接Photoshop...")
            
            # 清理现有连接
            if self.image_processor:
                self.image_processor.cleanup()
                self.image_processor = None
            
            # 尝试重新初始化连接
            return self.initialize_connection()
            
        except Exception as e:
            log.error(f"重新连接失败: {str(e)}")
            return False
    
    def create_canvas(self, width: int, height: int, name: str, ppi: int = 72) -> bool:
        """创建Photoshop画布
        
        Args:
            width: 画布宽度（像素）
            height: 画布高度（像素）
            name: 画布名称
            ppi: 分辨率
            
        Returns:
            bool: 是否创建成功
        """
        try:
            if not self.is_connected or not self.image_processor:
                if not self.initialize_connection():
                    return False
            
            self.operation_start_time = time.time()
            self.operation_progress.emit(0, f"创建画布: {name}")
            
            # 检查内存使用情况
            self.memory_manager.check_memory_usage()
            
            # 创建画布
            success = self.image_processor.create_canvas(width, height, name, ppi)
            
            # 更新统计信息
            self._update_operation_stats(success)
            
            if success:
                log.info(f"画布创建成功: {name} ({width}x{height}px)")
                self.operation_progress.emit(100, f"画布创建成功: {name}")
            else:
                error_msg = f"画布创建失败: {name}"
                log.error(error_msg)
                self.error_occurred.emit("CANVAS_ERROR", error_msg)
            
            return success
            
        except Exception as e:
            error_msg = f"创建画布时发生异常: {str(e)}"
            log.error(error_msg)
            self.error_occurred.emit("EXCEPTION", error_msg)
            self._update_operation_stats(False)
            return False
    
    def place_images_batch(self, images_info: List[Dict[str, Any]]) -> Tuple[int, int]:
        """批量放置图片
        
        Args:
            images_info: 图片信息列表
            
        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        try:
            if not self.is_connected or not self.image_processor:
                if not self.initialize_connection():
                    return 0, len(images_info)
            
            total_images = len(images_info)
            successful_count = 0
            failed_count = 0
            
            log.info(f"开始批量放置 {total_images} 张图片")
            self.operation_progress.emit(0, f"开始放置 {total_images} 张图片")
            
            # 分批处理图片
            for i in range(0, total_images, self.batch_size):
                batch = images_info[i:i + self.batch_size]
                batch_success, batch_failed = self._process_image_batch(batch, i, total_images)
                
                successful_count += batch_success
                failed_count += batch_failed
                
                # 更新进度
                progress = int((i + len(batch)) / total_images * 100)
                self.operation_progress.emit(progress, f"已处理 {i + len(batch)}/{total_images} 张图片")
                
                # 检查内存使用情况
                if i % (self.batch_size * 3) == 0:  # 每3批检查一次
                    self.memory_manager.check_memory_usage()
            
            log.info(f"批量放置完成: 成功 {successful_count}, 失败 {failed_count}")
            self.operation_progress.emit(100, f"放置完成: 成功 {successful_count}, 失败 {failed_count}")
            
            return successful_count, failed_count
            
        except Exception as e:
            error_msg = f"批量放置图片时发生异常: {str(e)}"
            log.error(error_msg)
            self.error_occurred.emit("EXCEPTION", error_msg)
            return 0, len(images_info)
    
    def _process_image_batch(self, batch: List[Dict[str, Any]], start_index: int, total_count: int) -> Tuple[int, int]:
        """处理单个图片批次
        
        Args:
            batch: 图片批次
            start_index: 起始索引
            total_count: 总数量
            
        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        successful_count = 0
        failed_count = 0
        
        for i, image_info in enumerate(batch):
            try:
                self.operation_start_time = time.time()
                
                # 放置图片
                success = self.image_processor.place_image(image_info)
                
                # 更新统计信息
                self._update_operation_stats(success)
                
                if success:
                    successful_count += 1
                    log.debug(f"图片放置成功: {image_info.get('name', 'Unknown')}")
                else:
                    failed_count += 1
                    image_name = image_info.get('name', 'Unknown')
                    log.warning(f"图片放置失败: {image_name}")
                    
                    # 记录失败的操作以便重试
                    operation_id = f"{start_index + i}_{image_name}"
                    if operation_id not in self.retry_attempts:
                        self.retry_attempts[operation_id] = 0
                    
                    if self.retry_attempts[operation_id] < self.max_retries:
                        self.retry_attempts[operation_id] += 1
                        log.info(f"准备重试图片放置: {image_name} (第{self.retry_attempts[operation_id]}次)")
                        # 可以在这里实现重试逻辑
                
            except Exception as e:
                failed_count += 1
                image_name = image_info.get('name', 'Unknown')
                log.error(f"处理图片 {image_name} 时发生异常: {str(e)}")
                self._update_operation_stats(False)
        
        return successful_count, failed_count
    
    def save_and_close_canvas(self, output_path: str) -> bool:
        """保存并关闭画布
        
        Args:
            output_path: 输出路径
            
        Returns:
            bool: 是否成功
        """
        try:
            if not self.is_connected or not self.image_processor:
                return False
            
            self.operation_progress.emit(0, "正在保存画布...")
            
            # 保存画布
            save_success = self.image_processor.save_canvas(output_path)
            
            if save_success:
                self.operation_progress.emit(50, "画布保存成功，正在关闭...")
                
                # 关闭画布以节省内存
                close_success = self.image_processor.close_canvas()
                
                if close_success:
                    self.operation_progress.emit(100, "画布保存并关闭成功")
                    log.info(f"画布保存并关闭成功: {output_path}")
                    return True
                else:
                    log.warning("画布保存成功但关闭失败")
                    return True  # 保存成功就算成功
            else:
                error_msg = f"画布保存失败: {output_path}"
                log.error(error_msg)
                self.error_occurred.emit("SAVE_ERROR", error_msg)
                return False
                
        except Exception as e:
            error_msg = f"保存并关闭画布时发生异常: {str(e)}"
            log.error(error_msg)
            self.error_occurred.emit("EXCEPTION", error_msg)
            return False
    
    def _update_operation_stats(self, success: bool):
        """更新操作统计信息
        
        Args:
            success: 操作是否成功
        """
        self.total_operations += 1
        
        if success:
            self.successful_operations += 1
        else:
            self.failed_operations += 1
            self.error_count += 1
        
        # 计算平均操作时间
        if self.operation_start_time:
            operation_time = time.time() - self.operation_start_time
            self.average_operation_time = (
                (self.average_operation_time * (self.total_operations - 1) + operation_time) 
                / self.total_operations
            )
        
        # 发送性能更新信号
        performance_data = {
            'total_operations': self.total_operations,
            'successful_operations': self.successful_operations,
            'failed_operations': self.failed_operations,
            'success_rate': self.successful_operations / self.total_operations if self.total_operations > 0 else 0,
            'average_operation_time': self.average_operation_time,
            'error_count': self.error_count
        }
        self.performance_updated.emit(performance_data)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息
        
        Returns:
            Dict[str, Any]: 性能统计数据
        """
        return {
            'is_connected': self.is_connected,
            'total_operations': self.total_operations,
            'successful_operations': self.successful_operations,
            'failed_operations': self.failed_operations,
            'success_rate': self.successful_operations / self.total_operations if self.total_operations > 0 else 0,
            'average_operation_time': self.average_operation_time,
            'error_count': self.error_count,
            'last_health_check': self.last_health_check
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            # 停止健康检查定时器
            if self.health_check_timer:
                self.health_check_timer.stop()
            
            # 清理图片处理器
            if self.image_processor:
                self.image_processor.cleanup()
                self.image_processor = None
            
            # 清理内存管理器
            self.memory_manager.cleanup()
            
            self.is_connected = False
            log.info("Photoshop集成管理器资源清理完成")
            
        except Exception as e:
            log.error(f"清理Photoshop集成管理器资源失败: {str(e)}")
