#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试俄罗斯方块式装箱算法
"""

import os
import sys
import time
import random
import logging
import argparse
from typing import List, Dict, Any, Tuple
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.tetris_packer import TetrisPacker

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("TestTetrisPacker")

def generate_test_images(count: int, min_size: int = 10, max_size: int = 100) -> List[Dict[str, Any]]:
    """
    生成测试图片数据
    
    Args:
        count: 图片数量
        min_size: 最小尺寸（像素）
        max_size: 最大尺寸（像素）
        
    Returns:
        List[Dict[str, Any]]: 图片数据列表
    """
    images = []
    for i in range(count):
        width = random.randint(min_size, max_size)
        height = random.randint(min_size, max_size)
        
        # 随机生成一些极端宽高比的图片
        if random.random() < 0.2:  # 20%的概率生成极端宽高比
            if random.random() < 0.5:  # 一半是宽大于高
                width = random.randint(max_size // 2, max_size)
                height = random.randint(min_size, min_size * 2)
            else:  # 一半是高大于宽
                width = random.randint(min_size, min_size * 2)
                height = random.randint(max_size // 2, max_size)
        
        images.append({
            'width': width,
            'height': height,
            'name': f'image_{i+1}',
            'color': (random.random(), random.random(), random.random())  # 随机颜色
        })
    
    return images

def visualize_layout(packer: TetrisPacker, canvas_width: int, output_path: str = None):
    """
    可视化布局结果
    
    Args:
        packer: 俄罗斯方块式装箱器
        canvas_width: 画布宽度
        output_path: 输出路径，如果为None则显示图像
    """
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 设置坐标轴
    ax.set_xlim(0, canvas_width)
    max_height = packer.max_height
    ax.set_ylim(0, max_height + 50)
    
    # 绘制画布边界
    ax.add_patch(
        patches.Rectangle(
            (0, 0),
            canvas_width,
            max_height,
            fill=False,
            edgecolor='black',
            linewidth=2
        )
    )
    
    # 绘制已放置的图片
    for img in packer.placed_images:
        x = img['x']
        y = img['y']
        width = img['width']
        height = img['height']
        
        # 获取图片颜色，如果没有则随机生成
        color = img.get('color', (random.random(), random.random(), random.random()))
        
        # 绘制矩形
        rect = patches.Rectangle(
            (x, y),
            width,
            height,
            fill=True,
            facecolor=color,
            edgecolor='black',
            linewidth=1,
            alpha=0.7
        )
        ax.add_patch(rect)
        
        # 添加图片名称
        name = img.get('name', f"{width}x{height}")
        ax.text(
            x + width / 2,
            y + height / 2,
            name,
            ha='center',
            va='center',
            fontsize=8,
            color='black'
        )
    
    # 添加标题和信息
    utilization = packer.get_utilization() * 100
    ax.set_title(f'俄罗斯方块式布局 - 利用率: {utilization:.2f}%')
    
    # 添加网格
    ax.grid(True, linestyle='--', alpha=0.3)
    
    # 保存或显示图像
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        log.info(f"布局可视化已保存到: {output_path}")
    else:
        plt.show()
    
    plt.close()

def test_tetris_packer(
    canvas_width: int = 1600,
    image_count: int = 50,
    min_size: int = 50,
    max_size: int = 300,
    image_spacing: int = 5,
    support_threshold: float = 0.3,
    enable_physics: bool = True,
    horizontal_priority: int = 80,
    gap_filling_priority: int = 70,
    rotation_priority: int = 60,
    drop_step_size: int = 5,
    output_path: str = None,
    visualize: bool = True
):
    """
    测试俄罗斯方块式装箱算法
    
    Args:
        canvas_width: 画布宽度（像素）
        image_count: 图片数量
        min_size: 最小图片尺寸（像素）
        max_size: 最大图片尺寸（像素）
        image_spacing: 图片间距（像素）
        support_threshold: 支撑阈值（0-1之间）
        enable_physics: 是否启用物理碰撞模拟
        horizontal_priority: 水平优先级（百分比）
        gap_filling_priority: 空隙填充优先级（百分比）
        rotation_priority: 旋转优先级（百分比）
        drop_step_size: 下落步长（像素）
        output_path: 输出路径，如果为None则显示图像
        visualize: 是否可视化布局结果
    """
    log.info(f"测试参数:")
    log.info(f"  - 画布宽度: {canvas_width}像素")
    log.info(f"  - 图片数量: {image_count}个")
    log.info(f"  - 图片尺寸范围: {min_size}-{max_size}像素")
    log.info(f"  - 图片间距: {image_spacing}像素")
    log.info(f"  - 支撑阈值: {support_threshold}")
    log.info(f"  - 启用物理碰撞: {'是' if enable_physics else '否'}")
    log.info(f"  - 水平优先级: {horizontal_priority}%")
    log.info(f"  - 空隙填充优先级: {gap_filling_priority}%")
    log.info(f"  - 旋转优先级: {rotation_priority}%")
    log.info(f"  - 下落步长: {drop_step_size}像素")
    
    # 创建俄罗斯方块式装箱器
    packer = TetrisPacker(
        container_width=canvas_width,
        image_spacing=image_spacing
    )
    
    # 设置算法参数
    packer.support_threshold = support_threshold
    packer.enable_physics = enable_physics
    packer.horizontal_priority = horizontal_priority
    packer.gap_filling_priority = gap_filling_priority
    packer.rotation_priority = rotation_priority
    packer.drop_step_size = drop_step_size
    
    # 生成测试图片
    images = generate_test_images(image_count, min_size, max_size)
    log.info(f"生成了 {len(images)} 个测试图片")
    
    # 按面积降序排序
    images.sort(key=lambda img: -(img['width'] * img['height']))
    
    # 放置图片
    start_time = time.time()
    successful_count = 0
    
    for i, img in enumerate(images):
        width = img['width']
        height = img['height']
        
        # 添加算法参数到图片数据
        img_data = {
            'c_support_threshold': int(support_threshold * 100),
            'c_enable_physics': enable_physics,
            'c_horizontal_priority': horizontal_priority,
            'c_gap_filling_priority': gap_filling_priority,
            'c_rotation_priority': rotation_priority,
            'c_drop_step_size': drop_step_size,
            'name': img['name'],
            'color': img['color']
        }
        
        # 尝试放置图片
        x, y, success = packer.find_position(width, height, img_data)
        
        if success:
            successful_count += 1
            log.info(f"成功放置图片 {img['name']} ({width}x{height}) 在 ({x}, {y})")
        else:
            log.warning(f"无法放置图片 {img['name']} ({width}x{height})")
    
    # 尝试重新分配图片
    log.info("尝试重新分配图片，优化整体布局...")
    redistribution_success = packer.redistribute_images()
    if redistribution_success:
        log.info("重新分配图片成功")
    else:
        log.info("重新分配图片失败或无需重新分配")
    
    # 计算性能
    elapsed_time = time.time() - start_time
    images_per_second = successful_count / elapsed_time if elapsed_time > 0 else 0
    
    # 计算利用率
    utilization = packer.get_utilization() * 100
    
    # 输出结果
    log.info(f"布局完成:")
    log.info(f"  - 成功放置: {successful_count}/{len(images)} 个图片")
    log.info(f"  - 利用率: {utilization:.2f}%")
    log.info(f"  - 最大高度: {packer.max_height}像素")
    log.info(f"  - 处理时间: {elapsed_time:.2f}秒")
    log.info(f"  - 处理速度: {images_per_second:.2f}图片/秒")
    
    # 输出性能统计
    log.info(f"性能统计:")
    log.info(f"  - 图片放置次数: {packer.placement_count}")
    log.info(f"  - 图片放置总时间: {packer.placement_time:.2f}秒")
    log.info(f"  - 空隙填充次数: {packer.gap_fill_count}")
    log.info(f"  - 空隙填充总时间: {packer.gap_fill_time:.2f}秒")
    log.info(f"  - 下落模拟次数: {packer.drop_simulation_count}")
    log.info(f"  - 下落模拟总时间: {packer.drop_simulation_time:.2f}秒")
    
    # 可视化布局结果
    if visualize:
        visualize_layout(packer, canvas_width, output_path)
    
    return packer, successful_count, utilization

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试俄罗斯方块式装箱算法')
    parser.add_argument('--width', type=int, default=1600, help='画布宽度（像素）')
    parser.add_argument('--count', type=int, default=50, help='图片数量')
    parser.add_argument('--min-size', type=int, default=50, help='最小图片尺寸（像素）')
    parser.add_argument('--max-size', type=int, default=300, help='最大图片尺寸（像素）')
    parser.add_argument('--spacing', type=int, default=5, help='图片间距（像素）')
    parser.add_argument('--support', type=float, default=0.3, help='支撑阈值（0-1之间）')
    parser.add_argument('--physics', action='store_true', help='启用物理碰撞模拟')
    parser.add_argument('--no-physics', action='store_true', help='禁用物理碰撞模拟')
    parser.add_argument('--horizontal', type=int, default=80, help='水平优先级（百分比）')
    parser.add_argument('--gap', type=int, default=70, help='空隙填充优先级（百分比）')
    parser.add_argument('--rotation', type=int, default=60, help='旋转优先级（百分比）')
    parser.add_argument('--step', type=int, default=5, help='下落步长（像素）')
    parser.add_argument('--output', type=str, help='输出路径')
    parser.add_argument('--no-visualize', action='store_true', help='不可视化布局结果')
    
    args = parser.parse_args()
    
    # 处理物理碰撞模拟参数
    enable_physics = True
    if args.physics:
        enable_physics = True
    elif args.no_physics:
        enable_physics = False
    
    # 运行测试
    test_tetris_packer(
        canvas_width=args.width,
        image_count=args.count,
        min_size=args.min_size,
        max_size=args.max_size,
        image_spacing=args.spacing,
        support_threshold=args.support,
        enable_physics=enable_physics,
        horizontal_priority=args.horizontal,
        gap_filling_priority=args.gap,
        rotation_priority=args.rotation,
        drop_step_size=args.step,
        output_path=args.output,
        visualize=not args.no_visualize
    )

if __name__ == '__main__':
    main()
